{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/%28auth%29/sign-in/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nexport default function SignInRedirect() {\n  const router = useRouter();\n\n  useEffect(() => {\n    // Immediate redirect to the new login page\n    router.replace('/login');\n  }, [router]);\n\n  // Also use a meta refresh as backup\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      window.location.href = '/login';\n    }, 100);\n    return () => clearTimeout(timer);\n  }, []);\n\n  return (\n    <>\n      <meta httpEquiv=\"refresh\" content=\"0;url=/login\" />\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Redirecting to login...</p>\n          <p className=\"text-sm text-gray-500 mt-2\">\n            If you're not redirected automatically, <a href=\"/login\" className=\"text-blue-600 hover:underline\">click here</a>\n          </p>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,2CAA2C;YAC3C,OAAO,OAAO,CAAC;QACjB;mCAAG;QAAC;KAAO;IAEX,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,QAAQ;kDAAW;oBACvB,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACzB;iDAAG;YACH;4CAAO,IAAM,aAAa;;QAC5B;mCAAG,EAAE;IAEL,qBACE;;0BACE,6LAAC;gBAAK,WAAU;gBAAU,SAAQ;;;;;;0BAClC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAC7B,6LAAC;4BAAE,WAAU;;gCAA6B;8CACA,6LAAC;oCAAE,MAAK;oCAAS,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;AAM/G;GA9BwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}