import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import User from '@/models/User';
import Patient from '@/models/Patient';
import Doctor from '@/models/Doctor';
import { ApiResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const { action } = await request.json();

    if (action === 'clear-all') {
      // Clear all collections
      await User.deleteMany({});
      await Patient.deleteMany({});
      await Doctor.deleteMany({});

      return NextResponse.json<ApiResponse>({
        success: true,
        message: 'All data cleared successfully',
      });
    }

    if (action === 'test-patient-registration') {
      // Test patient registration with new structure
      const user = new User({
        email: '<EMAIL>',
        password: 'password123',
        role: 'patient',
        isEmailVerified: true,
      });

      await user.save();

      const patient = new Patient({
        userId: user._id,
        firstName: 'Test',
        lastName: 'Patient',
        phone: '555-1234',
      });

      await patient.save();

      return NextResponse.json<ApiResponse>({
        success: true,
        data: {
          user: {
            id: user._id,
            email: user.email,
            role: user.role,
          },
          patient: {
            id: patient._id,
            firstName: patient.firstName,
            lastName: patient.lastName,
            phone: patient.phone,
          }
        },
        message: 'Test patient created successfully',
      });
    }

    if (action === 'test-doctor-registration') {
      // Test doctor registration with new structure
      const user = new User({
        email: '<EMAIL>',
        password: 'password123',
        role: 'doctor',
        isEmailVerified: true,
      });

      await user.save();

      const doctor = new Doctor({
        userId: user._id,
        specialty: 'General Practice',
        bio: 'Test doctor bio',
        experience: 5,
        consultationFee: 100,
        location: {
          address: '123 Test St',
          city: 'Test City',
          state: 'Test State',
          zipCode: '12345'
        },
        availability: [],
      });

      await doctor.save();

      return NextResponse.json<ApiResponse>({
        success: true,
        data: {
          user: {
            id: user._id,
            email: user.email,
            role: user.role,
          },
          doctor: {
            id: doctor._id,
            specialty: doctor.specialty,
            bio: doctor.bio,
            experience: doctor.experience,
          }
        },
        message: 'Test doctor created successfully',
      });
    }

    if (action === 'check-collections') {
      const userCount = await User.countDocuments();
      const patientCount = await Patient.countDocuments();
      const doctorCount = await Doctor.countDocuments();

      return NextResponse.json<ApiResponse>({
        success: true,
        data: {
          users: userCount,
          patients: patientCount,
          doctors: doctorCount,
        },
        message: 'Collection counts retrieved',
      });
    }

    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Invalid action',
    }, { status: 400 });

  } catch (error) {
    console.error('Migration test error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Internal server error',
    }, { status: 500 });
  }
}
