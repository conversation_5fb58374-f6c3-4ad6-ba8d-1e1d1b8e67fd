{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/logout/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/components/providers/AuthProvider';\n\nexport default function LogoutPage() {\n  const router = useRouter();\n  const { logout } = useAuth();\n\n  useEffect(() => {\n    const performLogout = async () => {\n      try {\n        await logout();\n        // Clear localStorage as well\n        localStorage.removeItem('authToken');\n        // Redirect to home page\n        router.push('/');\n      } catch (error) {\n        console.error('Logout error:', error);\n        // Force redirect anyway\n        localStorage.removeItem('authToken');\n        router.push('/');\n      }\n    };\n\n    performLogout();\n  }, [logout, router]);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n        <p className=\"text-gray-600\">Logging out...</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;IAEzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;sDAAgB;oBACpB,IAAI;wBACF,MAAM;wBACN,6BAA6B;wBAC7B,aAAa,UAAU,CAAC;wBACxB,wBAAwB;wBACxB,OAAO,IAAI,CAAC;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iBAAiB;wBAC/B,wBAAwB;wBACxB,aAAa,UAAU,CAAC;wBACxB,OAAO,IAAI,CAAC;oBACd;gBACF;;YAEA;QACF;+BAAG;QAAC;QAAQ;KAAO;IAEnB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAIrC;GA/BwB;;QACP,qIAAA,CAAA,YAAS;QACL,kJAAA,CAAA,UAAO;;;KAFJ", "debugId": null}}]}