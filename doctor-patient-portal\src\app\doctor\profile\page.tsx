'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/providers/AuthProvider';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Loader2, Save, User, MapPin, DollarSign, Stethoscope } from 'lucide-react';

interface DoctorProfile {
  _id: string;
  specialty: string;
  bio: string;
  experience: number;
  consultationFee: number;
  location: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
  };
  qualifications: string[];
  isVerified: boolean;
  rating: number;
  totalRatings: number;
}

interface UserProfile {
  firstName: string;
  lastName: string;
  phone: string;
}

const specialties = [
  'General Practice',
  'Cardiology',
  'Dermatology',
  'Endocrinology',
  'Gastroenterology',
  'Neurology',
  'Oncology',
  'Orthopedics',
  'Pediatrics',
  'Psychiatry',
  'Radiology',
  'Surgery',
  'Urology',
  'Other'
];

export default function DoctorProfilePage() {
  const [doctorProfile, setDoctorProfile] = useState<DoctorProfile | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile>({
    firstName: '',
    lastName: '',
    phone: ''
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const router = useRouter();
  const { user, isLoading: authLoading, isAuthenticated } = useAuth();

  useEffect(() => {
    if (authLoading) return;

    if (!isAuthenticated || !user) {
      router.push('/login');
      return;
    }

    if (user.role !== 'doctor') {
      router.push('/dashboard');
      return;
    }

    fetchDoctorProfile();
  }, [user, isAuthenticated, authLoading, router]);

  const fetchDoctorProfile = async () => {
    try {
      const response = await fetch('/api/doctors/me');
      const result = await response.json();

      if (result.success) {
        setDoctorProfile(result.data);
        // Set user profile data
        if (result.data.user) {
          setUserProfile({
            firstName: result.data.user.profile?.firstName || user?.firstName || '',
            lastName: result.data.user.profile?.lastName || user?.lastName || '',
            phone: result.data.user.profile?.phone || user?.phone || ''
          });
        }
      } else {
        toast.error('Failed to load profile');
      }
    } catch (error) {
      console.error('Error fetching doctor profile:', error);
      toast.error('Failed to load profile');
    } finally {
      setLoading(false);
    }
  };

  const handleDoctorProfileChange = (field: keyof DoctorProfile, value: any) => {
    if (!doctorProfile) return;
    
    setDoctorProfile({
      ...doctorProfile,
      [field]: value
    });
  };

  const handleLocationChange = (field: keyof DoctorProfile['location'], value: string) => {
    if (!doctorProfile) return;
    
    setDoctorProfile({
      ...doctorProfile,
      location: {
        ...doctorProfile.location,
        [field]: value
      }
    });
  };

  const handleUserProfileChange = (field: keyof UserProfile, value: string) => {
    setUserProfile({
      ...userProfile,
      [field]: value
    });
  };

  const handleSave = async () => {
    if (!doctorProfile) return;

    setSaving(true);
    try {
      // Update doctor profile
      const doctorResponse = await fetch('/api/doctors/me', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
        },
        body: JSON.stringify({
          specialty: doctorProfile.specialty,
          bio: doctorProfile.bio,
          experience: doctorProfile.experience,
          consultationFee: doctorProfile.consultationFee,
          location: doctorProfile.location,
          qualifications: doctorProfile.qualifications
        }),
      });

      const doctorResult = await doctorResponse.json();

      if (doctorResult.success) {
        toast.success('Profile updated successfully');
        // Update local state with the returned data
        setDoctorProfile(doctorResult.data);
      } else {
        toast.error(doctorResult.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!doctorProfile) {
    return (
      <DashboardLayout title="Profile" subtitle="Manage your professional information">
        <div className="text-center py-12">
          <p className="text-gray-600">No profile found. Please contact support.</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Profile" subtitle="Manage your professional information">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="w-5 h-5 mr-2" />
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  value={userProfile.firstName}
                  onChange={(e) => handleUserProfileChange('firstName', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={userProfile.lastName}
                  onChange={(e) => handleUserProfileChange('lastName', e.target.value)}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                value={userProfile.phone}
                onChange={(e) => handleUserProfileChange('phone', e.target.value)}
                placeholder="+****************"
              />
            </div>
          </CardContent>
        </Card>

        {/* Professional Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Stethoscope className="w-5 h-5 mr-2" />
              Professional Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="specialty">Specialty</Label>
                <Select
                  value={doctorProfile.specialty}
                  onValueChange={(value) => handleDoctorProfileChange('specialty', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select specialty" />
                  </SelectTrigger>
                  <SelectContent>
                    {specialties.map((specialty) => (
                      <SelectItem key={specialty} value={specialty}>
                        {specialty}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="experience">Years of Experience</Label>
                <Input
                  id="experience"
                  type="number"
                  min="0"
                  value={doctorProfile.experience}
                  onChange={(e) => handleDoctorProfileChange('experience', parseInt(e.target.value) || 0)}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="consultationFee">Consultation Fee ($)</Label>
              <Input
                id="consultationFee"
                type="number"
                min="0"
                step="0.01"
                value={doctorProfile.consultationFee}
                onChange={(e) => handleDoctorProfileChange('consultationFee', parseFloat(e.target.value) || 0)}
              />
            </div>
            <div>
              <Label htmlFor="bio">Bio</Label>
              <Textarea
                id="bio"
                rows={4}
                value={doctorProfile.bio}
                onChange={(e) => handleDoctorProfileChange('bio', e.target.value)}
                placeholder="Tell patients about yourself and your experience..."
              />
            </div>
          </CardContent>
        </Card>

        {/* Location Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MapPin className="w-5 h-5 mr-2" />
              Practice Location
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                value={doctorProfile.location.address}
                onChange={(e) => handleLocationChange('address', e.target.value)}
                placeholder="123 Main Street"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={doctorProfile.location.city}
                  onChange={(e) => handleLocationChange('city', e.target.value)}
                  placeholder="New York"
                />
              </div>
              <div>
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  value={doctorProfile.location.state}
                  onChange={(e) => handleLocationChange('state', e.target.value)}
                  placeholder="NY"
                />
              </div>
              <div>
                <Label htmlFor="zipCode">ZIP Code</Label>
                <Input
                  id="zipCode"
                  value={doctorProfile.location.zipCode}
                  onChange={(e) => handleLocationChange('zipCode', e.target.value)}
                  placeholder="10001"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button onClick={handleSave} disabled={saving} size="lg">
            {saving ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>
    </DashboardLayout>
  );
}
