{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 537, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 599, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/dashboard/DoctorCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { MapPin, Star, DollarSign, Clock, User } from 'lucide-react';\nimport { DoctorWithUser } from '@/types';\nimport { useAuthStore } from '@/lib/store';\n\ninterface DoctorCardProps {\n  doctor: Doctor<PERSON>ithUser;\n  showBookButton?: boolean;\n}\n\nexport default function DoctorCard({ doctor, showBookButton = true }: DoctorCardProps) {\n  const [isHovered, setIsHovered] = useState(false);\n  const router = useRouter();\n  const { isAuthenticated, user } = useAuthStore();\n\n  // Safe access to user profile data with fallbacks\n  const userProfile = doctor.user?.profile;\n  const firstName = userProfile?.firstName || 'Unknown';\n  const lastName = userProfile?.lastName || 'Doctor';\n  const profileImage = doctor.profileImage || userProfile?.profileImage;\n\n  const handleBookAppointment = () => {\n    if (!isAuthenticated) {\n      router.push('/login');\n      return;\n    }\n\n    if (user?.role !== 'patient') {\n      return;\n    }\n\n    router.push(`/patient/book/${doctor._id}`);\n  };\n\n  const handleViewProfile = () => {\n    router.push(`/doctors/${doctor._id}`);\n  };\n\n  const getInitials = (firstName: string, lastName: string) => {\n    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();\n  };\n\n  // Early return if doctor data is incomplete\n  if (!doctor || !doctor.user) {\n    return (\n      <Card className=\"h-full\">\n        <CardContent className=\"p-6\">\n          <div className=\"text-center text-gray-500\">\n            <p>Doctor information unavailable</p>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const renderStars = (rating: number) => {\n    return Array.from({ length: 5 }, (_, index) => (\n      <Star\n        key={index}\n        className={`w-4 h-4 ${\n          index < Math.floor(rating)\n            ? 'text-yellow-400 fill-current'\n            : 'text-gray-300'\n        }`}\n      />\n    ));\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n      whileHover={{ y: -5 }}\n      onHoverStart={() => setIsHovered(true)}\n      onHoverEnd={() => setIsHovered(false)}\n    >\n      <Card className=\"h-full transition-all duration-300 hover:shadow-lg border-0 shadow-md\">\n        <CardHeader className=\"pb-4\">\n          <div className=\"flex items-start space-x-4\">\n            <Avatar className=\"w-16 h-16\">\n              <AvatarImage\n                src={profileImage}\n                alt={`Dr. ${firstName} ${lastName}`}\n              />\n              <AvatarFallback className=\"bg-blue-100 text-blue-600 text-lg font-semibold\">\n                {getInitials(firstName, lastName)}\n              </AvatarFallback>\n            </Avatar>\n\n            <div className=\"flex-1 min-w-0\">\n              <h3 className=\"text-lg font-semibold text-gray-900 truncate\">\n                Dr. {firstName} {lastName}\n              </h3>\n              \n              <Badge variant=\"secondary\" className=\"mt-1 bg-blue-50 text-blue-700 hover:bg-blue-100\">\n                {doctor.specialty}\n              </Badge>\n              \n              <div className=\"flex items-center mt-2 space-x-1\">\n                {renderStars(doctor.rating)}\n                <span className=\"text-sm text-gray-600 ml-2\">\n                  {doctor.rating.toFixed(1)} ({doctor.totalRatings} reviews)\n                </span>\n              </div>\n            </div>\n          </div>\n        </CardHeader>\n\n        <CardContent className=\"pb-4\">\n          <p className=\"text-gray-600 text-sm line-clamp-3 mb-4\">\n            {doctor.bio}\n          </p>\n\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <Clock className=\"w-4 h-4 mr-2 text-gray-400\" />\n              <span>{doctor.experience} years experience</span>\n            </div>\n\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <MapPin className=\"w-4 h-4 mr-2 text-gray-400\" />\n              <span className=\"truncate\">\n                {doctor.location.city}, {doctor.location.state}\n              </span>\n            </div>\n\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <DollarSign className=\"w-4 h-4 mr-2 text-gray-400\" />\n              <span>${doctor.consultationFee} consultation fee</span>\n            </div>\n          </div>\n        </CardContent>\n\n        <CardFooter className=\"pt-0 space-x-2\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={handleViewProfile}\n            className=\"flex-1\"\n          >\n            <User className=\"w-4 h-4 mr-2\" />\n            View Profile\n          </Button>\n\n          {showBookButton && (\n            <motion.div\n              className=\"flex-1\"\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              <Button\n                onClick={handleBookAppointment}\n                size=\"sm\"\n                className=\"w-full bg-blue-600 hover:bg-blue-700 text-white\"\n                disabled={!isAuthenticated || user?.role !== 'patient'}\n              >\n                Book Appointment\n              </Button>\n            </motion.div>\n          )}\n        </CardFooter>\n      </Card>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AAXA;;;;;;;;;;AAkBe,SAAS,WAAW,EAAE,MAAM,EAAE,iBAAiB,IAAI,EAAmB;;IACnF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;IAE7C,kDAAkD;IAClD,MAAM,cAAc,OAAO,IAAI,EAAE;IACjC,MAAM,YAAY,aAAa,aAAa;IAC5C,MAAM,WAAW,aAAa,YAAY;IAC1C,MAAM,eAAe,OAAO,YAAY,IAAI,aAAa;IAEzD,MAAM,wBAAwB;QAC5B,IAAI,CAAC,iBAAiB;YACpB,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,MAAM,SAAS,WAAW;YAC5B;QACF;QAEA,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,OAAO,GAAG,EAAE;IAC3C;IAEA,MAAM,oBAAoB;QACxB,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,OAAO,GAAG,EAAE;IACtC;IAEA,MAAM,cAAc,CAAC,WAAmB;QACtC,OAAO,GAAG,UAAU,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,IAAI,CAAC,WAAW;IAClE;IAEA,4CAA4C;IAC5C,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,EAAE;QAC3B,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,sBACnC,6LAAC,qMAAA,CAAA,OAAI;gBAEH,WAAW,CAAC,QAAQ,EAClB,QAAQ,KAAK,KAAK,CAAC,UACf,iCACA,iBACJ;eALG;;;;;IAQX;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,cAAc,IAAM,aAAa;QACjC,YAAY,IAAM,aAAa;kBAE/B,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,6LAAC,qIAAA,CAAA,cAAW;wCACV,KAAK;wCACL,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,UAAU;;;;;;kDAErC,6LAAC,qIAAA,CAAA,iBAAc;wCAAC,WAAU;kDACvB,YAAY,WAAW;;;;;;;;;;;;0CAI5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CAA+C;4CACtD;4CAAU;4CAAE;;;;;;;kDAGnB,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAClC,OAAO,SAAS;;;;;;kDAGnB,6LAAC;wCAAI,WAAU;;4CACZ,YAAY,OAAO,MAAM;0DAC1B,6LAAC;gDAAK,WAAU;;oDACb,OAAO,MAAM,CAAC,OAAO,CAAC;oDAAG;oDAAG,OAAO,YAAY;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO3D,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAE,WAAU;sCACV,OAAO,GAAG;;;;;;sCAGb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;;gDAAM,OAAO,UAAU;gDAAC;;;;;;;;;;;;;8CAG3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;;gDACb,OAAO,QAAQ,CAAC,IAAI;gDAAC;gDAAG,OAAO,QAAQ,CAAC,KAAK;;;;;;;;;;;;;8CAIlD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;;gDAAK;gDAAE,OAAO,eAAe;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;;8BAKrC,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;wBAIlC,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;sCAExB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,MAAK;gCACL,WAAU;gCACV,UAAU,CAAC,mBAAmB,MAAM,SAAS;0CAC9C;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA3JwB;;QAEP,qIAAA,CAAA,YAAS;QACU,sHAAA,CAAA,eAAY;;;KAHxB", "debugId": null}}, {"offset": {"line": 1001, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/hooks/useClientOnly.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\n\n/**\n * Custom hook to handle client-side only state to prevent hydration mismatches\n * Returns true only after the component has mounted on the client side\n */\nexport function useClientOnly() {\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  return isClient;\n}\n\n/**\n * Custom hook to handle responsive breakpoints in a SSR-safe way\n */\nexport function useResponsive() {\n  const [screenSize, setScreenSize] = useState({\n    isSmall: false,\n    isMedium: false,\n    isLarge: false,\n    isXLarge: false,\n  });\n  const isClient = useClientOnly();\n\n  useEffect(() => {\n    if (!isClient) return;\n\n    const updateScreenSize = () => {\n      const width = window.innerWidth;\n      setScreenSize({\n        isSmall: width >= 640,\n        isMedium: width >= 768,\n        isLarge: width >= 1024,\n        isXLarge: width >= 1280,\n      });\n    };\n\n    updateScreenSize();\n    window.addEventListener('resize', updateScreenSize);\n\n    return () => window.removeEventListener('resize', updateScreenSize);\n  }, [isClient]);\n\n  return { ...screenSize, isClient };\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAMO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,YAAY;QACd;kCAAG,EAAE;IAEL,OAAO;AACT;GARgB;AAaT,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,SAAS;QACT,UAAU;QACV,SAAS;QACT,UAAU;IACZ;IACA,MAAM,WAAW;IAEjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,UAAU;YAEf,MAAM;4DAAmB;oBACvB,MAAM,QAAQ,OAAO,UAAU;oBAC/B,cAAc;wBACZ,SAAS,SAAS;wBAClB,UAAU,SAAS;wBACnB,SAAS,SAAS;wBAClB,UAAU,SAAS;oBACrB;gBACF;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC;2CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;kCAAG;QAAC;KAAS;IAEb,OAAO;QAAE,GAAG,UAAU;QAAE;IAAS;AACnC;IA7BgB;;QAOG", "debugId": null}}, {"offset": {"line": 1070, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/doctors/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Search, Filter, MapPin, Star } from 'lucide-react';\nimport { Input } from '@/components/ui/input';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport DoctorCard from '@/components/dashboard/DoctorCard';\nimport { DoctorWithUser, SearchFilters } from '@/types';\nimport { toast } from 'sonner';\nimport { useResponsive } from '@/hooks/useClientOnly';\n\nconst specialties = [\n  'All Specialties',\n  'Cardiology',\n  'Dermatology',\n  'Endocrinology',\n  'Gastroenterology',\n  'General Practice',\n  'Neurology',\n  'Oncology',\n  'Orthopedics',\n  'Pediatrics',\n  'Psychiatry',\n  'Radiology',\n  'Surgery',\n  'Urology',\n];\n\nconst sortOptions = [\n  { value: 'rating', label: 'Highest Rated' },\n  { value: 'experience', label: 'Most Experienced' },\n  { value: 'fee', label: 'Lowest Fee' },\n  { value: 'name', label: 'Name (A-Z)' },\n];\n\nexport default function DoctorsPage() {\n  const [doctors, setDoctors] = useState<DoctorWithUser[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedSpecialty, setSelectedSpecialty] = useState('All Specialties');\n  const [location, setLocation] = useState('');\n  const [sortBy, setSortBy] = useState('rating');\n  const [showFilters, setShowFilters] = useState(false);\n  const { isLarge, isClient } = useResponsive();\n\n  const fetchDoctors = async () => {\n    setLoading(true);\n    try {\n      const params = new URLSearchParams();\n      \n      if (searchQuery) {\n        // Search in specialty or location\n        params.append('specialty', searchQuery);\n      }\n      \n      if (selectedSpecialty !== 'All Specialties') {\n        params.append('specialty', selectedSpecialty);\n      }\n      \n      if (location) {\n        params.append('location', location);\n      }\n      \n      params.append('sortBy', sortBy);\n      params.append('sortOrder', sortBy === 'fee' ? 'asc' : 'desc');\n      params.append('limit', '20');\n\n      const response = await fetch(`/api/doctors?${params.toString()}`);\n      const result = await response.json();\n\n      if (result.success) {\n        setDoctors(result.data);\n      } else {\n        toast.error('Failed to fetch doctors');\n      }\n    } catch (error) {\n      toast.error('Failed to fetch doctors');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchDoctors();\n  }, [selectedSpecialty, location, sortBy]);\n\n  const handleSearch = () => {\n    fetchDoctors();\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      handleSearch();\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-3xl font-bold text-gray-900\">Find a Doctor</h1>\n            <p className=\"mt-2 text-gray-600\">\n              Search and book appointments with qualified healthcare professionals\n            </p>\n          </div>\n\n          {/* Search Bar */}\n          <div className=\"mt-8 max-w-2xl mx-auto\">\n            <div className=\"flex space-x-2\">\n              <div className=\"flex-1 relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                <Input\n                  type=\"text\"\n                  placeholder=\"Search by doctor name, specialty, or location...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  onKeyPress={handleKeyPress}\n                  className=\"pl-10 pr-4 py-3 text-lg\"\n                />\n              </div>\n              <Button onClick={handleSearch} size=\"lg\" className=\"px-8\">\n                Search\n              </Button>\n            </div>\n          </div>\n\n          {/* Filters */}\n          <div className=\"mt-6\">\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"mb-4 lg:hidden\"\n            >\n              <Filter className=\"w-4 h-4 mr-2\" />\n              Filters\n            </Button>\n\n            <motion.div\n              initial={false}\n              animate={{ height: (isClient && (showFilters || isLarge)) ? 'auto' : 0 }}\n              className=\"overflow-hidden lg:overflow-visible\"\n            >\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Specialty\n                  </label>\n                  <Select value={selectedSpecialty} onValueChange={setSelectedSpecialty}>\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {specialties.map((specialty) => (\n                        <SelectItem key={specialty} value={specialty}>\n                          {specialty}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Location\n                  </label>\n                  <div className=\"relative\">\n                    <MapPin className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n                    <Input\n                      type=\"text\"\n                      placeholder=\"City, State\"\n                      value={location}\n                      onChange={(e) => setLocation(e.target.value)}\n                      className=\"pl-10\"\n                    />\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Sort By\n                  </label>\n                  <Select value={sortBy} onValueChange={setSortBy}>\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {sortOptions.map((option) => (\n                        <SelectItem key={option.value} value={option.value}>\n                          {option.label}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </div>\n\n      {/* Results */}\n      <div className=\"max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8\">\n        {loading ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {Array.from({ length: 6 }).map((_, index) => (\n              <Card key={index} className=\"animate-pulse\">\n                <CardHeader>\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"w-16 h-16 bg-gray-200 rounded-full\"></div>\n                    <div className=\"flex-1 space-y-2\">\n                      <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                      <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                      <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\n                    </div>\n                  </div>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-2\">\n                    <div className=\"h-3 bg-gray-200 rounded\"></div>\n                    <div className=\"h-3 bg-gray-200 rounded w-5/6\"></div>\n                    <div className=\"h-3 bg-gray-200 rounded w-4/6\"></div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        ) : doctors.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <div className=\"w-24 h-24 mx-auto mb-4 text-gray-300\">\n              <Search className=\"w-full h-full\" />\n            </div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              No doctors found\n            </h3>\n            <p className=\"text-gray-600\">\n              Try adjusting your search criteria or filters\n            </p>\n          </div>\n        ) : (\n          <>\n            <div className=\"flex items-center justify-between mb-6\">\n              <p className=\"text-gray-600\">\n                Found {doctors.length} doctor{doctors.length !== 1 ? 's' : ''}\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {doctors.map((doctor) => (\n                <DoctorCard key={doctor._id} doctor={doctor} />\n              ))}\n            </div>\n          </>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;AAZA;;;;;;;;;;;AAcA,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,cAAc;IAClB;QAAE,OAAO;QAAU,OAAO;IAAgB;IAC1C;QAAE,OAAO;QAAc,OAAO;IAAmB;IACjD;QAAE,OAAO;QAAO,OAAO;IAAa;IACpC;QAAE,OAAO;QAAQ,OAAO;IAAa;CACtC;AAEc,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IAE1C,MAAM,eAAe;QACnB,WAAW;QACX,IAAI;YACF,MAAM,SAAS,IAAI;YAEnB,IAAI,aAAa;gBACf,kCAAkC;gBAClC,OAAO,MAAM,CAAC,aAAa;YAC7B;YAEA,IAAI,sBAAsB,mBAAmB;gBAC3C,OAAO,MAAM,CAAC,aAAa;YAC7B;YAEA,IAAI,UAAU;gBACZ,OAAO,MAAM,CAAC,YAAY;YAC5B;YAEA,OAAO,MAAM,CAAC,UAAU;YACxB,OAAO,MAAM,CAAC,aAAa,WAAW,QAAQ,QAAQ;YACtD,OAAO,MAAM,CAAC,SAAS;YAEvB,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,OAAO,QAAQ,IAAI;YAChE,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,WAAW,OAAO,IAAI;YACxB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG;QAAC;QAAmB;QAAU;KAAO;IAExC,MAAM,eAAe;QACnB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;sCAMpC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,YAAY;gDACZ,WAAU;;;;;;;;;;;;kDAGd,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAc,MAAK;wCAAK,WAAU;kDAAO;;;;;;;;;;;;;;;;;sCAO9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,eAAe,CAAC;oCAC/B,WAAU;;sDAEV,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;oCACT,SAAS;wCAAE,QAAQ,AAAC,YAAY,CAAC,eAAe,OAAO,IAAK,SAAS;oCAAE;oCACvE,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAmB,eAAe;;0EAC/C,6LAAC,qIAAA,CAAA,gBAAa;0EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;0EAEd,6LAAC,qIAAA,CAAA,gBAAa;0EACX,YAAY,GAAG,CAAC,CAAC,0BAChB,6LAAC,qIAAA,CAAA,aAAU;wEAAiB,OAAO;kFAChC;uEADc;;;;;;;;;;;;;;;;;;;;;;0DAQzB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC,oIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,aAAY;gEACZ,OAAO;gEACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gEAC3C,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAQ,eAAe;;0EACpC,6LAAC,qIAAA,CAAA,gBAAa;0EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;0EAEd,6LAAC,qIAAA,CAAA,gBAAa;0EACX,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC,qIAAA,CAAA,aAAU;wEAAoB,OAAO,OAAO,KAAK;kFAC/C,OAAO,KAAK;uEADE,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc/C,6LAAC;gBAAI,WAAU;0BACZ,wBACC,6LAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC,mIAAA,CAAA,OAAI;4BAAa,WAAU;;8CAC1B,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAIrB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;2BAfV;;;;;;;;;2BAqBb,QAAQ,MAAM,KAAK,kBACrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;yCAK/B;;sCACE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;oCAAgB;oCACpB,QAAQ,MAAM;oCAAC;oCAAQ,QAAQ,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;sCAI/D,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,gJAAA,CAAA,UAAU;oCAAkB,QAAQ;mCAApB,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;;AAQ3C;GA/NwB;;QAQQ,gIAAA,CAAA,gBAAa;;;KARrB", "debugId": null}}]}