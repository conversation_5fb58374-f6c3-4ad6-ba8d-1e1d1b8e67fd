{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/db.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/doctor-patient-portal';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\n// Global is used here to maintain a cached connection across hot reloads in development\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached!.conn) {\n    return cached!.conn;\n  }\n\n  if (!cached!.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached!.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Connected to MongoDB');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached!.conn = await cached!.promise;\n  } catch (e) {\n    cached!.promise = null;\n    throw e;\n  }\n\n  return cached!.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAYA,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAQ,IAAI,EAAE;QAChB,OAAO,OAAQ,IAAI;IACrB;IAEA,IAAI,CAAC,OAAQ,OAAO,EAAE;QACpB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAQ,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YAC1D,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAQ,IAAI,GAAG,MAAM,OAAQ,OAAO;IACtC,EAAE,OAAO,GAAG;QACV,OAAQ,OAAO,GAAG;QAClB,MAAM;IACR;IAEA,OAAO,OAAQ,IAAI;AACrB;uCAEe", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/Doctor.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { Doctor as IDoctor, AvailabilitySlot } from '@/types';\n\nexport interface DoctorDocument extends Document, Omit<IDoctor, '_id'> {}\n\nconst AvailabilitySlotSchema = new Schema<AvailabilitySlot>({\n  dayOfWeek: {\n    type: Number,\n    required: true,\n    min: 0,\n    max: 6,\n  },\n  startTime: {\n    type: String,\n    required: true,\n    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,\n  },\n  endTime: {\n    type: String,\n    required: true,\n    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,\n  },\n  isAvailable: {\n    type: Boolean,\n    default: true,\n  },\n});\n\nconst LocationSchema = new Schema({\n  address: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  city: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  state: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  zipCode: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n});\n\nconst DoctorSchema = new Schema<DoctorDocument>({\n  userId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: true,\n    unique: true,\n  },\n  firstName: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  lastName: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  phone: {\n    type: String,\n    trim: true,\n  },\n  specialty: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  bio: {\n    type: String,\n    required: true,\n    maxlength: 1000,\n  },\n  experience: {\n    type: Number,\n    required: true,\n    min: 0,\n  },\n  rating: {\n    type: Number,\n    default: 0,\n    min: 0,\n    max: 5,\n  },\n  totalRatings: {\n    type: Number,\n    default: 0,\n    min: 0,\n  },\n  profileImage: {\n    type: String,\n  },\n  availability: [AvailabilitySlotSchema],\n  consultationFee: {\n    type: Number,\n    required: true,\n    min: 0,\n  },\n  location: {\n    type: LocationSchema,\n    required: true,\n  },\n}, {\n  timestamps: true,\n});\n\n// Indexes for better query performance\n// Note: userId index is already created by unique: true\nDoctorSchema.index({ specialty: 1 });\nDoctorSchema.index({ rating: -1 });\nDoctorSchema.index({ 'location.city': 1 });\nDoctorSchema.index({ 'location.state': 1 });\n\n// Compound indexes for common queries\nDoctorSchema.index({ specialty: 1, rating: -1 });\nDoctorSchema.index({ 'location.city': 1, specialty: 1 });\n\nexport default mongoose.models.Doctor || mongoose.model<DoctorDocument>('Doctor', DoctorSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAKA,MAAM,yBAAyB,IAAI,yGAAA,CAAA,SAAM,CAAmB;IAC1D,WAAW;QACT,MAAM;QACN,UAAU;QACV,KAAK;QACL,KAAK;IACP;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;AACF;AAEA,MAAM,iBAAiB,IAAI,yGAAA,CAAA,SAAM,CAAC;IAChC,SAAS;QACP,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,MAAM;IACR;AACF;AAEA,MAAM,eAAe,IAAI,yGAAA,CAAA,SAAM,CAAiB;IAC9C,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;QACV,QAAQ;IACV;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,KAAK;QACH,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,YAAY;QACV,MAAM;QACN,UAAU;QACV,KAAK;IACP;IACA,QAAQ;QACN,MAAM;QACN,SAAS;QACT,KAAK;QACL,KAAK;IACP;IACA,cAAc;QACZ,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,cAAc;QACZ,MAAM;IACR;IACA,cAAc;QAAC;KAAuB;IACtC,iBAAiB;QACf,MAAM;QACN,UAAU;QACV,KAAK;IACP;IACA,UAAU;QACR,MAAM;QACN,UAAU;IACZ;AACF,GAAG;IACD,YAAY;AACd;AAEA,uCAAuC;AACvC,wDAAwD;AACxD,aAAa,KAAK,CAAC;IAAE,WAAW;AAAE;AAClC,aAAa,KAAK,CAAC;IAAE,QAAQ,CAAC;AAAE;AAChC,aAAa,KAAK,CAAC;IAAE,iBAAiB;AAAE;AACxC,aAAa,KAAK,CAAC;IAAE,kBAAkB;AAAE;AAEzC,sCAAsC;AACtC,aAAa,KAAK,CAAC;IAAE,WAAW;IAAG,QAAQ,CAAC;AAAE;AAC9C,aAAa,KAAK,CAAC;IAAE,iBAAiB;IAAG,WAAW;AAAE;uCAEvC,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAiB,UAAU", "debugId": null}}, {"offset": {"line": 264, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\nexport interface UserDocument extends Document {\n  email: string;\n  password: string;\n  role: 'doctor' | 'patient';\n  isEmailVerified: boolean;\n  resetPasswordToken?: string;\n  resetPasswordExpiry?: Date;\n  emailVerificationToken?: string;\n  emailVerificationExpiry?: Date;\n  comparePassword(candidatePassword: string): Promise<boolean>;\n}\n\n\n\nconst UserSchema = new Schema<UserDocument>({\n  email: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true,\n  },\n  password: {\n    type: String,\n    required: true,\n    minlength: 6,\n  },\n  role: {\n    type: String,\n    enum: ['doctor', 'patient'],\n    required: true,\n  },\n  isEmailVerified: {\n    type: Boolean,\n    default: false,\n  },\n  emailVerificationToken: {\n    type: String,\n  },\n  emailVerificationExpiry: {\n    type: Date,\n  },\n  resetPasswordToken: {\n    type: String,\n  },\n  resetPasswordExpiry: {\n    type: Date,\n  },\n}, {\n  timestamps: true,\n});\n\n// Hash password before saving\nUserSchema.pre('save', async function (next) {\n  if (!this.isModified('password')) return next();\n\n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error as Error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Generate email verification token\nUserSchema.methods.generateEmailVerificationToken = function (): string {\n  const crypto = require('crypto');\n  const token = crypto.randomBytes(32).toString('hex');\n\n  this.emailVerificationToken = token;\n  this.emailVerificationExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours\n\n  return token;\n};\n\n// Generate password reset token\nUserSchema.methods.generatePasswordResetToken = function (): string {\n  const crypto = require('crypto');\n  const token = crypto.randomBytes(32).toString('hex');\n\n  this.resetPasswordToken = token;\n  this.resetPasswordExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour\n\n  return token;\n};\n\n// Verify email verification token\nUserSchema.methods.verifyEmailToken = function (token: string): boolean {\n  return this.emailVerificationToken === token &&\n         this.emailVerificationExpiry &&\n         this.emailVerificationExpiry > new Date();\n};\n\n// Verify password reset token\nUserSchema.methods.verifyPasswordResetToken = function (token: string): boolean {\n  return this.resetPasswordToken === token &&\n         this.resetPasswordExpiry &&\n         this.resetPasswordExpiry > new Date();\n};\n\n// Clean JSON output (remove sensitive fields)\nUserSchema.methods.toJSON = function () {\n  const userObject = this.toObject();\n  delete userObject.password;\n  delete userObject.resetPasswordToken;\n  delete userObject.resetPasswordExpiry;\n  delete userObject.emailVerificationToken;\n  delete userObject.emailVerificationExpiry;\n  delete userObject.__v;\n  return userObject;\n};\n\n// Indexes for better query performance\n// Note: email index is already created by unique: true\nUserSchema.index({ role: 1 });\n\nexport default mongoose.models.User || mongoose.model<UserDocument>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAgBA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAe;IAC1C,OAAO;QACL,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAU;SAAU;QAC3B,UAAU;IACZ;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,wBAAwB;QACtB,MAAM;IACR;IACA,yBAAyB;QACvB,MAAM;IACR;IACA,oBAAoB;QAClB,MAAM;IACR;IACA,qBAAqB;QACnB,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAgB,IAAI;IACzC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAgB,iBAAyB;IAC5E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,oCAAoC;AACpC,WAAW,OAAO,CAAC,8BAA8B,GAAG;IAClD,MAAM;IACN,MAAM,QAAQ,OAAO,WAAW,CAAC,IAAI,QAAQ,CAAC;IAE9C,IAAI,CAAC,sBAAsB,GAAG;IAC9B,IAAI,CAAC,uBAAuB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,OAAO,WAAW;IAEtF,OAAO;AACT;AAEA,gCAAgC;AAChC,WAAW,OAAO,CAAC,0BAA0B,GAAG;IAC9C,MAAM;IACN,MAAM,QAAQ,OAAO,WAAW,CAAC,IAAI,QAAQ,CAAC;IAE9C,IAAI,CAAC,kBAAkB,GAAG;IAC1B,IAAI,CAAC,mBAAmB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,OAAO,SAAS;IAE3E,OAAO;AACT;AAEA,kCAAkC;AAClC,WAAW,OAAO,CAAC,gBAAgB,GAAG,SAAU,KAAa;IAC3D,OAAO,IAAI,CAAC,sBAAsB,KAAK,SAChC,IAAI,CAAC,uBAAuB,IAC5B,IAAI,CAAC,uBAAuB,GAAG,IAAI;AAC5C;AAEA,8BAA8B;AAC9B,WAAW,OAAO,CAAC,wBAAwB,GAAG,SAAU,KAAa;IACnE,OAAO,IAAI,CAAC,kBAAkB,KAAK,SAC5B,IAAI,CAAC,mBAAmB,IACxB,IAAI,CAAC,mBAAmB,GAAG,IAAI;AACxC;AAEA,8CAA8C;AAC9C,WAAW,OAAO,CAAC,MAAM,GAAG;IAC1B,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO,WAAW,kBAAkB;IACpC,OAAO,WAAW,mBAAmB;IACrC,OAAO,WAAW,sBAAsB;IACxC,OAAO,WAAW,uBAAuB;IACzC,OAAO,WAAW,GAAG;IACrB,OAAO;AACT;AAEA,uCAAuC;AACvC,uDAAuD;AACvD,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;uCAEZ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAe,QAAQ", "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/validations.ts"], "sourcesContent": ["import { z } from 'zod';\n\n// User validation schemas\nexport const userProfileSchema = z.object({\n  firstName: z.string().min(1, 'First name is required').max(50),\n  lastName: z.string().min(1, 'Last name is required').max(50),\n  phone: z.string().optional(),\n  profileImage: z.string().url().optional(),\n  dateOfBirth: z.date().optional(),\n});\n\nexport const loginSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n});\n\nexport const registerSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n  role: z.enum(['doctor', 'patient']),\n  profile: userProfileSchema,\n  doctorInfo: z.object({\n    specialty: z.string().min(1, 'Specialty is required'),\n    bio: z.string().min(10, 'Bio must be at least 10 characters').max(1000),\n    experience: z.number().min(0, 'Experience cannot be negative'),\n    consultationFee: z.number().min(0, 'Fee cannot be negative'),\n    location: z.object({\n      address: z.string().min(1, 'Address is required'),\n      city: z.string().min(1, 'City is required'),\n      state: z.string().min(1, 'State is required'),\n      zipCode: z.string().min(1, 'Zip code is required'),\n    }),\n  }).optional(),\n});\n\n// Doctor validation schemas\nexport const availabilitySlotSchema = z.object({\n  dayOfWeek: z.number().min(0).max(6),\n  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),\n  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),\n  isAvailable: z.boolean().default(true),\n});\n\nexport const doctorUpdateSchema = z.object({\n  specialty: z.string().min(1).optional(),\n  bio: z.string().min(10).max(1000).optional(),\n  experience: z.number().min(0).optional(),\n  consultationFee: z.number().min(0).optional(),\n  availability: z.array(availabilitySlotSchema).optional(),\n  location: z.object({\n    address: z.string().min(1),\n    city: z.string().min(1),\n    state: z.string().min(1),\n    zipCode: z.string().min(1),\n  }).optional(),\n});\n\n// Appointment validation schemas\nexport const bookAppointmentSchema = z.object({\n  doctorId: z.string().min(1, 'Doctor ID is required'),\n  dateTime: z.date(),\n  symptoms: z.string().max(500).optional(),\n  notes: z.string().max(500).optional(),\n});\n\nexport const updateAppointmentSchema = z.object({\n  status: z.enum(['pending', 'approved', 'rejected', 'completed', 'cancelled']).optional(),\n  notes: z.string().max(500).optional(),\n  prescription: z.string().max(1000).optional(),\n  diagnosis: z.string().max(1000).optional(),\n});\n\n// Search and filter schemas\nexport const searchDoctorsSchema = z.object({\n  specialty: z.string().optional(),\n  location: z.string().optional(),\n  rating: z.number().min(0).max(5).optional(),\n  availability: z.boolean().optional(),\n  sortBy: z.enum(['rating', 'experience', 'fee', 'name']).default('rating'),\n  sortOrder: z.enum(['asc', 'desc']).default('desc'),\n  page: z.number().min(1).default(1),\n  limit: z.number().min(1).max(50).default(10),\n});\n\n// Query parameter validation\nexport const paginationSchema = z.object({\n  page: z.number().min(1).default(1),\n  limit: z.number().min(1).max(50).default(10),\n});\n\nexport const appointmentFilterSchema = z.object({\n  status: z.enum(['pending', 'approved', 'rejected', 'completed', 'cancelled']).optional(),\n  startDate: z.date().optional(),\n  endDate: z.date().optional(),\n  ...paginationSchema.shape,\n});\n\n// Utility function to validate and parse request body\nexport function validateRequestBody<T>(schema: z.ZodSchema<T>, data: unknown): T {\n  const result = schema.safeParse(data);\n  if (!result.success) {\n    throw new Error(`Validation error: ${result.error.errors.map(e => e.message).join(', ')}`);\n  }\n  return result.data;\n}\n\n// Utility function to validate query parameters\nexport function validateQueryParams<T>(schema: z.ZodSchema<T>, params: Record<string, any>): T {\n  // Convert string numbers to actual numbers for query params\n  const processedParams = Object.entries(params).reduce((acc, [key, value]) => {\n    if (value === undefined || value === null || value === '') {\n      return acc;\n    }\n    \n    // Try to convert string numbers to numbers\n    if (typeof value === 'string' && !isNaN(Number(value))) {\n      acc[key] = Number(value);\n    } else if (value === 'true') {\n      acc[key] = true;\n    } else if (value === 'false') {\n      acc[key] = false;\n    } else {\n      acc[key] = value;\n    }\n    \n    return acc;\n  }, {} as Record<string, any>);\n\n  const result = schema.safeParse(processedParams);\n  if (!result.success) {\n    throw new Error(`Query validation error: ${result.error.errors.map(e => e.message).join(', ')}`);\n  }\n  return result.data;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAGO,MAAM,oBAAoB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC;IAC3D,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC;IACzD,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,cAAc,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;IACvC,aAAa,oKAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;AAChC;AAEO,MAAM,cAAc,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEO,MAAM,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,MAAM,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;KAAU;IAClC,SAAS;IACT,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC7B,KAAK,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,sCAAsC,GAAG,CAAC;QAClE,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC9B,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACnC,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACjB,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;YAC3B,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;YACxB,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;YACzB,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC7B;IACF,GAAG,QAAQ;AACb;AAGO,MAAM,yBAAyB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7C,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACjC,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,qCAAqC;IACjE,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,qCAAqC;IAC/D,aAAa,oKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AACnC;AAEO,MAAM,qBAAqB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;IACrC,KAAK,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,QAAQ;IAC1C,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;IACtC,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;IAC3C,cAAc,oKAAA,CAAA,IAAC,CAAC,KAAK,CAAC,wBAAwB,QAAQ;IACtD,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACjB,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;QACxB,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;QACrB,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;QACtB,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC1B,GAAG,QAAQ;AACb;AAGO,MAAM,wBAAwB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,UAAU,oKAAA,CAAA,IAAC,CAAC,IAAI;IAChB,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACtC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;AACrC;AAEO,MAAM,0BAA0B,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,QAAQ,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;QAAY;QAAY;QAAa;KAAY,EAAE,QAAQ;IACtF,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACnC,cAAc,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,QAAQ;IAC3C,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,QAAQ;AAC1C;AAGO,MAAM,sBAAsB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;IACzC,cAAc,oKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IAClC,QAAQ,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;QAAc;QAAO;KAAO,EAAE,OAAO,CAAC;IAChE,WAAW,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;KAAO,EAAE,OAAO,CAAC;IAC3C,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAChC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;AAC3C;AAGO,MAAM,mBAAmB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAChC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;AAC3C;AAEO,MAAM,0BAA0B,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,QAAQ,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;QAAY;QAAY;QAAa;KAAY,EAAE,QAAQ;IACtF,WAAW,oKAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;IAC5B,SAAS,oKAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;IAC1B,GAAG,iBAAiB,KAAK;AAC3B;AAGO,SAAS,oBAAuB,MAAsB,EAAE,IAAa;IAC1E,MAAM,SAAS,OAAO,SAAS,CAAC;IAChC,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO;IAC3F;IACA,OAAO,OAAO,IAAI;AACpB;AAGO,SAAS,oBAAuB,MAAsB,EAAE,MAA2B;IACxF,4DAA4D;IAC5D,MAAM,kBAAkB,OAAO,OAAO,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,MAAM;QACtE,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,OAAO;QACT;QAEA,2CAA2C;QAC3C,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,OAAO,SAAS;YACtD,GAAG,CAAC,IAAI,GAAG,OAAO;QACpB,OAAO,IAAI,UAAU,QAAQ;YAC3B,GAAG,CAAC,IAAI,GAAG;QACb,OAAO,IAAI,UAAU,SAAS;YAC5B,GAAG,CAAC,IAAI,GAAG;QACb,OAAO;YACL,GAAG,CAAC,IAAI,GAAG;QACb;QAEA,OAAO;IACT,GAAG,CAAC;IAEJ,MAAM,SAAS,OAAO,SAAS,CAAC;IAChC,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO;IACjG;IACA,OAAO,OAAO,IAAI;AACpB", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/api/doctors/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/db';\nimport Doctor from '@/models/Doctor';\nimport User from '@/models/User';\nimport { validateQueryParams, searchDoctorsSchema } from '@/lib/validations';\nimport { ApiResponse, PaginatedResponse, DoctorWithUser } from '@/types';\n\nexport async function GET(request: NextRequest) {\n  try {\n    await connectDB();\n\n    const { searchParams } = new URL(request.url);\n    const queryParams = Object.fromEntries(searchParams.entries());\n    \n    const {\n      specialty,\n      location,\n      rating,\n      availability,\n      sortBy,\n      sortOrder,\n      page,\n      limit\n    } = validateQueryParams(searchDoctorsSchema, queryParams);\n\n    // Build query\n    const query: any = {};\n    \n    if (specialty) {\n      query.specialty = { $regex: specialty, $options: 'i' };\n    }\n    \n    if (location) {\n      query.$or = [\n        { 'location.city': { $regex: location, $options: 'i' } },\n        { 'location.state': { $regex: location, $options: 'i' } },\n        { 'location.address': { $regex: location, $options: 'i' } }\n      ];\n    }\n    \n    if (rating) {\n      query.rating = { $gte: rating };\n    }\n\n    // Build sort object\n    const sort: any = {};\n    switch (sortBy) {\n      case 'rating':\n        sort.rating = sortOrder === 'asc' ? 1 : -1;\n        break;\n      case 'experience':\n        sort.experience = sortOrder === 'asc' ? 1 : -1;\n        break;\n      case 'fee':\n        sort.consultationFee = sortOrder === 'asc' ? 1 : -1;\n        break;\n      case 'name':\n        sort['user.profile.firstName'] = sortOrder === 'asc' ? 1 : -1;\n        break;\n      default:\n        sort.rating = -1;\n    }\n\n    // Calculate pagination\n    const skip = (page - 1) * limit;\n\n    // Get doctors with user information\n    const doctors = await Doctor.find(query)\n      .populate('userId', '-password')\n      .sort(sort)\n      .skip(skip)\n      .limit(limit)\n      .lean();\n\n    // Get total count for pagination\n    const total = await Doctor.countDocuments(query);\n    const pages = Math.ceil(total / limit);\n\n    // Transform data to include user information, filtering out doctors without valid user data\n    const doctorsWithUser: DoctorWithUser[] = doctors\n      .filter(doctor => doctor.userId && typeof doctor.userId === 'object') // Ensure userId is populated\n      .map(doctor => ({\n        ...doctor,\n        user: doctor.userId as any,\n      }));\n\n    return NextResponse.json<PaginatedResponse<DoctorWithUser>>({\n      success: true,\n      data: doctorsWithUser,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages,\n      },\n    });\n\n  } catch (error) {\n    console.error('Get doctors error:', error);\n\n    return NextResponse.json<ApiResponse>({\n      success: false,\n      error: error instanceof Error ? error.message : 'Failed to fetch doctors',\n    }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    await connectDB();\n    console.log('POST /api/doctors called');\n\n    // Get token from Authorization header or cookies\n    const authHeader = request.headers.get('authorization');\n    const cookieToken = request.cookies.get('authToken')?.value;\n\n    const token = authHeader?.replace('Bearer ', '') || cookieToken;\n    console.log('Auth header:', authHeader);\n    console.log('Cookie token exists:', !!cookieToken);\n    console.log('Final token exists:', !!token);\n\n    if (!token) {\n      console.log('No authentication token found');\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Authorization token required',\n      }, { status: 401 });\n    }\n    const { jwtVerify } = await import('jose');\n    const secret = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key');\n\n    // Verify token\n    let userId;\n    try {\n      const jwt = await import('jsonwebtoken');\n      const decoded = jwt.default.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;\n      userId = decoded.userId;\n      console.log('Token verified, userId:', userId);\n    } catch (error) {\n      console.log('Token verification failed:', error);\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Invalid token',\n      }, { status: 401 });\n    }\n\n    // Check if user exists and is a doctor\n    const user = await User.findById(userId);\n    if (!user) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'User not found',\n      }, { status: 404 });\n    }\n\n    if (user.role !== 'doctor') {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Only doctors can create doctor profiles',\n      }, { status: 403 });\n    }\n\n    // Check if doctor profile already exists\n    const existingDoctor = await Doctor.findOne({ userId });\n    if (existingDoctor) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Doctor profile already exists',\n      }, { status: 409 });\n    }\n\n    // Get request body\n    const body = await request.json();\n    const {\n      specialty = 'General Practice',\n      experience = 0,\n      qualifications = [],\n      consultationFee = 50,\n      availability = [],\n      bio = 'Welcome to my practice. I am dedicated to providing quality healthcare.',\n      location = {\n        address: 'To be updated',\n        city: 'To be updated',\n        state: 'To be updated',\n        zipCode: '00000'\n      }\n    } = body;\n\n    // Create doctor profile\n    const doctor = new Doctor({\n      userId,\n      specialty,\n      experience,\n      qualifications,\n      consultationFee,\n      availability,\n      bio,\n      location,\n      rating: 0,\n      totalRatings: 0,\n      isVerified: false,\n    });\n\n    await doctor.save();\n\n    return NextResponse.json<ApiResponse>({\n      success: true,\n      message: 'Doctor profile created successfully',\n      data: { doctor },\n    }, { status: 201 });\n\n  } catch (error) {\n    console.error('Create doctor profile error:', error);\n\n    return NextResponse.json<ApiResponse>({\n      success: false,\n      error: error instanceof Error ? error.message : 'Failed to create doctor profile',\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,cAAc,OAAO,WAAW,CAAC,aAAa,OAAO;QAE3D,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,MAAM,EACN,YAAY,EACZ,MAAM,EACN,SAAS,EACT,IAAI,EACJ,KAAK,EACN,GAAG,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD,EAAE,2HAAA,CAAA,sBAAmB,EAAE;QAE7C,cAAc;QACd,MAAM,QAAa,CAAC;QAEpB,IAAI,WAAW;YACb,MAAM,SAAS,GAAG;gBAAE,QAAQ;gBAAW,UAAU;YAAI;QACvD;QAEA,IAAI,UAAU;YACZ,MAAM,GAAG,GAAG;gBACV;oBAAE,iBAAiB;wBAAE,QAAQ;wBAAU,UAAU;oBAAI;gBAAE;gBACvD;oBAAE,kBAAkB;wBAAE,QAAQ;wBAAU,UAAU;oBAAI;gBAAE;gBACxD;oBAAE,oBAAoB;wBAAE,QAAQ;wBAAU,UAAU;oBAAI;gBAAE;aAC3D;QACH;QAEA,IAAI,QAAQ;YACV,MAAM,MAAM,GAAG;gBAAE,MAAM;YAAO;QAChC;QAEA,oBAAoB;QACpB,MAAM,OAAY,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,KAAK,MAAM,GAAG,cAAc,QAAQ,IAAI,CAAC;gBACzC;YACF,KAAK;gBACH,KAAK,UAAU,GAAG,cAAc,QAAQ,IAAI,CAAC;gBAC7C;YACF,KAAK;gBACH,KAAK,eAAe,GAAG,cAAc,QAAQ,IAAI,CAAC;gBAClD;YACF,KAAK;gBACH,IAAI,CAAC,yBAAyB,GAAG,cAAc,QAAQ,IAAI,CAAC;gBAC5D;YACF;gBACE,KAAK,MAAM,GAAG,CAAC;QACnB;QAEA,uBAAuB;QACvB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,oCAAoC;QACpC,MAAM,UAAU,MAAM,yHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,OAC/B,QAAQ,CAAC,UAAU,aACnB,IAAI,CAAC,MACL,IAAI,CAAC,MACL,KAAK,CAAC,OACN,IAAI;QAEP,iCAAiC;QACjC,MAAM,QAAQ,MAAM,yHAAA,CAAA,UAAM,CAAC,cAAc,CAAC;QAC1C,MAAM,QAAQ,KAAK,IAAI,CAAC,QAAQ;QAEhC,4FAA4F;QAC5F,MAAM,kBAAoC,QACvC,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,IAAI,OAAO,OAAO,MAAM,KAAK,UAAU,6BAA6B;SAClG,GAAG,CAAC,CAAA,SAAU,CAAC;gBACd,GAAG,MAAM;gBACT,MAAM,OAAO,MAAM;YACrB,CAAC;QAEH,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoC;YAC1D,SAAS;YACT,MAAM;YACN,YAAY;gBACV;gBACA;gBACA;gBACA;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QAEpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QACd,QAAQ,GAAG,CAAC;QAEZ,iDAAiD;QACjD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,cAAc;QAEtD,MAAM,QAAQ,YAAY,QAAQ,WAAW,OAAO;QACpD,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,QAAQ,GAAG,CAAC,wBAAwB,CAAC,CAAC;QACtC,QAAQ,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAErC,IAAI,CAAC,OAAO;YACV,QAAQ,GAAG,CAAC;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QACA,MAAM,EAAE,SAAS,EAAE,GAAG;QACtB,MAAM,SAAS,IAAI,cAAc,MAAM,CAAC,QAAQ,GAAG,CAAC,UAAU,IAAI;QAElE,eAAe;QACf,IAAI;QACJ,IAAI;YACF,MAAM,MAAM;YACZ,MAAM,UAAU,IAAI,OAAO,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;YACpE,SAAS,QAAQ,MAAM;YACvB,QAAQ,GAAG,CAAC,2BAA2B;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,uCAAuC;QACvC,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,IAAI,KAAK,IAAI,KAAK,UAAU;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,yCAAyC;QACzC,MAAM,iBAAiB,MAAM,yHAAA,CAAA,UAAM,CAAC,OAAO,CAAC;YAAE;QAAO;QACrD,IAAI,gBAAgB;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,mBAAmB;QACnB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,YAAY,kBAAkB,EAC9B,aAAa,CAAC,EACd,iBAAiB,EAAE,EACnB,kBAAkB,EAAE,EACpB,eAAe,EAAE,EACjB,MAAM,yEAAyE,EAC/E,WAAW;YACT,SAAS;YACT,MAAM;YACN,OAAO;YACP,SAAS;QACX,CAAC,EACF,GAAG;QAEJ,wBAAwB;QACxB,MAAM,SAAS,IAAI,yHAAA,CAAA,UAAM,CAAC;YACxB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,QAAQ;YACR,cAAc;YACd,YAAY;QACd;QAEA,MAAM,OAAO,IAAI;QAEjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,SAAS;YACT,MAAM;gBAAE;YAAO;QACjB,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAE9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}