{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/test-dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport { useRouter } from 'next/navigation';\nimport { useEffect, useState } from 'react';\n\nexport default function TestDashboardPage() {\n  const { user, isLoading, isAuthenticated } = useAuth();\n  const router = useRouter();\n  const [appointments, setAppointments] = useState([]);\n  const [appointmentsLoading, setAppointmentsLoading] = useState(false);\n\n  useEffect(() => {\n    console.log('TestDashboard - Auth state:', { user, isLoading, isAuthenticated });\n  }, [user, isLoading, isAuthenticated]);\n\n  const testAppointmentsAPI = async () => {\n    setAppointmentsLoading(true);\n    try {\n      const response = await fetch('/api/appointments?limit=5');\n      const result = await response.json();\n      console.log('Appointments API result:', result);\n      if (result.success) {\n        setAppointments(result.data);\n      }\n    } catch (error) {\n      console.error('Appointments API error:', error);\n    } finally {\n      setAppointmentsLoading(false);\n    }\n  };\n\n  if (isLoading) {\n    return <div className=\"p-8\">Loading authentication...</div>;\n  }\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"p-8\">\n        <p>Not authenticated.</p>\n        <button \n          onClick={() => router.push('/login')}\n          className=\"bg-blue-500 text-white px-4 py-2 rounded mt-4\"\n        >\n          Go to Login\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-8\">\n      <h1 className=\"text-2xl font-bold mb-4\">Dashboard Test Page</h1>\n      \n      <div className=\"space-y-4 mb-6\">\n        <div className=\"bg-gray-100 p-4 rounded\">\n          <h2 className=\"font-semibold mb-2\">Authentication Status</h2>\n          <p><strong>User ID:</strong> {user?.id}</p>\n          <p><strong>Email:</strong> {user?.email}</p>\n          <p><strong>Name:</strong> {user?.firstName} {user?.lastName}</p>\n          <p><strong>Role:</strong> {user?.role}</p>\n          <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>\n        </div>\n\n        <div className=\"bg-gray-100 p-4 rounded\">\n          <h2 className=\"font-semibold mb-2\">API Test</h2>\n          <button \n            onClick={testAppointmentsAPI}\n            disabled={appointmentsLoading}\n            className=\"bg-green-500 text-white px-4 py-2 rounded disabled:opacity-50\"\n          >\n            {appointmentsLoading ? 'Loading...' : 'Test Appointments API'}\n          </button>\n          \n          {appointments.length > 0 && (\n            <div className=\"mt-4\">\n              <p><strong>Appointments found:</strong> {appointments.length}</p>\n              <pre className=\"bg-white p-2 rounded mt-2 text-sm overflow-auto\">\n                {JSON.stringify(appointments, null, 2)}\n              </pre>\n            </div>\n          )}\n        </div>\n      </div>\n      \n      <div className=\"space-x-4\">\n        <button \n          onClick={() => router.push(user?.role === 'doctor' ? '/doctor/dashboard' : '/patient/dashboard')}\n          className=\"bg-blue-500 text-white px-4 py-2 rounded\"\n        >\n          Go to Real Dashboard\n        </button>\n        \n        <button \n          onClick={() => router.push(user?.role === 'doctor' ? '/doctor/profile' : '/patient/profile')}\n          className=\"bg-purple-500 text-white px-4 py-2 rounded\"\n        >\n          Go to Profile\n        </button>\n\n        <button \n          onClick={() => router.push('/dashboard')}\n          className=\"bg-gray-500 text-white px-4 py-2 rounded\"\n        >\n          Go to Dashboard Redirect\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,QAAQ,GAAG,CAAC,+BAA+B;gBAAE;gBAAM;gBAAW;YAAgB;QAChF;sCAAG;QAAC;QAAM;QAAW;KAAgB;IAErC,MAAM,sBAAsB;QAC1B,uBAAuB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,QAAQ,GAAG,CAAC,4BAA4B;YACxC,IAAI,OAAO,OAAO,EAAE;gBAClB,gBAAgB,OAAO,IAAI;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,IAAI,WAAW;QACb,qBAAO,6LAAC;YAAI,WAAU;sBAAM;;;;;;IAC9B;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;8BAAE;;;;;;8BACH,6LAAC;oBACC,SAAS,IAAM,OAAO,IAAI,CAAC;oBAC3B,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B;;;;;;0BAExC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC;;kDAAE,6LAAC;kDAAO;;;;;;oCAAiB;oCAAE,MAAM;;;;;;;0CACpC,6LAAC;;kDAAE,6LAAC;kDAAO;;;;;;oCAAe;oCAAE,MAAM;;;;;;;0CAClC,6LAAC;;kDAAE,6LAAC;kDAAO;;;;;;oCAAc;oCAAE,MAAM;oCAAU;oCAAE,MAAM;;;;;;;0CACnD,6LAAC;;kDAAE,6LAAC;kDAAO;;;;;;oCAAc;oCAAE,MAAM;;;;;;;0CACjC,6LAAC;;kDAAE,6LAAC;kDAAO;;;;;;oCAAuB;oCAAE,kBAAkB,QAAQ;;;;;;;;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,sBAAsB,eAAe;;;;;;4BAGvC,aAAa,MAAM,GAAG,mBACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAA4B;4CAAE,aAAa,MAAM;;;;;;;kDAC5D,6LAAC;wCAAI,WAAU;kDACZ,KAAK,SAAS,CAAC,cAAc,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0BAO9C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC,MAAM,SAAS,WAAW,sBAAsB;wBAC3E,WAAU;kCACX;;;;;;kCAID,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC,MAAM,SAAS,WAAW,oBAAoB;wBACzE,WAAU;kCACX;;;;;;kCAID,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;GAvGwB;;QACuB,kJAAA,CAAA,UAAO;QACrC,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}