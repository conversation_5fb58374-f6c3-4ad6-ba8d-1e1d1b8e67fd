import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import Doctor from '@/models/Doctor';
import User from '@/models/User';
import { validateQueryParams, searchDoctorsSchema } from '@/lib/validations';
import { ApiResponse, PaginatedResponse, DoctorWithUser } from '@/types';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const {
      specialty,
      location,
      rating,
      availability,
      sortBy,
      sortOrder,
      page,
      limit
    } = validateQueryParams(searchDoctorsSchema, queryParams);

    // Build query
    const query: any = {};
    
    if (specialty) {
      query.specialty = { $regex: specialty, $options: 'i' };
    }
    
    if (location) {
      query.$or = [
        { 'location.city': { $regex: location, $options: 'i' } },
        { 'location.state': { $regex: location, $options: 'i' } },
        { 'location.address': { $regex: location, $options: 'i' } }
      ];
    }
    
    if (rating) {
      query.rating = { $gte: rating };
    }

    // Build sort object
    const sort: any = {};
    switch (sortBy) {
      case 'rating':
        sort.rating = sortOrder === 'asc' ? 1 : -1;
        break;
      case 'experience':
        sort.experience = sortOrder === 'asc' ? 1 : -1;
        break;
      case 'fee':
        sort.consultationFee = sortOrder === 'asc' ? 1 : -1;
        break;
      case 'name':
        sort['user.profile.firstName'] = sortOrder === 'asc' ? 1 : -1;
        break;
      default:
        sort.rating = -1;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get doctors with user information
    const doctors = await Doctor.find(query)
      .populate('userId', '-password')
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await Doctor.countDocuments(query);
    const pages = Math.ceil(total / limit);

    // Transform data to include user information, filtering out doctors without valid user data
    const doctorsWithUser: DoctorWithUser[] = doctors
      .filter(doctor => doctor.userId && typeof doctor.userId === 'object') // Ensure userId is populated
      .map(doctor => ({
        ...doctor,
        user: doctor.userId as any,
      }));

    return NextResponse.json<PaginatedResponse<DoctorWithUser>>({
      success: true,
      data: doctorsWithUser,
      pagination: {
        page,
        limit,
        total,
        pages,
      },
    });

  } catch (error) {
    console.error('Get doctors error:', error);

    return NextResponse.json<ApiResponse>({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch doctors',
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    console.log('POST /api/doctors called');

    // Get token from Authorization header or cookies
    const authHeader = request.headers.get('authorization');
    const cookieToken = request.cookies.get('authToken')?.value;

    const token = authHeader?.replace('Bearer ', '') || cookieToken;
    console.log('Auth header:', authHeader);
    console.log('Cookie token exists:', !!cookieToken);
    console.log('Final token exists:', !!token);

    if (!token) {
      console.log('No authentication token found');
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Authorization token required',
      }, { status: 401 });
    }
    const { jwtVerify } = await import('jose');
    const secret = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key');

    // Verify token
    let userId;
    try {
      const jwt = await import('jsonwebtoken');
      const decoded = jwt.default.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
      userId = decoded.userId;
      console.log('Token verified, userId:', userId);
    } catch (error) {
      console.log('Token verification failed:', error);
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Invalid token',
      }, { status: 401 });
    }

    // Check if user exists and is a doctor
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'User not found',
      }, { status: 404 });
    }

    if (user.role !== 'doctor') {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Only doctors can create doctor profiles',
      }, { status: 403 });
    }

    // Check if doctor profile already exists
    const existingDoctor = await Doctor.findOne({ userId });
    if (existingDoctor) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Doctor profile already exists',
      }, { status: 409 });
    }

    // Get request body
    const body = await request.json();
    const {
      specialty = 'General Practice',
      experience = 0,
      qualifications = [],
      consultationFee = 50,
      availability = [],
      bio = 'Welcome to my practice. I am dedicated to providing quality healthcare.',
      location = {
        address: 'To be updated',
        city: 'To be updated',
        state: 'To be updated',
        zipCode: '00000'
      }
    } = body;

    // Create doctor profile
    const doctor = new Doctor({
      userId,
      specialty,
      experience,
      qualifications,
      consultationFee,
      availability,
      bio,
      location,
      rating: 0,
      totalRatings: 0,
      isVerified: false,
    });

    await doctor.save();

    return NextResponse.json<ApiResponse>({
      success: true,
      message: 'Doctor profile created successfully',
      data: { doctor },
    }, { status: 201 });

  } catch (error) {
    console.error('Create doctor profile error:', error);

    return NextResponse.json<ApiResponse>({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create doctor profile',
    }, { status: 500 });
  }
}
