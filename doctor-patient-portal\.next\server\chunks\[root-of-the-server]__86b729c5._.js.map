{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/db.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/doctor-patient-portal';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\n// Global is used here to maintain a cached connection across hot reloads in development\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached!.conn) {\n    return cached!.conn;\n  }\n\n  if (!cached!.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached!.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Connected to MongoDB');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached!.conn = await cached!.promise;\n  } catch (e) {\n    cached!.promise = null;\n    throw e;\n  }\n\n  return cached!.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAYA,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAQ,IAAI,EAAE;QAChB,OAAO,OAAQ,IAAI;IACrB;IAEA,IAAI,CAAC,OAAQ,OAAO,EAAE;QACpB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAQ,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YAC1D,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAQ,IAAI,GAAG,MAAM,OAAQ,OAAO;IACtC,EAAE,OAAO,GAAG;QACV,OAAQ,OAAO,GAAG;QAClB,MAAM;IACR;IAEA,OAAO,OAAQ,IAAI;AACrB;uCAEe", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/Doctor.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { Doctor as IDoctor, AvailabilitySlot } from '@/types';\n\nexport interface DoctorDocument extends Document, Omit<IDoctor, '_id'> {}\n\nconst AvailabilitySlotSchema = new Schema<AvailabilitySlot>({\n  dayOfWeek: {\n    type: Number,\n    required: true,\n    min: 0,\n    max: 6,\n  },\n  startTime: {\n    type: String,\n    required: true,\n    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,\n  },\n  endTime: {\n    type: String,\n    required: true,\n    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,\n  },\n  isAvailable: {\n    type: Boolean,\n    default: true,\n  },\n});\n\nconst LocationSchema = new Schema({\n  address: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  city: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  state: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  zipCode: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n});\n\nconst DoctorSchema = new Schema<DoctorDocument>({\n  userId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: true,\n    unique: true,\n  },\n  specialty: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  bio: {\n    type: String,\n    required: true,\n    maxlength: 1000,\n  },\n  experience: {\n    type: Number,\n    required: true,\n    min: 0,\n  },\n  rating: {\n    type: Number,\n    default: 0,\n    min: 0,\n    max: 5,\n  },\n  totalRatings: {\n    type: Number,\n    default: 0,\n    min: 0,\n  },\n  profileImage: {\n    type: String,\n  },\n  availability: [AvailabilitySlotSchema],\n  consultationFee: {\n    type: Number,\n    required: true,\n    min: 0,\n  },\n  location: {\n    type: LocationSchema,\n    required: true,\n  },\n}, {\n  timestamps: true,\n});\n\n// Indexes for better query performance\n// Note: userId index is already created by unique: true\nDoctorSchema.index({ specialty: 1 });\nDoctorSchema.index({ rating: -1 });\nDoctorSchema.index({ 'location.city': 1 });\nDoctorSchema.index({ 'location.state': 1 });\n\n// Compound indexes for common queries\nDoctorSchema.index({ specialty: 1, rating: -1 });\nDoctorSchema.index({ 'location.city': 1, specialty: 1 });\n\nexport default mongoose.models.Doctor || mongoose.model<DoctorDocument>('Doctor', DoctorSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAKA,MAAM,yBAAyB,IAAI,yGAAA,CAAA,SAAM,CAAmB;IAC1D,WAAW;QACT,MAAM;QACN,UAAU;QACV,KAAK;QACL,KAAK;IACP;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;AACF;AAEA,MAAM,iBAAiB,IAAI,yGAAA,CAAA,SAAM,CAAC;IAChC,SAAS;QACP,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,MAAM;IACR;AACF;AAEA,MAAM,eAAe,IAAI,yGAAA,CAAA,SAAM,CAAiB;IAC9C,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;QACV,QAAQ;IACV;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,KAAK;QACH,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,YAAY;QACV,MAAM;QACN,UAAU;QACV,KAAK;IACP;IACA,QAAQ;QACN,MAAM;QACN,SAAS;QACT,KAAK;QACL,KAAK;IACP;IACA,cAAc;QACZ,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,cAAc;QACZ,MAAM;IACR;IACA,cAAc;QAAC;KAAuB;IACtC,iBAAiB;QACf,MAAM;QACN,UAAU;QACV,KAAK;IACP;IACA,UAAU;QACR,MAAM;QACN,UAAU;IACZ;AACF,GAAG;IACD,YAAY;AACd;AAEA,uCAAuC;AACvC,wDAAwD;AACxD,aAAa,KAAK,CAAC;IAAE,WAAW;AAAE;AAClC,aAAa,KAAK,CAAC;IAAE,QAAQ,CAAC;AAAE;AAChC,aAAa,KAAK,CAAC;IAAE,iBAAiB;AAAE;AACxC,aAAa,KAAK,CAAC;IAAE,kBAAkB;AAAE;AAEzC,sCAAsC;AACtC,aAAa,KAAK,CAAC;IAAE,WAAW;IAAG,QAAQ,CAAC;AAAE;AAC9C,aAAa,KAAK,CAAC;IAAE,iBAAiB;IAAG,WAAW;AAAE;uCAEvC,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAiB,UAAU", "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport bcrypt from 'bcryptjs';\nimport { User as IUser, UserProfile } from '@/types';\n\nexport interface UserDocument extends Document, Omit<IUser, '_id'> {\n  password: string;\n  resetPasswordToken?: string;\n  resetPasswordExpiry?: Date;\n  isEmailVerified: boolean;\n  emailVerificationToken?: string;\n  emailVerificationExpiry?: Date;\n  comparePassword(candidatePassword: string): Promise<boolean>;\n}\n\nconst EmergencyContactSchema = new Schema({\n  name: {\n    type: String,\n    trim: true,\n  },\n  relationship: {\n    type: String,\n    trim: true,\n  },\n  phone: {\n    type: String,\n    trim: true,\n  },\n});\n\nconst MedicalHistorySchema = new Schema({\n  allergies: {\n    type: String,\n    trim: true,\n  },\n  medications: {\n    type: String,\n    trim: true,\n  },\n  conditions: {\n    type: String,\n    trim: true,\n  },\n  surgeries: {\n    type: String,\n    trim: true,\n  },\n});\n\nconst InsuranceSchema = new Schema({\n  provider: {\n    type: String,\n    trim: true,\n  },\n  policyNumber: {\n    type: String,\n    trim: true,\n  },\n  groupNumber: {\n    type: String,\n    trim: true,\n  },\n});\n\nconst UserProfileSchema = new Schema<UserProfile>({\n  firstName: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  lastName: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  phone: {\n    type: String,\n    trim: true,\n  },\n  profileImage: {\n    type: String,\n  },\n  dateOfBirth: {\n    type: Date,\n  },\n  gender: {\n    type: String,\n    enum: ['male', 'female', 'other', 'prefer-not-to-say'],\n  },\n  address: {\n    type: String,\n    trim: true,\n  },\n  city: {\n    type: String,\n    trim: true,\n  },\n  state: {\n    type: String,\n    trim: true,\n  },\n  zipCode: {\n    type: String,\n    trim: true,\n  },\n  emergencyContact: {\n    type: EmergencyContactSchema,\n  },\n  medicalHistory: {\n    type: MedicalHistorySchema,\n  },\n  insurance: {\n    type: InsuranceSchema,\n  },\n});\n\nconst UserSchema = new Schema<UserDocument>({\n  email: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true,\n  },\n  password: {\n    type: String,\n    required: true,\n    minlength: 6,\n  },\n  role: {\n    type: String,\n    enum: ['doctor', 'patient'],\n    required: true,\n  },\n  profile: {\n    type: UserProfileSchema,\n    required: true,\n  },\n  isEmailVerified: {\n    type: Boolean,\n    default: false,\n  },\n  emailVerificationToken: {\n    type: String,\n  },\n  emailVerificationExpiry: {\n    type: Date,\n  },\n  resetPasswordToken: {\n    type: String,\n  },\n  resetPasswordExpiry: {\n    type: Date,\n  },\n}, {\n  timestamps: true,\n});\n\n// Hash password before saving\nUserSchema.pre('save', async function (next) {\n  if (!this.isModified('password')) return next();\n\n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error as Error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Generate email verification token\nUserSchema.methods.generateEmailVerificationToken = function (): string {\n  const crypto = require('crypto');\n  const token = crypto.randomBytes(32).toString('hex');\n\n  this.emailVerificationToken = token;\n  this.emailVerificationExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours\n\n  return token;\n};\n\n// Generate password reset token\nUserSchema.methods.generatePasswordResetToken = function (): string {\n  const crypto = require('crypto');\n  const token = crypto.randomBytes(32).toString('hex');\n\n  this.resetPasswordToken = token;\n  this.resetPasswordExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour\n\n  return token;\n};\n\n// Verify email verification token\nUserSchema.methods.verifyEmailToken = function (token: string): boolean {\n  return this.emailVerificationToken === token &&\n         this.emailVerificationExpiry &&\n         this.emailVerificationExpiry > new Date();\n};\n\n// Verify password reset token\nUserSchema.methods.verifyPasswordResetToken = function (token: string): boolean {\n  return this.resetPasswordToken === token &&\n         this.resetPasswordExpiry &&\n         this.resetPasswordExpiry > new Date();\n};\n\n// Clean JSON output (remove sensitive fields)\nUserSchema.methods.toJSON = function () {\n  const userObject = this.toObject();\n  delete userObject.password;\n  delete userObject.resetPasswordToken;\n  delete userObject.resetPasswordExpiry;\n  delete userObject.emailVerificationToken;\n  delete userObject.emailVerificationExpiry;\n  delete userObject.__v;\n  return userObject;\n};\n\n// Indexes for better query performance\n// Note: email index is already created by unique: true\nUserSchema.index({ role: 1 });\n\nexport default mongoose.models.User || mongoose.model<UserDocument>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAaA,MAAM,yBAAyB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACxC,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;AACF;AAEA,MAAM,uBAAuB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACtC,WAAW;QACT,MAAM;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,MAAM;IACR;IACA,YAAY;QACV,MAAM;QACN,MAAM;IACR;IACA,WAAW;QACT,MAAM;QACN,MAAM;IACR;AACF;AAEA,MAAM,kBAAkB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACjC,UAAU;QACR,MAAM;QACN,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,MAAM;IACR;AACF;AAEA,MAAM,oBAAoB,IAAI,yGAAA,CAAA,SAAM,CAAc;IAChD,WAAW;QACT,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,cAAc;QACZ,MAAM;IACR;IACA,aAAa;QACX,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAQ;YAAU;YAAS;SAAoB;IACxD;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,kBAAkB;QAChB,MAAM;IACR;IACA,gBAAgB;QACd,MAAM;IACR;IACA,WAAW;QACT,MAAM;IACR;AACF;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAe;IAC1C,OAAO;QACL,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAU;SAAU;QAC3B,UAAU;IACZ;IACA,SAAS;QACP,MAAM;QACN,UAAU;IACZ;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,wBAAwB;QACtB,MAAM;IACR;IACA,yBAAyB;QACvB,MAAM;IACR;IACA,oBAAoB;QAClB,MAAM;IACR;IACA,qBAAqB;QACnB,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAgB,IAAI;IACzC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAgB,iBAAyB;IAC5E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,oCAAoC;AACpC,WAAW,OAAO,CAAC,8BAA8B,GAAG;IAClD,MAAM;IACN,MAAM,QAAQ,OAAO,WAAW,CAAC,IAAI,QAAQ,CAAC;IAE9C,IAAI,CAAC,sBAAsB,GAAG;IAC9B,IAAI,CAAC,uBAAuB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,OAAO,WAAW;IAEtF,OAAO;AACT;AAEA,gCAAgC;AAChC,WAAW,OAAO,CAAC,0BAA0B,GAAG;IAC9C,MAAM;IACN,MAAM,QAAQ,OAAO,WAAW,CAAC,IAAI,QAAQ,CAAC;IAE9C,IAAI,CAAC,kBAAkB,GAAG;IAC1B,IAAI,CAAC,mBAAmB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,OAAO,SAAS;IAE3E,OAAO;AACT;AAEA,kCAAkC;AAClC,WAAW,OAAO,CAAC,gBAAgB,GAAG,SAAU,KAAa;IAC3D,OAAO,IAAI,CAAC,sBAAsB,KAAK,SAChC,IAAI,CAAC,uBAAuB,IAC5B,IAAI,CAAC,uBAAuB,GAAG,IAAI;AAC5C;AAEA,8BAA8B;AAC9B,WAAW,OAAO,CAAC,wBAAwB,GAAG,SAAU,KAAa;IACnE,OAAO,IAAI,CAAC,kBAAkB,KAAK,SAC5B,IAAI,CAAC,mBAAmB,IACxB,IAAI,CAAC,mBAAmB,GAAG,IAAI;AACxC;AAEA,8CAA8C;AAC9C,WAAW,OAAO,CAAC,MAAM,GAAG;IAC1B,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO,WAAW,kBAAkB;IACpC,OAAO,WAAW,mBAAmB;IACrC,OAAO,WAAW,sBAAsB;IACxC,OAAO,WAAW,uBAAuB;IACzC,OAAO,WAAW,GAAG;IACrB,OAAO;AACT;AAEA,uCAAuC;AACvC,uDAAuD;AACvD,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;uCAEZ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAe,QAAQ", "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/auth.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\nimport jwt from 'jsonwebtoken';\nimport User from '@/models/User';\n\nexport async function authenticateUser(request: NextRequest) {\n  const authHeader = request.headers.get('Authorization');\n  const cookieToken = request.cookies.get('authToken')?.value;\n\n  const token = authHeader?.replace('Bearer ', '') || cookieToken;\n\n  if (!token) {\n    throw new Error('No authentication token provided');\n  }\n\n  try {\n    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;\n    const user = await User.findById(decoded.userId);\n\n    if (!user) {\n      throw new Error('User not found');\n    }\n\n    return user;\n  } catch (error) {\n    throw new Error('Invalid or expired token');\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;;;AAEO,eAAe,iBAAiB,OAAoB;IACzD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,cAAc;IAEtD,MAAM,QAAQ,YAAY,QAAQ,WAAW,OAAO;IAEpD,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;QAC5D,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,QAAQ,MAAM;QAE/C,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/api/doctors/me/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/db';\nimport Doctor from '@/models/Doctor';\nimport { authenticateUser } from '@/lib/auth';\nimport { ApiResponse } from '@/types';\nimport { DoctorWithUser } from '@/types';\n\nexport async function GET(request: NextRequest) {\n  try {\n    await connectDB();\n\n    // Authenticate user\n    let user;\n    try {\n      user = await authenticateUser(request);\n    } catch (error) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Unauthorized',\n      }, { status: 401 });\n    }\n\n    // Check if user is a doctor\n    if (user.role !== 'doctor') {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Access denied. Only doctors can access this endpoint.',\n      }, { status: 403 });\n    }\n\n    // Find doctor profile\n    const doctor = await Doctor.findOne({ userId: user._id })\n      .populate('userId', '-password')\n      .lean();\n\n    if (!doctor) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Doctor profile not found',\n      }, { status: 404 });\n    }\n\n    const doctorWithUser: DoctorWithUser = {\n      ...doctor,\n      user: doctor.userId as any,\n    };\n\n    return NextResponse.json<ApiResponse<DoctorWithUser>>({\n      success: true,\n      data: doctorWithUser,\n    });\n\n  } catch (error) {\n    console.error('Get current doctor error:', error);\n    \n    return NextResponse.json<ApiResponse>({\n      success: false,\n      error: 'Failed to fetch doctor profile',\n    }, { status: 500 });\n  }\n}\n\nexport async function PUT(request: NextRequest) {\n  try {\n    await connectDB();\n\n    // Authenticate user\n    let user;\n    try {\n      user = await authenticateUser(request);\n    } catch (error) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Unauthorized',\n      }, { status: 401 });\n    }\n\n    // Check if user is a doctor\n    if (user.role !== 'doctor') {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Access denied. Only doctors can access this endpoint.',\n      }, { status: 403 });\n    }\n\n    // Find doctor profile\n    const doctor = await Doctor.findOne({ userId: user._id });\n\n    if (!doctor) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Doctor profile not found',\n      }, { status: 404 });\n    }\n\n    const body = await request.json();\n    const {\n      specialty,\n      bio,\n      experience,\n      consultationFee,\n      location,\n      qualifications\n    } = body;\n\n    // Update doctor profile\n    const updatedDoctor = await Doctor.findByIdAndUpdate(\n      doctor._id,\n      {\n        ...(specialty && { specialty }),\n        ...(bio && { bio }),\n        ...(experience !== undefined && { experience }),\n        ...(consultationFee !== undefined && { consultationFee }),\n        ...(location && { location }),\n        ...(qualifications && { qualifications })\n      },\n      { new: true, runValidators: true }\n    ).populate('userId', '-password');\n\n    if (!updatedDoctor) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Failed to update doctor profile',\n      }, { status: 500 });\n    }\n\n    const doctorWithUser: DoctorWithUser = {\n      ...updatedDoctor.toObject(),\n      user: updatedDoctor.userId as any,\n    };\n\n    return NextResponse.json<ApiResponse<DoctorWithUser>>({\n      success: true,\n      data: doctorWithUser,\n      message: 'Doctor profile updated successfully',\n    });\n\n  } catch (error) {\n    console.error('Update current doctor error:', error);\n    \n    return NextResponse.json<ApiResponse>({\n      success: false,\n      error: 'Failed to update doctor profile',\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAIO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,oBAAoB;QACpB,IAAI;QACJ,IAAI;YACF,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,4BAA4B;QAC5B,IAAI,KAAK,IAAI,KAAK,UAAU;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,sBAAsB;QACtB,MAAM,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,OAAO,CAAC;YAAE,QAAQ,KAAK,GAAG;QAAC,GACpD,QAAQ,CAAC,UAAU,aACnB,IAAI;QAEP,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,iBAAiC;YACrC,GAAG,MAAM;YACT,MAAM,OAAO,MAAM;QACrB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAA8B;YACpD,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAE3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,oBAAoB;QACpB,IAAI;QACJ,IAAI;YACF,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,4BAA4B;QAC5B,IAAI,KAAK,IAAI,KAAK,UAAU;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,sBAAsB;QACtB,MAAM,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,OAAO,CAAC;YAAE,QAAQ,KAAK,GAAG;QAAC;QAEvD,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,SAAS,EACT,GAAG,EACH,UAAU,EACV,eAAe,EACf,QAAQ,EACR,cAAc,EACf,GAAG;QAEJ,wBAAwB;QACxB,MAAM,gBAAgB,MAAM,yHAAA,CAAA,UAAM,CAAC,iBAAiB,CAClD,OAAO,GAAG,EACV;YACE,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,GAAI,OAAO;gBAAE;YAAI,CAAC;YAClB,GAAI,eAAe,aAAa;gBAAE;YAAW,CAAC;YAC9C,GAAI,oBAAoB,aAAa;gBAAE;YAAgB,CAAC;YACxD,GAAI,YAAY;gBAAE;YAAS,CAAC;YAC5B,GAAI,kBAAkB;gBAAE;YAAe,CAAC;QAC1C,GACA;YAAE,KAAK;YAAM,eAAe;QAAK,GACjC,QAAQ,CAAC,UAAU;QAErB,IAAI,CAAC,eAAe;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,iBAAiC;YACrC,GAAG,cAAc,QAAQ,EAAE;YAC3B,MAAM,cAAc,MAAM;QAC5B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAA8B;YACpD,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAE9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}