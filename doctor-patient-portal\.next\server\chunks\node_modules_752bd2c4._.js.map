{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/safe-buffer/index.js"], "sourcesContent": ["/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */\n/* eslint-disable node/no-deprecated-api */\nvar buffer = require('buffer')\nvar Buffer = buffer.Buffer\n\n// alternative to using Object.keys for old browsers\nfunction copyProps (src, dst) {\n  for (var key in src) {\n    dst[key] = src[key]\n  }\n}\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports)\n  exports.Buffer = SafeBuffer\n}\n\nfunction SafeBuffer (arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.prototype = Object.create(Buffer.prototype)\n\n// Copy static methods from Buffer\ncopyProps(Buffer, SafeBuffer)\n\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number')\n  }\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  var buf = Buffer(size)\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding)\n    } else {\n      buf.fill(fill)\n    }\n  } else {\n    buf.fill(0)\n  }\n  return buf\n}\n\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return Buffer(size)\n}\n\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return buffer.SlowBuffer(size)\n}\n"], "names": [], "mappings": "AAAA,kFAAkF,GAClF,yCAAyC,GACzC,IAAI;AACJ,IAAI,SAAS,OAAO,MAAM;AAE1B,oDAAoD;AACpD,SAAS,UAAW,GAAG,EAAE,GAAG;IAC1B,IAAK,IAAI,OAAO,IAAK;QACnB,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;IACrB;AACF;AACA,IAAI,OAAO,IAAI,IAAI,OAAO,KAAK,IAAI,OAAO,WAAW,IAAI,OAAO,eAAe,EAAE;IAC/E,OAAO,OAAO,GAAG;AACnB,OAAO;IACL,yCAAyC;IACzC,UAAU,QAAQ;IAClB,QAAQ,MAAM,GAAG;AACnB;AAEA,SAAS,WAAY,GAAG,EAAE,gBAAgB,EAAE,MAAM;IAChD,OAAO,OAAO,KAAK,kBAAkB;AACvC;AAEA,WAAW,SAAS,GAAG,OAAO,MAAM,CAAC,OAAO,SAAS;AAErD,kCAAkC;AAClC,UAAU,QAAQ;AAElB,WAAW,IAAI,GAAG,SAAU,GAAG,EAAE,gBAAgB,EAAE,MAAM;IACvD,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,IAAI,UAAU;IACtB;IACA,OAAO,OAAO,KAAK,kBAAkB;AACvC;AAEA,WAAW,KAAK,GAAG,SAAU,IAAI,EAAE,IAAI,EAAE,QAAQ;IAC/C,IAAI,OAAO,SAAS,UAAU;QAC5B,MAAM,IAAI,UAAU;IACtB;IACA,IAAI,MAAM,OAAO;IACjB,IAAI,SAAS,WAAW;QACtB,IAAI,OAAO,aAAa,UAAU;YAChC,IAAI,IAAI,CAAC,MAAM;QACjB,OAAO;YACL,IAAI,IAAI,CAAC;QACX;IACF,OAAO;QACL,IAAI,IAAI,CAAC;IACX;IACA,OAAO;AACT;AAEA,WAAW,WAAW,GAAG,SAAU,IAAI;IACrC,IAAI,OAAO,SAAS,UAAU;QAC5B,MAAM,IAAI,UAAU;IACtB;IACA,OAAO,OAAO;AAChB;AAEA,WAAW,eAAe,GAAG,SAAU,IAAI;IACzC,IAAI,OAAO,SAAS,UAAU;QAC5B,MAAM,IAAI,UAAU;IACtB;IACA,OAAO,OAAO,UAAU,CAAC;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jws/lib/data-stream.js"], "sourcesContent": ["/*global module, process*/\nvar Buffer = require('safe-buffer').Buffer;\nvar Stream = require('stream');\nvar util = require('util');\n\nfunction DataStream(data) {\n  this.buffer = null;\n  this.writable = true;\n  this.readable = true;\n\n  // No input\n  if (!data) {\n    this.buffer = Buffer.alloc(0);\n    return this;\n  }\n\n  // Stream\n  if (typeof data.pipe === 'function') {\n    this.buffer = Buffer.alloc(0);\n    data.pipe(this);\n    return this;\n  }\n\n  // Buffer or String\n  // or Object (assumedly a passworded key)\n  if (data.length || typeof data === 'object') {\n    this.buffer = data;\n    this.writable = false;\n    process.nextTick(function () {\n      this.emit('end', data);\n      this.readable = false;\n      this.emit('close');\n    }.bind(this));\n    return this;\n  }\n\n  throw new TypeError('Unexpected data type ('+ typeof data + ')');\n}\nutil.inherits(DataStream, Stream);\n\nDataStream.prototype.write = function write(data) {\n  this.buffer = Buffer.concat([this.buffer, Buffer.from(data)]);\n  this.emit('data', data);\n};\n\nDataStream.prototype.end = function end(data) {\n  if (data)\n    this.write(data);\n  this.emit('end', data);\n  this.emit('close');\n  this.writable = false;\n  this.readable = false;\n};\n\nmodule.exports = DataStream;\n"], "names": [], "mappings": "AAAA,wBAAwB,GACxB,IAAI,SAAS,gGAAuB,MAAM;AAC1C,IAAI;AACJ,IAAI;AAEJ,SAAS,WAAW,IAAI;IACtB,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,QAAQ,GAAG;IAEhB,WAAW;IACX,IAAI,CAAC,MAAM;QACT,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK,CAAC;QAC3B,OAAO,IAAI;IACb;IAEA,SAAS;IACT,IAAI,OAAO,KAAK,IAAI,KAAK,YAAY;QACnC,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK,CAAC;QAC3B,KAAK,IAAI,CAAC,IAAI;QACd,OAAO,IAAI;IACb;IAEA,mBAAmB;IACnB,yCAAyC;IACzC,IAAI,KAAK,MAAM,IAAI,OAAO,SAAS,UAAU;QAC3C,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,GAAG;QAChB,QAAQ,QAAQ,CAAC,CAAA;YACf,IAAI,CAAC,IAAI,CAAC,OAAO;YACjB,IAAI,CAAC,QAAQ,GAAG;YAChB,IAAI,CAAC,IAAI,CAAC;QACZ,CAAA,EAAE,IAAI,CAAC,IAAI;QACX,OAAO,IAAI;IACb;IAEA,MAAM,IAAI,UAAU,2BAA0B,OAAO,OAAO;AAC9D;AACA,KAAK,QAAQ,CAAC,YAAY;AAE1B,WAAW,SAAS,CAAC,KAAK,GAAG,SAAS,MAAM,IAAI;IAC9C,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM,CAAC;QAAC,IAAI,CAAC,MAAM;QAAE,OAAO,IAAI,CAAC;KAAM;IAC5D,IAAI,CAAC,IAAI,CAAC,QAAQ;AACpB;AAEA,WAAW,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,IAAI;IAC1C,IAAI,MACF,IAAI,CAAC,KAAK,CAAC;IACb,IAAI,CAAC,IAAI,CAAC,OAAO;IACjB,IAAI,CAAC,IAAI,CAAC;IACV,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,QAAQ,GAAG;AAClB;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jws/lib/tostring.js"], "sourcesContent": ["/*global module*/\nvar Buffer = require('buffer').Buffer;\n\nmodule.exports = function toString(obj) {\n  if (typeof obj === 'string')\n    return obj;\n  if (typeof obj === 'number' || Buffer.isBuffer(obj))\n    return obj.toString();\n  return JSON.stringify(obj);\n};\n"], "names": [], "mappings": "AAAA,eAAe,GACf,IAAI,SAAS,uEAAkB,MAAM;AAErC,OAAO,OAAO,GAAG,SAAS,SAAS,GAAG;IACpC,IAAI,OAAO,QAAQ,UACjB,OAAO;IACT,IAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,CAAC,MAC7C,OAAO,IAAI,QAAQ;IACrB,OAAO,KAAK,SAAS,CAAC;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jws/lib/sign-stream.js"], "sourcesContent": ["/*global module*/\nvar Buffer = require('safe-buffer').Buffer;\nvar DataStream = require('./data-stream');\nvar jwa = require('jwa');\nvar Stream = require('stream');\nvar toString = require('./tostring');\nvar util = require('util');\n\nfunction base64url(string, encoding) {\n  return Buffer\n    .from(string, encoding)\n    .toString('base64')\n    .replace(/=/g, '')\n    .replace(/\\+/g, '-')\n    .replace(/\\//g, '_');\n}\n\nfunction jwsSecuredInput(header, payload, encoding) {\n  encoding = encoding || 'utf8';\n  var encodedHeader = base64url(toString(header), 'binary');\n  var encodedPayload = base64url(toString(payload), encoding);\n  return util.format('%s.%s', encodedHeader, encodedPayload);\n}\n\nfunction jwsSign(opts) {\n  var header = opts.header;\n  var payload = opts.payload;\n  var secretOrKey = opts.secret || opts.privateKey;\n  var encoding = opts.encoding;\n  var algo = jwa(header.alg);\n  var securedInput = jwsSecuredInput(header, payload, encoding);\n  var signature = algo.sign(securedInput, secretOrKey);\n  return util.format('%s.%s', securedInput, signature);\n}\n\nfunction SignStream(opts) {\n  var secret = opts.secret||opts.privateKey||opts.key;\n  var secretStream = new DataStream(secret);\n  this.readable = true;\n  this.header = opts.header;\n  this.encoding = opts.encoding;\n  this.secret = this.privateKey = this.key = secretStream;\n  this.payload = new DataStream(opts.payload);\n  this.secret.once('close', function () {\n    if (!this.payload.writable && this.readable)\n      this.sign();\n  }.bind(this));\n\n  this.payload.once('close', function () {\n    if (!this.secret.writable && this.readable)\n      this.sign();\n  }.bind(this));\n}\nutil.inherits(SignStream, Stream);\n\nSignStream.prototype.sign = function sign() {\n  try {\n    var signature = jwsSign({\n      header: this.header,\n      payload: this.payload.buffer,\n      secret: this.secret.buffer,\n      encoding: this.encoding\n    });\n    this.emit('done', signature);\n    this.emit('data', signature);\n    this.emit('end');\n    this.readable = false;\n    return signature;\n  } catch (e) {\n    this.readable = false;\n    this.emit('error', e);\n    this.emit('close');\n  }\n};\n\nSignStream.sign = jwsSign;\n\nmodule.exports = SignStream;\n"], "names": [], "mappings": "AAAA,eAAe,GACf,IAAI,SAAS,gGAAuB,MAAM;AAC1C,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,UAAU,MAAM,EAAE,QAAQ;IACjC,OAAO,OACJ,IAAI,CAAC,QAAQ,UACb,QAAQ,CAAC,UACT,OAAO,CAAC,MAAM,IACd,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,OAAO;AACpB;AAEA,SAAS,gBAAgB,MAAM,EAAE,OAAO,EAAE,QAAQ;IAChD,WAAW,YAAY;IACvB,IAAI,gBAAgB,UAAU,SAAS,SAAS;IAChD,IAAI,iBAAiB,UAAU,SAAS,UAAU;IAClD,OAAO,KAAK,MAAM,CAAC,SAAS,eAAe;AAC7C;AAEA,SAAS,QAAQ,IAAI;IACnB,IAAI,SAAS,KAAK,MAAM;IACxB,IAAI,UAAU,KAAK,OAAO;IAC1B,IAAI,cAAc,KAAK,MAAM,IAAI,KAAK,UAAU;IAChD,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI,OAAO,IAAI,OAAO,GAAG;IACzB,IAAI,eAAe,gBAAgB,QAAQ,SAAS;IACpD,IAAI,YAAY,KAAK,IAAI,CAAC,cAAc;IACxC,OAAO,KAAK,MAAM,CAAC,SAAS,cAAc;AAC5C;AAEA,SAAS,WAAW,IAAI;IACtB,IAAI,SAAS,KAAK,MAAM,IAAE,KAAK,UAAU,IAAE,KAAK,GAAG;IACnD,IAAI,eAAe,IAAI,WAAW;IAClC,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;IACzB,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ;IAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,GAAG;IAC3C,IAAI,CAAC,OAAO,GAAG,IAAI,WAAW,KAAK,OAAO;IAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAA;QACxB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EACzC,IAAI,CAAC,IAAI;IACb,CAAA,EAAE,IAAI,CAAC,IAAI;IAEX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAA;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EACxC,IAAI,CAAC,IAAI;IACb,CAAA,EAAE,IAAI,CAAC,IAAI;AACb;AACA,KAAK,QAAQ,CAAC,YAAY;AAE1B,WAAW,SAAS,CAAC,IAAI,GAAG,SAAS;IACnC,IAAI;QACF,IAAI,YAAY,QAAQ;YACtB,QAAQ,IAAI,CAAC,MAAM;YACnB,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM;YAC5B,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM;YAC1B,UAAU,IAAI,CAAC,QAAQ;QACzB;QACA,IAAI,CAAC,IAAI,CAAC,QAAQ;QAClB,IAAI,CAAC,IAAI,CAAC,QAAQ;QAClB,IAAI,CAAC,IAAI,CAAC;QACV,IAAI,CAAC,QAAQ,GAAG;QAChB,OAAO;IACT,EAAE,OAAO,GAAG;QACV,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,IAAI,CAAC,SAAS;QACnB,IAAI,CAAC,IAAI,CAAC;IACZ;AACF;AAEA,WAAW,IAAI,GAAG;AAElB,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jws/lib/verify-stream.js"], "sourcesContent": ["/*global module*/\nvar Buffer = require('safe-buffer').Buffer;\nvar DataStream = require('./data-stream');\nvar jwa = require('jwa');\nvar Stream = require('stream');\nvar toString = require('./tostring');\nvar util = require('util');\nvar JWS_REGEX = /^[a-zA-Z0-9\\-_]+?\\.[a-zA-Z0-9\\-_]+?\\.([a-zA-Z0-9\\-_]+)?$/;\n\nfunction isObject(thing) {\n  return Object.prototype.toString.call(thing) === '[object Object]';\n}\n\nfunction safeJsonParse(thing) {\n  if (isObject(thing))\n    return thing;\n  try { return JSON.parse(thing); }\n  catch (e) { return undefined; }\n}\n\nfunction headerFromJWS(jwsSig) {\n  var encodedHeader = jwsSig.split('.', 1)[0];\n  return safeJsonParse(Buffer.from(encodedHeader, 'base64').toString('binary'));\n}\n\nfunction securedInputFromJWS(jwsSig) {\n  return jwsSig.split('.', 2).join('.');\n}\n\nfunction signatureFromJWS(jwsSig) {\n  return jwsSig.split('.')[2];\n}\n\nfunction payloadFromJWS(jwsSig, encoding) {\n  encoding = encoding || 'utf8';\n  var payload = jwsSig.split('.')[1];\n  return Buffer.from(payload, 'base64').toString(encoding);\n}\n\nfunction isValidJws(string) {\n  return JWS_REGEX.test(string) && !!headerFromJWS(string);\n}\n\nfunction jwsVerify(jwsSig, algorithm, secretOrKey) {\n  if (!algorithm) {\n    var err = new Error(\"Missing algorithm parameter for jws.verify\");\n    err.code = \"MISSING_ALGORITHM\";\n    throw err;\n  }\n  jwsSig = toString(jwsSig);\n  var signature = signatureFromJWS(jwsSig);\n  var securedInput = securedInputFromJWS(jwsSig);\n  var algo = jwa(algorithm);\n  return algo.verify(securedInput, signature, secretOrKey);\n}\n\nfunction jwsDecode(jwsSig, opts) {\n  opts = opts || {};\n  jwsSig = toString(jwsSig);\n\n  if (!isValidJws(jwsSig))\n    return null;\n\n  var header = headerFromJWS(jwsSig);\n\n  if (!header)\n    return null;\n\n  var payload = payloadFromJWS(jwsSig);\n  if (header.typ === 'JWT' || opts.json)\n    payload = JSON.parse(payload, opts.encoding);\n\n  return {\n    header: header,\n    payload: payload,\n    signature: signatureFromJWS(jwsSig)\n  };\n}\n\nfunction VerifyStream(opts) {\n  opts = opts || {};\n  var secretOrKey = opts.secret||opts.publicKey||opts.key;\n  var secretStream = new DataStream(secretOrKey);\n  this.readable = true;\n  this.algorithm = opts.algorithm;\n  this.encoding = opts.encoding;\n  this.secret = this.publicKey = this.key = secretStream;\n  this.signature = new DataStream(opts.signature);\n  this.secret.once('close', function () {\n    if (!this.signature.writable && this.readable)\n      this.verify();\n  }.bind(this));\n\n  this.signature.once('close', function () {\n    if (!this.secret.writable && this.readable)\n      this.verify();\n  }.bind(this));\n}\nutil.inherits(VerifyStream, Stream);\nVerifyStream.prototype.verify = function verify() {\n  try {\n    var valid = jwsVerify(this.signature.buffer, this.algorithm, this.key.buffer);\n    var obj = jwsDecode(this.signature.buffer, this.encoding);\n    this.emit('done', valid, obj);\n    this.emit('data', valid);\n    this.emit('end');\n    this.readable = false;\n    return valid;\n  } catch (e) {\n    this.readable = false;\n    this.emit('error', e);\n    this.emit('close');\n  }\n};\n\nVerifyStream.decode = jwsDecode;\nVerifyStream.isValid = isValidJws;\nVerifyStream.verify = jwsVerify;\n\nmodule.exports = VerifyStream;\n"], "names": [], "mappings": "AAAA,eAAe,GACf,IAAI,SAAS,gGAAuB,MAAM;AAC1C,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,YAAY;AAEhB,SAAS,SAAS,KAAK;IACrB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;AACnD;AAEA,SAAS,cAAc,KAAK;IAC1B,IAAI,SAAS,QACX,OAAO;IACT,IAAI;QAAE,OAAO,KAAK,KAAK,CAAC;IAAQ,EAChC,OAAO,GAAG;QAAE,OAAO;IAAW;AAChC;AAEA,SAAS,cAAc,MAAM;IAC3B,IAAI,gBAAgB,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;IAC3C,OAAO,cAAc,OAAO,IAAI,CAAC,eAAe,UAAU,QAAQ,CAAC;AACrE;AAEA,SAAS,oBAAoB,MAAM;IACjC,OAAO,OAAO,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;AACnC;AAEA,SAAS,iBAAiB,MAAM;IAC9B,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;AAC7B;AAEA,SAAS,eAAe,MAAM,EAAE,QAAQ;IACtC,WAAW,YAAY;IACvB,IAAI,UAAU,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;IAClC,OAAO,OAAO,IAAI,CAAC,SAAS,UAAU,QAAQ,CAAC;AACjD;AAEA,SAAS,WAAW,MAAM;IACxB,OAAO,UAAU,IAAI,CAAC,WAAW,CAAC,CAAC,cAAc;AACnD;AAEA,SAAS,UAAU,MAAM,EAAE,SAAS,EAAE,WAAW;IAC/C,IAAI,CAAC,WAAW;QACd,IAAI,MAAM,IAAI,MAAM;QACpB,IAAI,IAAI,GAAG;QACX,MAAM;IACR;IACA,SAAS,SAAS;IAClB,IAAI,YAAY,iBAAiB;IACjC,IAAI,eAAe,oBAAoB;IACvC,IAAI,OAAO,IAAI;IACf,OAAO,KAAK,MAAM,CAAC,cAAc,WAAW;AAC9C;AAEA,SAAS,UAAU,MAAM,EAAE,IAAI;IAC7B,OAAO,QAAQ,CAAC;IAChB,SAAS,SAAS;IAElB,IAAI,CAAC,WAAW,SACd,OAAO;IAET,IAAI,SAAS,cAAc;IAE3B,IAAI,CAAC,QACH,OAAO;IAET,IAAI,UAAU,eAAe;IAC7B,IAAI,OAAO,GAAG,KAAK,SAAS,KAAK,IAAI,EACnC,UAAU,KAAK,KAAK,CAAC,SAAS,KAAK,QAAQ;IAE7C,OAAO;QACL,QAAQ;QACR,SAAS;QACT,WAAW,iBAAiB;IAC9B;AACF;AAEA,SAAS,aAAa,IAAI;IACxB,OAAO,QAAQ,CAAC;IAChB,IAAI,cAAc,KAAK,MAAM,IAAE,KAAK,SAAS,IAAE,KAAK,GAAG;IACvD,IAAI,eAAe,IAAI,WAAW;IAClC,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,SAAS,GAAG,KAAK,SAAS;IAC/B,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ;IAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,GAAG;IAC1C,IAAI,CAAC,SAAS,GAAG,IAAI,WAAW,KAAK,SAAS;IAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAA;QACxB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAC3C,IAAI,CAAC,MAAM;IACf,CAAA,EAAE,IAAI,CAAC,IAAI;IAEX,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAA;QAC3B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EACxC,IAAI,CAAC,MAAM;IACf,CAAA,EAAE,IAAI,CAAC,IAAI;AACb;AACA,KAAK,QAAQ,CAAC,cAAc;AAC5B,aAAa,SAAS,CAAC,MAAM,GAAG,SAAS;IACvC,IAAI;QACF,IAAI,QAAQ,UAAU,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM;QAC5E,IAAI,MAAM,UAAU,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ;QACxD,IAAI,CAAC,IAAI,CAAC,QAAQ,OAAO;QACzB,IAAI,CAAC,IAAI,CAAC,QAAQ;QAClB,IAAI,CAAC,IAAI,CAAC;QACV,IAAI,CAAC,QAAQ,GAAG;QAChB,OAAO;IACT,EAAE,OAAO,GAAG;QACV,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,IAAI,CAAC,SAAS;QACnB,IAAI,CAAC,IAAI,CAAC;IACZ;AACF;AAEA,aAAa,MAAM,GAAG;AACtB,aAAa,OAAO,GAAG;AACvB,aAAa,MAAM,GAAG;AAEtB,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jws/index.js"], "sourcesContent": ["/*global exports*/\nvar SignStream = require('./lib/sign-stream');\nvar VerifyStream = require('./lib/verify-stream');\n\nvar ALGORITHMS = [\n  'HS256', 'HS384', 'HS512',\n  'RS256', 'RS384', 'RS512',\n  'PS256', 'PS384', 'PS512',\n  'ES256', 'ES384', 'ES512'\n];\n\nexports.ALGORITHMS = ALGORITHMS;\nexports.sign = SignStream.sign;\nexports.verify = VerifyStream.verify;\nexports.decode = VerifyStream.decode;\nexports.isValid = VerifyStream.isValid;\nexports.createSign = function createSign(opts) {\n  return new SignStream(opts);\n};\nexports.createVerify = function createVerify(opts) {\n  return new VerifyStream(opts);\n};\n"], "names": [], "mappings": "AAAA,gBAAgB,GAChB,IAAI;AACJ,IAAI;AAEJ,IAAI,aAAa;IACf;IAAS;IAAS;IAClB;IAAS;IAAS;IAClB;IAAS;IAAS;IAClB;IAAS;IAAS;CACnB;AAED,QAAQ,UAAU,GAAG;AACrB,QAAQ,IAAI,GAAG,WAAW,IAAI;AAC9B,QAAQ,MAAM,GAAG,aAAa,MAAM;AACpC,QAAQ,MAAM,GAAG,aAAa,MAAM;AACpC,QAAQ,OAAO,GAAG,aAAa,OAAO;AACtC,QAAQ,UAAU,GAAG,SAAS,WAAW,IAAI;IAC3C,OAAO,IAAI,WAAW;AACxB;AACA,QAAQ,YAAY,GAAG,SAAS,aAAa,IAAI;IAC/C,OAAO,IAAI,aAAa;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/ecdsa-sig-formatter/src/param-bytes-for-alg.js"], "sourcesContent": ["'use strict';\n\nfunction getParamSize(keySize) {\n\tvar result = ((keySize / 8) | 0) + (keySize % 8 === 0 ? 0 : 1);\n\treturn result;\n}\n\nvar paramBytesForAlg = {\n\tES256: getParamSize(256),\n\tES384: getParamSize(384),\n\tES512: getParamSize(521)\n};\n\nfunction getParamBytesForAlg(alg) {\n\tvar paramBytes = paramBytesForAlg[alg];\n\tif (paramBytes) {\n\t\treturn paramBytes;\n\t}\n\n\tthrow new Error('Unknown algorithm \"' + alg + '\"');\n}\n\nmodule.exports = getParamBytesForAlg;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,aAAa,OAAO;IAC5B,IAAI,SAAS,CAAC,AAAC,UAAU,IAAK,CAAC,IAAI,CAAC,UAAU,MAAM,IAAI,IAAI,CAAC;IAC7D,OAAO;AACR;AAEA,IAAI,mBAAmB;IACtB,OAAO,aAAa;IACpB,OAAO,aAAa;IACpB,OAAO,aAAa;AACrB;AAEA,SAAS,oBAAoB,GAAG;IAC/B,IAAI,aAAa,gBAAgB,CAAC,IAAI;IACtC,IAAI,YAAY;QACf,OAAO;IACR;IAEA,MAAM,IAAI,MAAM,wBAAwB,MAAM;AAC/C;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/ecdsa-sig-formatter/src/ecdsa-sig-formatter.js"], "sourcesContent": ["'use strict';\n\nvar Buffer = require('safe-buffer').Buffer;\n\nvar getParamBytesForAlg = require('./param-bytes-for-alg');\n\nvar MAX_OCTET = 0x80,\n\tCLASS_UNIVERSAL = 0,\n\tPRIMITIVE_BIT = 0x20,\n\tTAG_SEQ = 0x10,\n\tTAG_INT = 0x02,\n\tENCODED_TAG_SEQ = (TAG_SEQ | PRIMITIVE_BIT) | (CLASS_UNIVERSAL << 6),\n\tENCODED_TAG_INT = TAG_INT | (CLASS_UNIVERSAL << 6);\n\nfunction base64Url(base64) {\n\treturn base64\n\t\t.replace(/=/g, '')\n\t\t.replace(/\\+/g, '-')\n\t\t.replace(/\\//g, '_');\n}\n\nfunction signatureAsBuffer(signature) {\n\tif (Buffer.isBuffer(signature)) {\n\t\treturn signature;\n\t} else if ('string' === typeof signature) {\n\t\treturn Buffer.from(signature, 'base64');\n\t}\n\n\tthrow new TypeError('ECDSA signature must be a Base64 string or a Buffer');\n}\n\nfunction derToJose(signature, alg) {\n\tsignature = signatureAsBuffer(signature);\n\tvar paramBytes = getParamBytesForAlg(alg);\n\n\t// the DER encoded param should at most be the param size, plus a padding\n\t// zero, since due to being a signed integer\n\tvar maxEncodedParamLength = paramBytes + 1;\n\n\tvar inputLength = signature.length;\n\n\tvar offset = 0;\n\tif (signature[offset++] !== ENCODED_TAG_SEQ) {\n\t\tthrow new Error('Could not find expected \"seq\"');\n\t}\n\n\tvar seqLength = signature[offset++];\n\tif (seqLength === (MAX_OCTET | 1)) {\n\t\tseqLength = signature[offset++];\n\t}\n\n\tif (inputLength - offset < seqLength) {\n\t\tthrow new Error('\"seq\" specified length of \"' + seqLength + '\", only \"' + (inputLength - offset) + '\" remaining');\n\t}\n\n\tif (signature[offset++] !== ENCODED_TAG_INT) {\n\t\tthrow new Error('Could not find expected \"int\" for \"r\"');\n\t}\n\n\tvar rLength = signature[offset++];\n\n\tif (inputLength - offset - 2 < rLength) {\n\t\tthrow new Error('\"r\" specified length of \"' + rLength + '\", only \"' + (inputLength - offset - 2) + '\" available');\n\t}\n\n\tif (maxEncodedParamLength < rLength) {\n\t\tthrow new Error('\"r\" specified length of \"' + rLength + '\", max of \"' + maxEncodedParamLength + '\" is acceptable');\n\t}\n\n\tvar rOffset = offset;\n\toffset += rLength;\n\n\tif (signature[offset++] !== ENCODED_TAG_INT) {\n\t\tthrow new Error('Could not find expected \"int\" for \"s\"');\n\t}\n\n\tvar sLength = signature[offset++];\n\n\tif (inputLength - offset !== sLength) {\n\t\tthrow new Error('\"s\" specified length of \"' + sLength + '\", expected \"' + (inputLength - offset) + '\"');\n\t}\n\n\tif (maxEncodedParamLength < sLength) {\n\t\tthrow new Error('\"s\" specified length of \"' + sLength + '\", max of \"' + maxEncodedParamLength + '\" is acceptable');\n\t}\n\n\tvar sOffset = offset;\n\toffset += sLength;\n\n\tif (offset !== inputLength) {\n\t\tthrow new Error('Expected to consume entire buffer, but \"' + (inputLength - offset) + '\" bytes remain');\n\t}\n\n\tvar rPadding = paramBytes - rLength,\n\t\tsPadding = paramBytes - sLength;\n\n\tvar dst = Buffer.allocUnsafe(rPadding + rLength + sPadding + sLength);\n\n\tfor (offset = 0; offset < rPadding; ++offset) {\n\t\tdst[offset] = 0;\n\t}\n\tsignature.copy(dst, offset, rOffset + Math.max(-rPadding, 0), rOffset + rLength);\n\n\toffset = paramBytes;\n\n\tfor (var o = offset; offset < o + sPadding; ++offset) {\n\t\tdst[offset] = 0;\n\t}\n\tsignature.copy(dst, offset, sOffset + Math.max(-sPadding, 0), sOffset + sLength);\n\n\tdst = dst.toString('base64');\n\tdst = base64Url(dst);\n\n\treturn dst;\n}\n\nfunction countPadding(buf, start, stop) {\n\tvar padding = 0;\n\twhile (start + padding < stop && buf[start + padding] === 0) {\n\t\t++padding;\n\t}\n\n\tvar needsSign = buf[start + padding] >= MAX_OCTET;\n\tif (needsSign) {\n\t\t--padding;\n\t}\n\n\treturn padding;\n}\n\nfunction joseToDer(signature, alg) {\n\tsignature = signatureAsBuffer(signature);\n\tvar paramBytes = getParamBytesForAlg(alg);\n\n\tvar signatureBytes = signature.length;\n\tif (signatureBytes !== paramBytes * 2) {\n\t\tthrow new TypeError('\"' + alg + '\" signatures must be \"' + paramBytes * 2 + '\" bytes, saw \"' + signatureBytes + '\"');\n\t}\n\n\tvar rPadding = countPadding(signature, 0, paramBytes);\n\tvar sPadding = countPadding(signature, paramBytes, signature.length);\n\tvar rLength = paramBytes - rPadding;\n\tvar sLength = paramBytes - sPadding;\n\n\tvar rsBytes = 1 + 1 + rLength + 1 + 1 + sLength;\n\n\tvar shortLength = rsBytes < MAX_OCTET;\n\n\tvar dst = Buffer.allocUnsafe((shortLength ? 2 : 3) + rsBytes);\n\n\tvar offset = 0;\n\tdst[offset++] = ENCODED_TAG_SEQ;\n\tif (shortLength) {\n\t\t// Bit 8 has value \"0\"\n\t\t// bits 7-1 give the length.\n\t\tdst[offset++] = rsBytes;\n\t} else {\n\t\t// Bit 8 of first octet has value \"1\"\n\t\t// bits 7-1 give the number of additional length octets.\n\t\tdst[offset++] = MAX_OCTET\t| 1;\n\t\t// length, base 256\n\t\tdst[offset++] = rsBytes & 0xff;\n\t}\n\tdst[offset++] = ENCODED_TAG_INT;\n\tdst[offset++] = rLength;\n\tif (rPadding < 0) {\n\t\tdst[offset++] = 0;\n\t\toffset += signature.copy(dst, offset, 0, paramBytes);\n\t} else {\n\t\toffset += signature.copy(dst, offset, rPadding, paramBytes);\n\t}\n\tdst[offset++] = ENCODED_TAG_INT;\n\tdst[offset++] = sLength;\n\tif (sPadding < 0) {\n\t\tdst[offset++] = 0;\n\t\tsignature.copy(dst, offset, paramBytes);\n\t} else {\n\t\tsignature.copy(dst, offset, paramBytes + sPadding);\n\t}\n\n\treturn dst;\n}\n\nmodule.exports = {\n\tderToJose: derToJose,\n\tjoseToDer: joseToDer\n};\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,SAAS,gGAAuB,MAAM;AAE1C,IAAI;AAEJ,IAAI,YAAY,MACf,kBAAkB,GAClB,gBAAgB,MAChB,UAAU,MACV,UAAU,MACV,kBAAkB,AAAC,UAAU,gBAAkB,mBAAmB,GAClE,kBAAkB,UAAW,mBAAmB;AAEjD,SAAS,UAAU,MAAM;IACxB,OAAO,OACL,OAAO,CAAC,MAAM,IACd,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,OAAO;AAClB;AAEA,SAAS,kBAAkB,SAAS;IACnC,IAAI,OAAO,QAAQ,CAAC,YAAY;QAC/B,OAAO;IACR,OAAO,IAAI,aAAa,OAAO,WAAW;QACzC,OAAO,OAAO,IAAI,CAAC,WAAW;IAC/B;IAEA,MAAM,IAAI,UAAU;AACrB;AAEA,SAAS,UAAU,SAAS,EAAE,GAAG;IAChC,YAAY,kBAAkB;IAC9B,IAAI,aAAa,oBAAoB;IAErC,yEAAyE;IACzE,4CAA4C;IAC5C,IAAI,wBAAwB,aAAa;IAEzC,IAAI,cAAc,UAAU,MAAM;IAElC,IAAI,SAAS;IACb,IAAI,SAAS,CAAC,SAAS,KAAK,iBAAiB;QAC5C,MAAM,IAAI,MAAM;IACjB;IAEA,IAAI,YAAY,SAAS,CAAC,SAAS;IACnC,IAAI,cAAc,CAAC,YAAY,CAAC,GAAG;QAClC,YAAY,SAAS,CAAC,SAAS;IAChC;IAEA,IAAI,cAAc,SAAS,WAAW;QACrC,MAAM,IAAI,MAAM,gCAAgC,YAAY,cAAc,CAAC,cAAc,MAAM,IAAI;IACpG;IAEA,IAAI,SAAS,CAAC,SAAS,KAAK,iBAAiB;QAC5C,MAAM,IAAI,MAAM;IACjB;IAEA,IAAI,UAAU,SAAS,CAAC,SAAS;IAEjC,IAAI,cAAc,SAAS,IAAI,SAAS;QACvC,MAAM,IAAI,MAAM,8BAA8B,UAAU,cAAc,CAAC,cAAc,SAAS,CAAC,IAAI;IACpG;IAEA,IAAI,wBAAwB,SAAS;QACpC,MAAM,IAAI,MAAM,8BAA8B,UAAU,gBAAgB,wBAAwB;IACjG;IAEA,IAAI,UAAU;IACd,UAAU;IAEV,IAAI,SAAS,CAAC,SAAS,KAAK,iBAAiB;QAC5C,MAAM,IAAI,MAAM;IACjB;IAEA,IAAI,UAAU,SAAS,CAAC,SAAS;IAEjC,IAAI,cAAc,WAAW,SAAS;QACrC,MAAM,IAAI,MAAM,8BAA8B,UAAU,kBAAkB,CAAC,cAAc,MAAM,IAAI;IACpG;IAEA,IAAI,wBAAwB,SAAS;QACpC,MAAM,IAAI,MAAM,8BAA8B,UAAU,gBAAgB,wBAAwB;IACjG;IAEA,IAAI,UAAU;IACd,UAAU;IAEV,IAAI,WAAW,aAAa;QAC3B,MAAM,IAAI,MAAM,6CAA6C,CAAC,cAAc,MAAM,IAAI;IACvF;IAEA,IAAI,WAAW,aAAa,SAC3B,WAAW,aAAa;IAEzB,IAAI,MAAM,OAAO,WAAW,CAAC,WAAW,UAAU,WAAW;IAE7D,IAAK,SAAS,GAAG,SAAS,UAAU,EAAE,OAAQ;QAC7C,GAAG,CAAC,OAAO,GAAG;IACf;IACA,UAAU,IAAI,CAAC,KAAK,QAAQ,UAAU,KAAK,GAAG,CAAC,CAAC,UAAU,IAAI,UAAU;IAExE,SAAS;IAET,IAAK,IAAI,IAAI,QAAQ,SAAS,IAAI,UAAU,EAAE,OAAQ;QACrD,GAAG,CAAC,OAAO,GAAG;IACf;IACA,UAAU,IAAI,CAAC,KAAK,QAAQ,UAAU,KAAK,GAAG,CAAC,CAAC,UAAU,IAAI,UAAU;IAExE,MAAM,IAAI,QAAQ,CAAC;IACnB,MAAM,UAAU;IAEhB,OAAO;AACR;AAEA,SAAS,aAAa,GAAG,EAAE,KAAK,EAAE,IAAI;IACrC,IAAI,UAAU;IACd,MAAO,QAAQ,UAAU,QAAQ,GAAG,CAAC,QAAQ,QAAQ,KAAK,EAAG;QAC5D,EAAE;IACH;IAEA,IAAI,YAAY,GAAG,CAAC,QAAQ,QAAQ,IAAI;IACxC,IAAI,WAAW;QACd,EAAE;IACH;IAEA,OAAO;AACR;AAEA,SAAS,UAAU,SAAS,EAAE,GAAG;IAChC,YAAY,kBAAkB;IAC9B,IAAI,aAAa,oBAAoB;IAErC,IAAI,iBAAiB,UAAU,MAAM;IACrC,IAAI,mBAAmB,aAAa,GAAG;QACtC,MAAM,IAAI,UAAU,MAAM,MAAM,2BAA2B,aAAa,IAAI,mBAAmB,iBAAiB;IACjH;IAEA,IAAI,WAAW,aAAa,WAAW,GAAG;IAC1C,IAAI,WAAW,aAAa,WAAW,YAAY,UAAU,MAAM;IACnE,IAAI,UAAU,aAAa;IAC3B,IAAI,UAAU,aAAa;IAE3B,IAAI,UAAU,IAAI,IAAI,UAAU,IAAI,IAAI;IAExC,IAAI,cAAc,UAAU;IAE5B,IAAI,MAAM,OAAO,WAAW,CAAC,CAAC,cAAc,IAAI,CAAC,IAAI;IAErD,IAAI,SAAS;IACb,GAAG,CAAC,SAAS,GAAG;IAChB,IAAI,aAAa;QAChB,sBAAsB;QACtB,4BAA4B;QAC5B,GAAG,CAAC,SAAS,GAAG;IACjB,OAAO;QACN,qCAAqC;QACrC,wDAAwD;QACxD,GAAG,CAAC,SAAS,GAAG,YAAY;QAC5B,mBAAmB;QACnB,GAAG,CAAC,SAAS,GAAG,UAAU;IAC3B;IACA,GAAG,CAAC,SAAS,GAAG;IAChB,GAAG,CAAC,SAAS,GAAG;IAChB,IAAI,WAAW,GAAG;QACjB,GAAG,CAAC,SAAS,GAAG;QAChB,UAAU,UAAU,IAAI,CAAC,KAAK,QAAQ,GAAG;IAC1C,OAAO;QACN,UAAU,UAAU,IAAI,CAAC,KAAK,QAAQ,UAAU;IACjD;IACA,GAAG,CAAC,SAAS,GAAG;IAChB,GAAG,CAAC,SAAS,GAAG;IAChB,IAAI,WAAW,GAAG;QACjB,GAAG,CAAC,SAAS,GAAG;QAChB,UAAU,IAAI,CAAC,KAAK,QAAQ;IAC7B,OAAO;QACN,UAAU,IAAI,CAAC,KAAK,QAAQ,aAAa;IAC1C;IAEA,OAAO;AACR;AAEA,OAAO,OAAO,GAAG;IAChB,WAAW;IACX,WAAW;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/buffer-equal-constant-time/index.js"], "sourcesContent": ["/*jshint node:true */\n'use strict';\nvar Buffer = require('buffer').Buffer; // browserify\nvar SlowBuffer = require('buffer').SlowBuffer;\n\nmodule.exports = bufferEq;\n\nfunction bufferEq(a, b) {\n\n  // shortcutting on type is necessary for correctness\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    return false;\n  }\n\n  // buffer sizes should be well-known information, so despite this\n  // shortcutting, it doesn't leak any information about the *contents* of the\n  // buffers.\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  var c = 0;\n  for (var i = 0; i < a.length; i++) {\n    /*jshint bitwise:false */\n    c |= a[i] ^ b[i]; // XOR\n  }\n  return c === 0;\n}\n\nbufferEq.install = function() {\n  Buffer.prototype.equal = SlowBuffer.prototype.equal = function equal(that) {\n    return bufferEq(this, that);\n  };\n};\n\nvar origBufEqual = Buffer.prototype.equal;\nvar origSlowBufEqual = SlowBuffer.prototype.equal;\nbufferEq.restore = function() {\n  Buffer.prototype.equal = origBufEqual;\n  SlowBuffer.prototype.equal = origSlowBufEqual;\n};\n"], "names": [], "mappings": "AAAA,mBAAmB,GACnB;AACA,IAAI,SAAS,uEAAkB,MAAM,EAAE,aAAa;AACpD,IAAI,aAAa,uEAAkB,UAAU;AAE7C,OAAO,OAAO,GAAG;AAEjB,SAAS,SAAS,CAAC,EAAE,CAAC;IAEpB,oDAAoD;IACpD,IAAI,CAAC,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,QAAQ,CAAC,IAAI;QAC9C,OAAO;IACT;IAEA,iEAAiE;IACjE,4EAA4E;IAC5E,WAAW;IACX,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE;QACzB,OAAO;IACT;IAEA,IAAI,IAAI;IACR,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK;QACjC,uBAAuB,GACvB,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,MAAM;IAC1B;IACA,OAAO,MAAM;AACf;AAEA,SAAS,OAAO,GAAG;IACjB,OAAO,SAAS,CAAC,KAAK,GAAG,WAAW,SAAS,CAAC,KAAK,GAAG,SAAS,MAAM,IAAI;QACvE,OAAO,SAAS,IAAI,EAAE;IACxB;AACF;AAEA,IAAI,eAAe,OAAO,SAAS,CAAC,KAAK;AACzC,IAAI,mBAAmB,WAAW,SAAS,CAAC,KAAK;AACjD,SAAS,OAAO,GAAG;IACjB,OAAO,SAAS,CAAC,KAAK,GAAG;IACzB,WAAW,SAAS,CAAC,KAAK,GAAG;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jwa/index.js"], "sourcesContent": ["var Buffer = require('safe-buffer').Buffer;\nvar crypto = require('crypto');\nvar formatEcdsa = require('ecdsa-sig-formatter');\nvar util = require('util');\n\nvar MSG_INVALID_ALGORITHM = '\"%s\" is not a valid algorithm.\\n  Supported algorithms are:\\n  \"HS256\", \"HS384\", \"HS512\", \"RS256\", \"RS384\", \"RS512\", \"PS256\", \"PS384\", \"PS512\", \"ES256\", \"ES384\", \"ES512\" and \"none\".'\nvar MSG_INVALID_SECRET = 'secret must be a string or buffer';\nvar MSG_INVALID_VERIFIER_KEY = 'key must be a string or a buffer';\nvar MSG_INVALID_SIGNER_KEY = 'key must be a string, a buffer or an object';\n\nvar supportsKeyObjects = typeof crypto.createPublicKey === 'function';\nif (supportsKeyObjects) {\n  MSG_INVALID_VERIFIER_KEY += ' or a KeyObject';\n  MSG_INVALID_SECRET += 'or a KeyObject';\n}\n\nfunction checkIsPublicKey(key) {\n  if (Buffer.isBuffer(key)) {\n    return;\n  }\n\n  if (typeof key === 'string') {\n    return;\n  }\n\n  if (!supportsKeyObjects) {\n    throw typeError(MSG_INVALID_VERIFIER_KEY);\n  }\n\n  if (typeof key !== 'object') {\n    throw typeError(MSG_INVALID_VERIFIER_KEY);\n  }\n\n  if (typeof key.type !== 'string') {\n    throw typeError(MSG_INVALID_VERIFIER_KEY);\n  }\n\n  if (typeof key.asymmetricKeyType !== 'string') {\n    throw typeError(MSG_INVALID_VERIFIER_KEY);\n  }\n\n  if (typeof key.export !== 'function') {\n    throw typeError(MSG_INVALID_VERIFIER_KEY);\n  }\n};\n\nfunction checkIsPrivateKey(key) {\n  if (Buffer.isBuffer(key)) {\n    return;\n  }\n\n  if (typeof key === 'string') {\n    return;\n  }\n\n  if (typeof key === 'object') {\n    return;\n  }\n\n  throw typeError(MSG_INVALID_SIGNER_KEY);\n};\n\nfunction checkIsSecretKey(key) {\n  if (Buffer.isBuffer(key)) {\n    return;\n  }\n\n  if (typeof key === 'string') {\n    return key;\n  }\n\n  if (!supportsKeyObjects) {\n    throw typeError(MSG_INVALID_SECRET);\n  }\n\n  if (typeof key !== 'object') {\n    throw typeError(MSG_INVALID_SECRET);\n  }\n\n  if (key.type !== 'secret') {\n    throw typeError(MSG_INVALID_SECRET);\n  }\n\n  if (typeof key.export !== 'function') {\n    throw typeError(MSG_INVALID_SECRET);\n  }\n}\n\nfunction fromBase64(base64) {\n  return base64\n    .replace(/=/g, '')\n    .replace(/\\+/g, '-')\n    .replace(/\\//g, '_');\n}\n\nfunction toBase64(base64url) {\n  base64url = base64url.toString();\n\n  var padding = 4 - base64url.length % 4;\n  if (padding !== 4) {\n    for (var i = 0; i < padding; ++i) {\n      base64url += '=';\n    }\n  }\n\n  return base64url\n    .replace(/\\-/g, '+')\n    .replace(/_/g, '/');\n}\n\nfunction typeError(template) {\n  var args = [].slice.call(arguments, 1);\n  var errMsg = util.format.bind(util, template).apply(null, args);\n  return new TypeError(errMsg);\n}\n\nfunction bufferOrString(obj) {\n  return Buffer.isBuffer(obj) || typeof obj === 'string';\n}\n\nfunction normalizeInput(thing) {\n  if (!bufferOrString(thing))\n    thing = JSON.stringify(thing);\n  return thing;\n}\n\nfunction createHmacSigner(bits) {\n  return function sign(thing, secret) {\n    checkIsSecretKey(secret);\n    thing = normalizeInput(thing);\n    var hmac = crypto.createHmac('sha' + bits, secret);\n    var sig = (hmac.update(thing), hmac.digest('base64'))\n    return fromBase64(sig);\n  }\n}\n\nvar bufferEqual;\nvar timingSafeEqual = 'timingSafeEqual' in crypto ? function timingSafeEqual(a, b) {\n  if (a.byteLength !== b.byteLength) {\n    return false;\n  }\n\n  return crypto.timingSafeEqual(a, b)\n} : function timingSafeEqual(a, b) {\n  if (!bufferEqual) {\n    bufferEqual = require('buffer-equal-constant-time');\n  }\n\n  return bufferEqual(a, b)\n}\n\nfunction createHmacVerifier(bits) {\n  return function verify(thing, signature, secret) {\n    var computedSig = createHmacSigner(bits)(thing, secret);\n    return timingSafeEqual(Buffer.from(signature), Buffer.from(computedSig));\n  }\n}\n\nfunction createKeySigner(bits) {\n return function sign(thing, privateKey) {\n    checkIsPrivateKey(privateKey);\n    thing = normalizeInput(thing);\n    // Even though we are specifying \"RSA\" here, this works with ECDSA\n    // keys as well.\n    var signer = crypto.createSign('RSA-SHA' + bits);\n    var sig = (signer.update(thing), signer.sign(privateKey, 'base64'));\n    return fromBase64(sig);\n  }\n}\n\nfunction createKeyVerifier(bits) {\n  return function verify(thing, signature, publicKey) {\n    checkIsPublicKey(publicKey);\n    thing = normalizeInput(thing);\n    signature = toBase64(signature);\n    var verifier = crypto.createVerify('RSA-SHA' + bits);\n    verifier.update(thing);\n    return verifier.verify(publicKey, signature, 'base64');\n  }\n}\n\nfunction createPSSKeySigner(bits) {\n  return function sign(thing, privateKey) {\n    checkIsPrivateKey(privateKey);\n    thing = normalizeInput(thing);\n    var signer = crypto.createSign('RSA-SHA' + bits);\n    var sig = (signer.update(thing), signer.sign({\n      key: privateKey,\n      padding: crypto.constants.RSA_PKCS1_PSS_PADDING,\n      saltLength: crypto.constants.RSA_PSS_SALTLEN_DIGEST\n    }, 'base64'));\n    return fromBase64(sig);\n  }\n}\n\nfunction createPSSKeyVerifier(bits) {\n  return function verify(thing, signature, publicKey) {\n    checkIsPublicKey(publicKey);\n    thing = normalizeInput(thing);\n    signature = toBase64(signature);\n    var verifier = crypto.createVerify('RSA-SHA' + bits);\n    verifier.update(thing);\n    return verifier.verify({\n      key: publicKey,\n      padding: crypto.constants.RSA_PKCS1_PSS_PADDING,\n      saltLength: crypto.constants.RSA_PSS_SALTLEN_DIGEST\n    }, signature, 'base64');\n  }\n}\n\nfunction createECDSASigner(bits) {\n  var inner = createKeySigner(bits);\n  return function sign() {\n    var signature = inner.apply(null, arguments);\n    signature = formatEcdsa.derToJose(signature, 'ES' + bits);\n    return signature;\n  };\n}\n\nfunction createECDSAVerifer(bits) {\n  var inner = createKeyVerifier(bits);\n  return function verify(thing, signature, publicKey) {\n    signature = formatEcdsa.joseToDer(signature, 'ES' + bits).toString('base64');\n    var result = inner(thing, signature, publicKey);\n    return result;\n  };\n}\n\nfunction createNoneSigner() {\n  return function sign() {\n    return '';\n  }\n}\n\nfunction createNoneVerifier() {\n  return function verify(thing, signature) {\n    return signature === '';\n  }\n}\n\nmodule.exports = function jwa(algorithm) {\n  var signerFactories = {\n    hs: createHmacSigner,\n    rs: createKeySigner,\n    ps: createPSSKeySigner,\n    es: createECDSASigner,\n    none: createNoneSigner,\n  }\n  var verifierFactories = {\n    hs: createHmacVerifier,\n    rs: createKeyVerifier,\n    ps: createPSSKeyVerifier,\n    es: createECDSAVerifer,\n    none: createNoneVerifier,\n  }\n  var match = algorithm.match(/^(RS|PS|ES|HS)(256|384|512)$|^(none)$/i);\n  if (!match)\n    throw typeError(MSG_INVALID_ALGORITHM, algorithm);\n  var algo = (match[1] || match[3]).toLowerCase();\n  var bits = match[2];\n\n  return {\n    sign: signerFactories[algo](bits),\n    verify: verifierFactories[algo](bits),\n  }\n};\n"], "names": [], "mappings": "AAAA,IAAI,SAAS,gGAAuB,MAAM;AAC1C,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,wBAAwB;AAC5B,IAAI,qBAAqB;AACzB,IAAI,2BAA2B;AAC/B,IAAI,yBAAyB;AAE7B,IAAI,qBAAqB,OAAO,OAAO,eAAe,KAAK;AAC3D,IAAI,oBAAoB;IACtB,4BAA4B;IAC5B,sBAAsB;AACxB;AAEA,SAAS,iBAAiB,GAAG;IAC3B,IAAI,OAAO,QAAQ,CAAC,MAAM;QACxB;IACF;IAEA,IAAI,OAAO,QAAQ,UAAU;QAC3B;IACF;IAEA,IAAI,CAAC,oBAAoB;QACvB,MAAM,UAAU;IAClB;IAEA,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,UAAU;IAClB;IAEA,IAAI,OAAO,IAAI,IAAI,KAAK,UAAU;QAChC,MAAM,UAAU;IAClB;IAEA,IAAI,OAAO,IAAI,iBAAiB,KAAK,UAAU;QAC7C,MAAM,UAAU;IAClB;IAEA,IAAI,OAAO,IAAI,MAAM,KAAK,YAAY;QACpC,MAAM,UAAU;IAClB;AACF;;AAEA,SAAS,kBAAkB,GAAG;IAC5B,IAAI,OAAO,QAAQ,CAAC,MAAM;QACxB;IACF;IAEA,IAAI,OAAO,QAAQ,UAAU;QAC3B;IACF;IAEA,IAAI,OAAO,QAAQ,UAAU;QAC3B;IACF;IAEA,MAAM,UAAU;AAClB;;AAEA,SAAS,iBAAiB,GAAG;IAC3B,IAAI,OAAO,QAAQ,CAAC,MAAM;QACxB;IACF;IAEA,IAAI,OAAO,QAAQ,UAAU;QAC3B,OAAO;IACT;IAEA,IAAI,CAAC,oBAAoB;QACvB,MAAM,UAAU;IAClB;IAEA,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,UAAU;IAClB;IAEA,IAAI,IAAI,IAAI,KAAK,UAAU;QACzB,MAAM,UAAU;IAClB;IAEA,IAAI,OAAO,IAAI,MAAM,KAAK,YAAY;QACpC,MAAM,UAAU;IAClB;AACF;AAEA,SAAS,WAAW,MAAM;IACxB,OAAO,OACJ,OAAO,CAAC,MAAM,IACd,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,OAAO;AACpB;AAEA,SAAS,SAAS,SAAS;IACzB,YAAY,UAAU,QAAQ;IAE9B,IAAI,UAAU,IAAI,UAAU,MAAM,GAAG;IACrC,IAAI,YAAY,GAAG;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,EAAE,EAAG;YAChC,aAAa;QACf;IACF;IAEA,OAAO,UACJ,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,MAAM;AACnB;AAEA,SAAS,UAAU,QAAQ;IACzB,IAAI,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;IACpC,IAAI,SAAS,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,UAAU,KAAK,CAAC,MAAM;IAC1D,OAAO,IAAI,UAAU;AACvB;AAEA,SAAS,eAAe,GAAG;IACzB,OAAO,OAAO,QAAQ,CAAC,QAAQ,OAAO,QAAQ;AAChD;AAEA,SAAS,eAAe,KAAK;IAC3B,IAAI,CAAC,eAAe,QAClB,QAAQ,KAAK,SAAS,CAAC;IACzB,OAAO;AACT;AAEA,SAAS,iBAAiB,IAAI;IAC5B,OAAO,SAAS,KAAK,KAAK,EAAE,MAAM;QAChC,iBAAiB;QACjB,QAAQ,eAAe;QACvB,IAAI,OAAO,OAAO,UAAU,CAAC,QAAQ,MAAM;QAC3C,IAAI,MAAM,CAAC,KAAK,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,SAAS;QACpD,OAAO,WAAW;IACpB;AACF;AAEA,IAAI;AACJ,IAAI,kBAAkB,qBAAqB,SAAS,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAC/E,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,EAAE;QACjC,OAAO;IACT;IAEA,OAAO,OAAO,eAAe,CAAC,GAAG;AACnC,IAAI,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAC/B,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,OAAO,YAAY,GAAG;AACxB;AAEA,SAAS,mBAAmB,IAAI;IAC9B,OAAO,SAAS,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM;QAC7C,IAAI,cAAc,iBAAiB,MAAM,OAAO;QAChD,OAAO,gBAAgB,OAAO,IAAI,CAAC,YAAY,OAAO,IAAI,CAAC;IAC7D;AACF;AAEA,SAAS,gBAAgB,IAAI;IAC5B,OAAO,SAAS,KAAK,KAAK,EAAE,UAAU;QACnC,kBAAkB;QAClB,QAAQ,eAAe;QACvB,kEAAkE;QAClE,gBAAgB;QAChB,IAAI,SAAS,OAAO,UAAU,CAAC,YAAY;QAC3C,IAAI,MAAM,CAAC,OAAO,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,YAAY,SAAS;QAClE,OAAO,WAAW;IACpB;AACF;AAEA,SAAS,kBAAkB,IAAI;IAC7B,OAAO,SAAS,OAAO,KAAK,EAAE,SAAS,EAAE,SAAS;QAChD,iBAAiB;QACjB,QAAQ,eAAe;QACvB,YAAY,SAAS;QACrB,IAAI,WAAW,OAAO,YAAY,CAAC,YAAY;QAC/C,SAAS,MAAM,CAAC;QAChB,OAAO,SAAS,MAAM,CAAC,WAAW,WAAW;IAC/C;AACF;AAEA,SAAS,mBAAmB,IAAI;IAC9B,OAAO,SAAS,KAAK,KAAK,EAAE,UAAU;QACpC,kBAAkB;QAClB,QAAQ,eAAe;QACvB,IAAI,SAAS,OAAO,UAAU,CAAC,YAAY;QAC3C,IAAI,MAAM,CAAC,OAAO,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC;YAC3C,KAAK;YACL,SAAS,OAAO,SAAS,CAAC,qBAAqB;YAC/C,YAAY,OAAO,SAAS,CAAC,sBAAsB;QACrD,GAAG,SAAS;QACZ,OAAO,WAAW;IACpB;AACF;AAEA,SAAS,qBAAqB,IAAI;IAChC,OAAO,SAAS,OAAO,KAAK,EAAE,SAAS,EAAE,SAAS;QAChD,iBAAiB;QACjB,QAAQ,eAAe;QACvB,YAAY,SAAS;QACrB,IAAI,WAAW,OAAO,YAAY,CAAC,YAAY;QAC/C,SAAS,MAAM,CAAC;QAChB,OAAO,SAAS,MAAM,CAAC;YACrB,KAAK;YACL,SAAS,OAAO,SAAS,CAAC,qBAAqB;YAC/C,YAAY,OAAO,SAAS,CAAC,sBAAsB;QACrD,GAAG,WAAW;IAChB;AACF;AAEA,SAAS,kBAAkB,IAAI;IAC7B,IAAI,QAAQ,gBAAgB;IAC5B,OAAO,SAAS;QACd,IAAI,YAAY,MAAM,KAAK,CAAC,MAAM;QAClC,YAAY,YAAY,SAAS,CAAC,WAAW,OAAO;QACpD,OAAO;IACT;AACF;AAEA,SAAS,mBAAmB,IAAI;IAC9B,IAAI,QAAQ,kBAAkB;IAC9B,OAAO,SAAS,OAAO,KAAK,EAAE,SAAS,EAAE,SAAS;QAChD,YAAY,YAAY,SAAS,CAAC,WAAW,OAAO,MAAM,QAAQ,CAAC;QACnE,IAAI,SAAS,MAAM,OAAO,WAAW;QACrC,OAAO;IACT;AACF;AAEA,SAAS;IACP,OAAO,SAAS;QACd,OAAO;IACT;AACF;AAEA,SAAS;IACP,OAAO,SAAS,OAAO,KAAK,EAAE,SAAS;QACrC,OAAO,cAAc;IACvB;AACF;AAEA,OAAO,OAAO,GAAG,SAAS,IAAI,SAAS;IACrC,IAAI,kBAAkB;QACpB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IACA,IAAI,oBAAoB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IACA,IAAI,QAAQ,UAAU,KAAK,CAAC;IAC5B,IAAI,CAAC,OACH,MAAM,UAAU,uBAAuB;IACzC,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,EAAE,WAAW;IAC7C,IAAI,OAAO,KAAK,CAAC,EAAE;IAEnB,OAAO;QACL,MAAM,eAAe,CAAC,KAAK,CAAC;QAC5B,QAAQ,iBAAiB,CAAC,KAAK,CAAC;IAClC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jsonwebtoken/decode.js"], "sourcesContent": ["var jws = require('jws');\n\nmodule.exports = function (jwt, options) {\n  options = options || {};\n  var decoded = jws.decode(jwt, options);\n  if (!decoded) { return null; }\n  var payload = decoded.payload;\n\n  //try parse the payload\n  if(typeof payload === 'string') {\n    try {\n      var obj = JSON.parse(payload);\n      if(obj !== null && typeof obj === 'object') {\n        payload = obj;\n      }\n    } catch (e) { }\n  }\n\n  //return header if `complete` option is enabled.  header includes claims\n  //such as `kid` and `alg` used to select the key within a JWKS needed to\n  //verify the signature\n  if (options.complete === true) {\n    return {\n      header: decoded.header,\n      payload: payload,\n      signature: decoded.signature\n    };\n  }\n  return payload;\n};\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,OAAO,OAAO,GAAG,SAAU,GAAG,EAAE,OAAO;IACrC,UAAU,WAAW,CAAC;IACtB,IAAI,UAAU,IAAI,MAAM,CAAC,KAAK;IAC9B,IAAI,CAAC,SAAS;QAAE,OAAO;IAAM;IAC7B,IAAI,UAAU,QAAQ,OAAO;IAE7B,uBAAuB;IACvB,IAAG,OAAO,YAAY,UAAU;QAC9B,IAAI;YACF,IAAI,MAAM,KAAK,KAAK,CAAC;YACrB,IAAG,QAAQ,QAAQ,OAAO,QAAQ,UAAU;gBAC1C,UAAU;YACZ;QACF,EAAE,OAAO,GAAG,CAAE;IAChB;IAEA,wEAAwE;IACxE,wEAAwE;IACxE,sBAAsB;IACtB,IAAI,QAAQ,QAAQ,KAAK,MAAM;QAC7B,OAAO;YACL,QAAQ,QAAQ,MAAM;YACtB,SAAS;YACT,WAAW,QAAQ,SAAS;QAC9B;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jsonwebtoken/lib/JsonWebTokenError.js"], "sourcesContent": ["var JsonWebTokenError = function (message, error) {\n  Error.call(this, message);\n  if(Error.captureStackTrace) {\n    Error.captureStackTrace(this, this.constructor);\n  }\n  this.name = 'JsonWebTokenError';\n  this.message = message;\n  if (error) this.inner = error;\n};\n\nJsonWebTokenError.prototype = Object.create(Error.prototype);\nJsonWebTokenError.prototype.constructor = JsonWebTokenError;\n\nmodule.exports = JsonWebTokenError;\n"], "names": [], "mappings": "AAAA,IAAI,oBAAoB,SAAU,OAAO,EAAE,KAAK;IAC9C,MAAM,IAAI,CAAC,IAAI,EAAE;IACjB,IAAG,MAAM,iBAAiB,EAAE;QAC1B,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;IAChD;IACA,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,OAAO,IAAI,CAAC,KAAK,GAAG;AAC1B;AAEA,kBAAkB,SAAS,GAAG,OAAO,MAAM,CAAC,MAAM,SAAS;AAC3D,kBAAkB,SAAS,CAAC,WAAW,GAAG;AAE1C,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 808, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jsonwebtoken/lib/NotBeforeError.js"], "sourcesContent": ["var JsonWebTokenError = require('./JsonWebTokenError');\n\nvar NotBeforeError = function (message, date) {\n  JsonWebTokenError.call(this, message);\n  this.name = 'NotBeforeError';\n  this.date = date;\n};\n\nNotBeforeError.prototype = Object.create(JsonWebTokenError.prototype);\n\nNotBeforeError.prototype.constructor = NotBeforeError;\n\nmodule.exports = NotBeforeError;"], "names": [], "mappings": "AAAA,IAAI;AAEJ,IAAI,iBAAiB,SAAU,OAAO,EAAE,IAAI;IAC1C,kBAAkB,IAAI,CAAC,IAAI,EAAE;IAC7B,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,IAAI,GAAG;AACd;AAEA,eAAe,SAAS,GAAG,OAAO,MAAM,CAAC,kBAAkB,SAAS;AAEpE,eAAe,SAAS,CAAC,WAAW,GAAG;AAEvC,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 822, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jsonwebtoken/lib/TokenExpiredError.js"], "sourcesContent": ["var JsonWebTokenError = require('./JsonWebTokenError');\n\nvar TokenExpiredError = function (message, expiredAt) {\n  JsonWebTokenError.call(this, message);\n  this.name = 'TokenExpiredError';\n  this.expiredAt = expiredAt;\n};\n\nTokenExpiredError.prototype = Object.create(JsonWebTokenError.prototype);\n\nTokenExpiredError.prototype.constructor = TokenExpiredError;\n\nmodule.exports = TokenExpiredError;"], "names": [], "mappings": "AAAA,IAAI;AAEJ,IAAI,oBAAoB,SAAU,OAAO,EAAE,SAAS;IAClD,kBAAkB,IAAI,CAAC,IAAI,EAAE;IAC7B,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,SAAS,GAAG;AACnB;AAEA,kBAAkB,SAAS,GAAG,OAAO,MAAM,CAAC,kBAAkB,SAAS;AAEvE,kBAAkB,SAAS,CAAC,WAAW,GAAG;AAE1C,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 836, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jsonwebtoken/lib/timespan.js"], "sourcesContent": ["var ms = require('ms');\n\nmodule.exports = function (time, iat) {\n  var timestamp = iat || Math.floor(Date.now() / 1000);\n\n  if (typeof time === 'string') {\n    var milliseconds = ms(time);\n    if (typeof milliseconds === 'undefined') {\n      return;\n    }\n    return Math.floor(timestamp + milliseconds / 1000);\n  } else if (typeof time === 'number') {\n    return timestamp + time;\n  } else {\n    return;\n  }\n\n};"], "names": [], "mappings": "AAAA,IAAI;AAEJ,OAAO,OAAO,GAAG,SAAU,IAAI,EAAE,GAAG;IAClC,IAAI,YAAY,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;IAE/C,IAAI,OAAO,SAAS,UAAU;QAC5B,IAAI,eAAe,GAAG;QACtB,IAAI,OAAO,iBAAiB,aAAa;YACvC;QACF;QACA,OAAO,KAAK,KAAK,CAAC,YAAY,eAAe;IAC/C,OAAO,IAAI,OAAO,SAAS,UAAU;QACnC,OAAO,YAAY;IACrB,OAAO;QACL;IACF;AAEF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js"], "sourcesContent": ["const semver = require('semver');\n\nmodule.exports = semver.satisfies(process.version, '>=15.7.0');\n"], "names": [], "mappings": "AAAA,MAAM;AAEN,OAAO,OAAO,GAAG,OAAO,SAAS,CAAC,QAAQ,OAAO,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js"], "sourcesContent": ["const semver = require('semver');\n\nmodule.exports = semver.satisfies(process.version, '>=16.9.0');\n"], "names": [], "mappings": "AAAA,MAAM;AAEN,OAAO,OAAO,GAAG,OAAO,SAAS,CAAC,QAAQ,OAAO,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jsonwebtoken/lib/validateAsymmetricKey.js"], "sourcesContent": ["const ASYMMETRIC_KEY_DETAILS_SUPPORTED = require('./asymmetricKeyDetailsSupported');\nconst RSA_PSS_KEY_DETAILS_SUPPORTED = require('./rsaPssKeyDetailsSupported');\n\nconst allowedAlgorithmsForKeys = {\n  'ec': ['ES256', 'ES384', 'ES512'],\n  'rsa': ['RS256', 'PS256', 'RS384', 'PS384', 'RS512', 'PS512'],\n  'rsa-pss': ['PS256', 'PS384', 'PS512']\n};\n\nconst allowedCurves = {\n  ES256: 'prime256v1',\n  ES384: 'secp384r1',\n  ES512: 'secp521r1',\n};\n\nmodule.exports = function(algorithm, key) {\n  if (!algorithm || !key) return;\n\n  const keyType = key.asymmetricKeyType;\n  if (!keyType) return;\n\n  const allowedAlgorithms = allowedAlgorithmsForKeys[keyType];\n\n  if (!allowedAlgorithms) {\n    throw new Error(`Unknown key type \"${keyType}\".`);\n  }\n\n  if (!allowedAlgorithms.includes(algorithm)) {\n    throw new Error(`\"alg\" parameter for \"${keyType}\" key type must be one of: ${allowedAlgorithms.join(', ')}.`)\n  }\n\n  /*\n   * Ignore the next block from test coverage because it gets executed\n   * conditionally depending on the Node version. Not ignoring it would\n   * prevent us from reaching the target % of coverage for versions of\n   * Node under 15.7.0.\n   */\n  /* istanbul ignore next */\n  if (ASYMMETRIC_KEY_DETAILS_SUPPORTED) {\n    switch (keyType) {\n    case 'ec':\n      const keyCurve = key.asymmetricKeyDetails.namedCurve;\n      const allowedCurve = allowedCurves[algorithm];\n\n      if (keyCurve !== allowedCurve) {\n        throw new Error(`\"alg\" parameter \"${algorithm}\" requires curve \"${allowedCurve}\".`);\n      }\n      break;\n\n    case 'rsa-pss':\n      if (RSA_PSS_KEY_DETAILS_SUPPORTED) {\n        const length = parseInt(algorithm.slice(-3), 10);\n        const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = key.asymmetricKeyDetails;\n\n        if (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm) {\n          throw new Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${algorithm}.`);\n        }\n\n        if (saltLength !== undefined && saltLength > length >> 3) {\n          throw new Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${algorithm}.`)\n        }\n      }\n      break;\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,MAAM;AACN,MAAM;AAEN,MAAM,2BAA2B;IAC/B,MAAM;QAAC;QAAS;QAAS;KAAQ;IACjC,OAAO;QAAC;QAAS;QAAS;QAAS;QAAS;QAAS;KAAQ;IAC7D,WAAW;QAAC;QAAS;QAAS;KAAQ;AACxC;AAEA,MAAM,gBAAgB;IACpB,OAAO;IACP,OAAO;IACP,OAAO;AACT;AAEA,OAAO,OAAO,GAAG,SAAS,SAAS,EAAE,GAAG;IACtC,IAAI,CAAC,aAAa,CAAC,KAAK;IAExB,MAAM,UAAU,IAAI,iBAAiB;IACrC,IAAI,CAAC,SAAS;IAEd,MAAM,oBAAoB,wBAAwB,CAAC,QAAQ;IAE3D,IAAI,CAAC,mBAAmB;QACtB,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,QAAQ,EAAE,CAAC;IAClD;IAEA,IAAI,CAAC,kBAAkB,QAAQ,CAAC,YAAY;QAC1C,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,QAAQ,2BAA2B,EAAE,kBAAkB,IAAI,CAAC,MAAM,CAAC,CAAC;IAC9G;IAEA;;;;;GAKC,GACD,wBAAwB,GACxB,IAAI,kCAAkC;QACpC,OAAQ;YACR,KAAK;gBACH,MAAM,WAAW,IAAI,oBAAoB,CAAC,UAAU;gBACpD,MAAM,eAAe,aAAa,CAAC,UAAU;gBAE7C,IAAI,aAAa,cAAc;oBAC7B,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,UAAU,kBAAkB,EAAE,aAAa,EAAE,CAAC;gBACpF;gBACA;YAEF,KAAK;gBACH,IAAI,+BAA+B;oBACjC,MAAM,SAAS,SAAS,UAAU,KAAK,CAAC,CAAC,IAAI;oBAC7C,MAAM,EAAE,aAAa,EAAE,iBAAiB,EAAE,UAAU,EAAE,GAAG,IAAI,oBAAoB;oBAEjF,IAAI,kBAAkB,CAAC,GAAG,EAAE,QAAQ,IAAI,sBAAsB,eAAe;wBAC3E,MAAM,IAAI,MAAM,CAAC,6FAA6F,EAAE,UAAU,CAAC,CAAC;oBAC9H;oBAEA,IAAI,eAAe,aAAa,aAAa,UAAU,GAAG;wBACxD,MAAM,IAAI,MAAM,CAAC,yGAAyG,EAAE,UAAU,CAAC,CAAC;oBAC1I;gBACF;gBACA;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 942, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jsonwebtoken/lib/psSupported.js"], "sourcesContent": ["var semver = require('semver');\n\nmodule.exports = semver.satisfies(process.version, '^6.12.0 || >=8.0.0');\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,OAAO,OAAO,GAAG,OAAO,SAAS,CAAC,QAAQ,OAAO,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jsonwebtoken/verify.js"], "sourcesContent": ["const JsonWebTokenError = require('./lib/JsonWebTokenError');\nconst NotBeforeError = require('./lib/NotBeforeError');\nconst TokenExpiredError = require('./lib/TokenExpiredError');\nconst decode = require('./decode');\nconst timespan = require('./lib/timespan');\nconst validateAsymmetricKey = require('./lib/validateAsymmetricKey');\nconst PS_SUPPORTED = require('./lib/psSupported');\nconst jws = require('jws');\nconst {KeyObject, createSecretKey, createPublicKey} = require(\"crypto\");\n\nconst PUB_KEY_ALGS = ['RS256', 'RS384', 'RS512'];\nconst EC_KEY_ALGS = ['ES256', 'ES384', 'ES512'];\nconst RSA_KEY_ALGS = ['RS256', 'RS384', 'RS512'];\nconst HS_ALGS = ['HS256', 'HS384', 'HS512'];\n\nif (PS_SUPPORTED) {\n  PUB_KEY_ALGS.splice(PUB_KEY_ALGS.length, 0, 'PS256', 'PS384', 'PS512');\n  RSA_KEY_ALGS.splice(RSA_KEY_ALGS.length, 0, 'PS256', 'PS384', 'PS512');\n}\n\nmodule.exports = function (jwtString, secretOrPublicKey, options, callback) {\n  if ((typeof options === 'function') && !callback) {\n    callback = options;\n    options = {};\n  }\n\n  if (!options) {\n    options = {};\n  }\n\n  //clone this object since we are going to mutate it.\n  options = Object.assign({}, options);\n\n  let done;\n\n  if (callback) {\n    done = callback;\n  } else {\n    done = function(err, data) {\n      if (err) throw err;\n      return data;\n    };\n  }\n\n  if (options.clockTimestamp && typeof options.clockTimestamp !== 'number') {\n    return done(new JsonWebTokenError('clockTimestamp must be a number'));\n  }\n\n  if (options.nonce !== undefined && (typeof options.nonce !== 'string' || options.nonce.trim() === '')) {\n    return done(new JsonWebTokenError('nonce must be a non-empty string'));\n  }\n\n  if (options.allowInvalidAsymmetricKeyTypes !== undefined && typeof options.allowInvalidAsymmetricKeyTypes !== 'boolean') {\n    return done(new JsonWebTokenError('allowInvalidAsymmetricKeyTypes must be a boolean'));\n  }\n\n  const clockTimestamp = options.clockTimestamp || Math.floor(Date.now() / 1000);\n\n  if (!jwtString){\n    return done(new JsonWebTokenError('jwt must be provided'));\n  }\n\n  if (typeof jwtString !== 'string') {\n    return done(new JsonWebTokenError('jwt must be a string'));\n  }\n\n  const parts = jwtString.split('.');\n\n  if (parts.length !== 3){\n    return done(new JsonWebTokenError('jwt malformed'));\n  }\n\n  let decodedToken;\n\n  try {\n    decodedToken = decode(jwtString, { complete: true });\n  } catch(err) {\n    return done(err);\n  }\n\n  if (!decodedToken) {\n    return done(new JsonWebTokenError('invalid token'));\n  }\n\n  const header = decodedToken.header;\n  let getSecret;\n\n  if(typeof secretOrPublicKey === 'function') {\n    if(!callback) {\n      return done(new JsonWebTokenError('verify must be called asynchronous if secret or public key is provided as a callback'));\n    }\n\n    getSecret = secretOrPublicKey;\n  }\n  else {\n    getSecret = function(header, secretCallback) {\n      return secretCallback(null, secretOrPublicKey);\n    };\n  }\n\n  return getSecret(header, function(err, secretOrPublicKey) {\n    if(err) {\n      return done(new JsonWebTokenError('error in secret or public key callback: ' + err.message));\n    }\n\n    const hasSignature = parts[2].trim() !== '';\n\n    if (!hasSignature && secretOrPublicKey){\n      return done(new JsonWebTokenError('jwt signature is required'));\n    }\n\n    if (hasSignature && !secretOrPublicKey) {\n      return done(new JsonWebTokenError('secret or public key must be provided'));\n    }\n\n    if (!hasSignature && !options.algorithms) {\n      return done(new JsonWebTokenError('please specify \"none\" in \"algorithms\" to verify unsigned tokens'));\n    }\n\n    if (secretOrPublicKey != null && !(secretOrPublicKey instanceof KeyObject)) {\n      try {\n        secretOrPublicKey = createPublicKey(secretOrPublicKey);\n      } catch (_) {\n        try {\n          secretOrPublicKey = createSecretKey(typeof secretOrPublicKey === 'string' ? Buffer.from(secretOrPublicKey) : secretOrPublicKey);\n        } catch (_) {\n          return done(new JsonWebTokenError('secretOrPublicKey is not valid key material'))\n        }\n      }\n    }\n\n    if (!options.algorithms) {\n      if (secretOrPublicKey.type === 'secret') {\n        options.algorithms = HS_ALGS;\n      } else if (['rsa', 'rsa-pss'].includes(secretOrPublicKey.asymmetricKeyType)) {\n        options.algorithms = RSA_KEY_ALGS\n      } else if (secretOrPublicKey.asymmetricKeyType === 'ec') {\n        options.algorithms = EC_KEY_ALGS\n      } else {\n        options.algorithms = PUB_KEY_ALGS\n      }\n    }\n\n    if (options.algorithms.indexOf(decodedToken.header.alg) === -1) {\n      return done(new JsonWebTokenError('invalid algorithm'));\n    }\n\n    if (header.alg.startsWith('HS') && secretOrPublicKey.type !== 'secret') {\n      return done(new JsonWebTokenError((`secretOrPublicKey must be a symmetric key when using ${header.alg}`)))\n    } else if (/^(?:RS|PS|ES)/.test(header.alg) && secretOrPublicKey.type !== 'public') {\n      return done(new JsonWebTokenError((`secretOrPublicKey must be an asymmetric key when using ${header.alg}`)))\n    }\n\n    if (!options.allowInvalidAsymmetricKeyTypes) {\n      try {\n        validateAsymmetricKey(header.alg, secretOrPublicKey);\n      } catch (e) {\n        return done(e);\n      }\n    }\n\n    let valid;\n\n    try {\n      valid = jws.verify(jwtString, decodedToken.header.alg, secretOrPublicKey);\n    } catch (e) {\n      return done(e);\n    }\n\n    if (!valid) {\n      return done(new JsonWebTokenError('invalid signature'));\n    }\n\n    const payload = decodedToken.payload;\n\n    if (typeof payload.nbf !== 'undefined' && !options.ignoreNotBefore) {\n      if (typeof payload.nbf !== 'number') {\n        return done(new JsonWebTokenError('invalid nbf value'));\n      }\n      if (payload.nbf > clockTimestamp + (options.clockTolerance || 0)) {\n        return done(new NotBeforeError('jwt not active', new Date(payload.nbf * 1000)));\n      }\n    }\n\n    if (typeof payload.exp !== 'undefined' && !options.ignoreExpiration) {\n      if (typeof payload.exp !== 'number') {\n        return done(new JsonWebTokenError('invalid exp value'));\n      }\n      if (clockTimestamp >= payload.exp + (options.clockTolerance || 0)) {\n        return done(new TokenExpiredError('jwt expired', new Date(payload.exp * 1000)));\n      }\n    }\n\n    if (options.audience) {\n      const audiences = Array.isArray(options.audience) ? options.audience : [options.audience];\n      const target = Array.isArray(payload.aud) ? payload.aud : [payload.aud];\n\n      const match = target.some(function (targetAudience) {\n        return audiences.some(function (audience) {\n          return audience instanceof RegExp ? audience.test(targetAudience) : audience === targetAudience;\n        });\n      });\n\n      if (!match) {\n        return done(new JsonWebTokenError('jwt audience invalid. expected: ' + audiences.join(' or ')));\n      }\n    }\n\n    if (options.issuer) {\n      const invalid_issuer =\n              (typeof options.issuer === 'string' && payload.iss !== options.issuer) ||\n              (Array.isArray(options.issuer) && options.issuer.indexOf(payload.iss) === -1);\n\n      if (invalid_issuer) {\n        return done(new JsonWebTokenError('jwt issuer invalid. expected: ' + options.issuer));\n      }\n    }\n\n    if (options.subject) {\n      if (payload.sub !== options.subject) {\n        return done(new JsonWebTokenError('jwt subject invalid. expected: ' + options.subject));\n      }\n    }\n\n    if (options.jwtid) {\n      if (payload.jti !== options.jwtid) {\n        return done(new JsonWebTokenError('jwt jwtid invalid. expected: ' + options.jwtid));\n      }\n    }\n\n    if (options.nonce) {\n      if (payload.nonce !== options.nonce) {\n        return done(new JsonWebTokenError('jwt nonce invalid. expected: ' + options.nonce));\n      }\n    }\n\n    if (options.maxAge) {\n      if (typeof payload.iat !== 'number') {\n        return done(new JsonWebTokenError('iat required when maxAge is specified'));\n      }\n\n      const maxAgeTimestamp = timespan(options.maxAge, payload.iat);\n      if (typeof maxAgeTimestamp === 'undefined') {\n        return done(new JsonWebTokenError('\"maxAge\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n      }\n      if (clockTimestamp >= maxAgeTimestamp + (options.clockTolerance || 0)) {\n        return done(new TokenExpiredError('maxAge exceeded', new Date(maxAgeTimestamp * 1000)));\n      }\n    }\n\n    if (options.complete === true) {\n      const signature = decodedToken.signature;\n\n      return done(null, {\n        header: header,\n        payload: payload,\n        signature: signature\n      });\n    }\n\n    return done(null, payload);\n  });\n};\n"], "names": [], "mappings": "AAAA,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,EAAC,SAAS,EAAE,eAAe,EAAE,eAAe,EAAC;AAEnD,MAAM,eAAe;IAAC;IAAS;IAAS;CAAQ;AAChD,MAAM,cAAc;IAAC;IAAS;IAAS;CAAQ;AAC/C,MAAM,eAAe;IAAC;IAAS;IAAS;CAAQ;AAChD,MAAM,UAAU;IAAC;IAAS;IAAS;CAAQ;AAE3C,IAAI,cAAc;IAChB,aAAa,MAAM,CAAC,aAAa,MAAM,EAAE,GAAG,SAAS,SAAS;IAC9D,aAAa,MAAM,CAAC,aAAa,MAAM,EAAE,GAAG,SAAS,SAAS;AAChE;AAEA,OAAO,OAAO,GAAG,SAAU,SAAS,EAAE,iBAAiB,EAAE,OAAO,EAAE,QAAQ;IACxE,IAAI,AAAC,OAAO,YAAY,cAAe,CAAC,UAAU;QAChD,WAAW;QACX,UAAU,CAAC;IACb;IAEA,IAAI,CAAC,SAAS;QACZ,UAAU,CAAC;IACb;IAEA,oDAAoD;IACpD,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG;IAE5B,IAAI;IAEJ,IAAI,UAAU;QACZ,OAAO;IACT,OAAO;QACL,OAAO,SAAS,GAAG,EAAE,IAAI;YACvB,IAAI,KAAK,MAAM;YACf,OAAO;QACT;IACF;IAEA,IAAI,QAAQ,cAAc,IAAI,OAAO,QAAQ,cAAc,KAAK,UAAU;QACxE,OAAO,KAAK,IAAI,kBAAkB;IACpC;IAEA,IAAI,QAAQ,KAAK,KAAK,aAAa,CAAC,OAAO,QAAQ,KAAK,KAAK,YAAY,QAAQ,KAAK,CAAC,IAAI,OAAO,EAAE,GAAG;QACrG,OAAO,KAAK,IAAI,kBAAkB;IACpC;IAEA,IAAI,QAAQ,8BAA8B,KAAK,aAAa,OAAO,QAAQ,8BAA8B,KAAK,WAAW;QACvH,OAAO,KAAK,IAAI,kBAAkB;IACpC;IAEA,MAAM,iBAAiB,QAAQ,cAAc,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;IAEzE,IAAI,CAAC,WAAU;QACb,OAAO,KAAK,IAAI,kBAAkB;IACpC;IAEA,IAAI,OAAO,cAAc,UAAU;QACjC,OAAO,KAAK,IAAI,kBAAkB;IACpC;IAEA,MAAM,QAAQ,UAAU,KAAK,CAAC;IAE9B,IAAI,MAAM,MAAM,KAAK,GAAE;QACrB,OAAO,KAAK,IAAI,kBAAkB;IACpC;IAEA,IAAI;IAEJ,IAAI;QACF,eAAe,OAAO,WAAW;YAAE,UAAU;QAAK;IACpD,EAAE,OAAM,KAAK;QACX,OAAO,KAAK;IACd;IAEA,IAAI,CAAC,cAAc;QACjB,OAAO,KAAK,IAAI,kBAAkB;IACpC;IAEA,MAAM,SAAS,aAAa,MAAM;IAClC,IAAI;IAEJ,IAAG,OAAO,sBAAsB,YAAY;QAC1C,IAAG,CAAC,UAAU;YACZ,OAAO,KAAK,IAAI,kBAAkB;QACpC;QAEA,YAAY;IACd,OACK;QACH,YAAY,SAAS,MAAM,EAAE,cAAc;YACzC,OAAO,eAAe,MAAM;QAC9B;IACF;IAEA,OAAO,UAAU,QAAQ,SAAS,GAAG,EAAE,iBAAiB;QACtD,IAAG,KAAK;YACN,OAAO,KAAK,IAAI,kBAAkB,6CAA6C,IAAI,OAAO;QAC5F;QAEA,MAAM,eAAe,KAAK,CAAC,EAAE,CAAC,IAAI,OAAO;QAEzC,IAAI,CAAC,gBAAgB,mBAAkB;YACrC,OAAO,KAAK,IAAI,kBAAkB;QACpC;QAEA,IAAI,gBAAgB,CAAC,mBAAmB;YACtC,OAAO,KAAK,IAAI,kBAAkB;QACpC;QAEA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,UAAU,EAAE;YACxC,OAAO,KAAK,IAAI,kBAAkB;QACpC;QAEA,IAAI,qBAAqB,QAAQ,CAAC,CAAC,6BAA6B,SAAS,GAAG;YAC1E,IAAI;gBACF,oBAAoB,gBAAgB;YACtC,EAAE,OAAO,GAAG;gBACV,IAAI;oBACF,oBAAoB,gBAAgB,OAAO,sBAAsB,WAAW,OAAO,IAAI,CAAC,qBAAqB;gBAC/G,EAAE,OAAO,GAAG;oBACV,OAAO,KAAK,IAAI,kBAAkB;gBACpC;YACF;QACF;QAEA,IAAI,CAAC,QAAQ,UAAU,EAAE;YACvB,IAAI,kBAAkB,IAAI,KAAK,UAAU;gBACvC,QAAQ,UAAU,GAAG;YACvB,OAAO,IAAI;gBAAC;gBAAO;aAAU,CAAC,QAAQ,CAAC,kBAAkB,iBAAiB,GAAG;gBAC3E,QAAQ,UAAU,GAAG;YACvB,OAAO,IAAI,kBAAkB,iBAAiB,KAAK,MAAM;gBACvD,QAAQ,UAAU,GAAG;YACvB,OAAO;gBACL,QAAQ,UAAU,GAAG;YACvB;QACF;QAEA,IAAI,QAAQ,UAAU,CAAC,OAAO,CAAC,aAAa,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG;YAC9D,OAAO,KAAK,IAAI,kBAAkB;QACpC;QAEA,IAAI,OAAO,GAAG,CAAC,UAAU,CAAC,SAAS,kBAAkB,IAAI,KAAK,UAAU;YACtE,OAAO,KAAK,IAAI,kBAAmB,CAAC,qDAAqD,EAAE,OAAO,GAAG,EAAE;QACzG,OAAO,IAAI,gBAAgB,IAAI,CAAC,OAAO,GAAG,KAAK,kBAAkB,IAAI,KAAK,UAAU;YAClF,OAAO,KAAK,IAAI,kBAAmB,CAAC,uDAAuD,EAAE,OAAO,GAAG,EAAE;QAC3G;QAEA,IAAI,CAAC,QAAQ,8BAA8B,EAAE;YAC3C,IAAI;gBACF,sBAAsB,OAAO,GAAG,EAAE;YACpC,EAAE,OAAO,GAAG;gBACV,OAAO,KAAK;YACd;QACF;QAEA,IAAI;QAEJ,IAAI;YACF,QAAQ,IAAI,MAAM,CAAC,WAAW,aAAa,MAAM,CAAC,GAAG,EAAE;QACzD,EAAE,OAAO,GAAG;YACV,OAAO,KAAK;QACd;QAEA,IAAI,CAAC,OAAO;YACV,OAAO,KAAK,IAAI,kBAAkB;QACpC;QAEA,MAAM,UAAU,aAAa,OAAO;QAEpC,IAAI,OAAO,QAAQ,GAAG,KAAK,eAAe,CAAC,QAAQ,eAAe,EAAE;YAClE,IAAI,OAAO,QAAQ,GAAG,KAAK,UAAU;gBACnC,OAAO,KAAK,IAAI,kBAAkB;YACpC;YACA,IAAI,QAAQ,GAAG,GAAG,iBAAiB,CAAC,QAAQ,cAAc,IAAI,CAAC,GAAG;gBAChE,OAAO,KAAK,IAAI,eAAe,kBAAkB,IAAI,KAAK,QAAQ,GAAG,GAAG;YAC1E;QACF;QAEA,IAAI,OAAO,QAAQ,GAAG,KAAK,eAAe,CAAC,QAAQ,gBAAgB,EAAE;YACnE,IAAI,OAAO,QAAQ,GAAG,KAAK,UAAU;gBACnC,OAAO,KAAK,IAAI,kBAAkB;YACpC;YACA,IAAI,kBAAkB,QAAQ,GAAG,GAAG,CAAC,QAAQ,cAAc,IAAI,CAAC,GAAG;gBACjE,OAAO,KAAK,IAAI,kBAAkB,eAAe,IAAI,KAAK,QAAQ,GAAG,GAAG;YAC1E;QACF;QAEA,IAAI,QAAQ,QAAQ,EAAE;YACpB,MAAM,YAAY,MAAM,OAAO,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,GAAG;gBAAC,QAAQ,QAAQ;aAAC;YACzF,MAAM,SAAS,MAAM,OAAO,CAAC,QAAQ,GAAG,IAAI,QAAQ,GAAG,GAAG;gBAAC,QAAQ,GAAG;aAAC;YAEvE,MAAM,QAAQ,OAAO,IAAI,CAAC,SAAU,cAAc;gBAChD,OAAO,UAAU,IAAI,CAAC,SAAU,QAAQ;oBACtC,OAAO,oBAAoB,SAAS,SAAS,IAAI,CAAC,kBAAkB,aAAa;gBACnF;YACF;YAEA,IAAI,CAAC,OAAO;gBACV,OAAO,KAAK,IAAI,kBAAkB,qCAAqC,UAAU,IAAI,CAAC;YACxF;QACF;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,MAAM,iBACE,AAAC,OAAO,QAAQ,MAAM,KAAK,YAAY,QAAQ,GAAG,KAAK,QAAQ,MAAM,IACpE,MAAM,OAAO,CAAC,QAAQ,MAAM,KAAK,QAAQ,MAAM,CAAC,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC;YAEnF,IAAI,gBAAgB;gBAClB,OAAO,KAAK,IAAI,kBAAkB,mCAAmC,QAAQ,MAAM;YACrF;QACF;QAEA,IAAI,QAAQ,OAAO,EAAE;YACnB,IAAI,QAAQ,GAAG,KAAK,QAAQ,OAAO,EAAE;gBACnC,OAAO,KAAK,IAAI,kBAAkB,oCAAoC,QAAQ,OAAO;YACvF;QACF;QAEA,IAAI,QAAQ,KAAK,EAAE;YACjB,IAAI,QAAQ,GAAG,KAAK,QAAQ,KAAK,EAAE;gBACjC,OAAO,KAAK,IAAI,kBAAkB,kCAAkC,QAAQ,KAAK;YACnF;QACF;QAEA,IAAI,QAAQ,KAAK,EAAE;YACjB,IAAI,QAAQ,KAAK,KAAK,QAAQ,KAAK,EAAE;gBACnC,OAAO,KAAK,IAAI,kBAAkB,kCAAkC,QAAQ,KAAK;YACnF;QACF;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,IAAI,OAAO,QAAQ,GAAG,KAAK,UAAU;gBACnC,OAAO,KAAK,IAAI,kBAAkB;YACpC;YAEA,MAAM,kBAAkB,SAAS,QAAQ,MAAM,EAAE,QAAQ,GAAG;YAC5D,IAAI,OAAO,oBAAoB,aAAa;gBAC1C,OAAO,KAAK,IAAI,kBAAkB;YACpC;YACA,IAAI,kBAAkB,kBAAkB,CAAC,QAAQ,cAAc,IAAI,CAAC,GAAG;gBACrE,OAAO,KAAK,IAAI,kBAAkB,mBAAmB,IAAI,KAAK,kBAAkB;YAClF;QACF;QAEA,IAAI,QAAQ,QAAQ,KAAK,MAAM;YAC7B,MAAM,YAAY,aAAa,SAAS;YAExC,OAAO,KAAK,MAAM;gBAChB,QAAQ;gBACR,SAAS;gBACT,WAAW;YACb;QACF;QAEA,OAAO,KAAK,MAAM;IACpB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jsonwebtoken/sign.js"], "sourcesContent": ["const timespan = require('./lib/timespan');\nconst PS_SUPPORTED = require('./lib/psSupported');\nconst validateAsymmetricKey = require('./lib/validateAsymmetricKey');\nconst jws = require('jws');\nconst includes = require('lodash.includes');\nconst isBoolean = require('lodash.isboolean');\nconst isInteger = require('lodash.isinteger');\nconst isNumber = require('lodash.isnumber');\nconst isPlainObject = require('lodash.isplainobject');\nconst isString = require('lodash.isstring');\nconst once = require('lodash.once');\nconst { KeyObject, createSecretKey, createPrivateKey } = require('crypto')\n\nconst SUPPORTED_ALGS = ['RS256', 'RS384', 'RS512', 'ES256', 'ES384', 'ES512', 'HS256', 'HS384', 'HS512', 'none'];\nif (PS_SUPPORTED) {\n  SUPPORTED_ALGS.splice(3, 0, 'PS256', 'PS384', 'PS512');\n}\n\nconst sign_options_schema = {\n  expiresIn: { isValid: function(value) { return isInteger(value) || (isString(value) && value); }, message: '\"expiresIn\" should be a number of seconds or string representing a timespan' },\n  notBefore: { isValid: function(value) { return isInteger(value) || (isString(value) && value); }, message: '\"notBefore\" should be a number of seconds or string representing a timespan' },\n  audience: { isValid: function(value) { return isString(value) || Array.isArray(value); }, message: '\"audience\" must be a string or array' },\n  algorithm: { isValid: includes.bind(null, SUPPORTED_ALGS), message: '\"algorithm\" must be a valid string enum value' },\n  header: { isValid: isPlainObject, message: '\"header\" must be an object' },\n  encoding: { isValid: isString, message: '\"encoding\" must be a string' },\n  issuer: { isValid: isString, message: '\"issuer\" must be a string' },\n  subject: { isValid: isString, message: '\"subject\" must be a string' },\n  jwtid: { isValid: isString, message: '\"jwtid\" must be a string' },\n  noTimestamp: { isValid: isBoolean, message: '\"noTimestamp\" must be a boolean' },\n  keyid: { isValid: isString, message: '\"keyid\" must be a string' },\n  mutatePayload: { isValid: isBoolean, message: '\"mutatePayload\" must be a boolean' },\n  allowInsecureKeySizes: { isValid: isBoolean, message: '\"allowInsecureKeySizes\" must be a boolean'},\n  allowInvalidAsymmetricKeyTypes: { isValid: isBoolean, message: '\"allowInvalidAsymmetricKeyTypes\" must be a boolean'}\n};\n\nconst registered_claims_schema = {\n  iat: { isValid: isNumber, message: '\"iat\" should be a number of seconds' },\n  exp: { isValid: isNumber, message: '\"exp\" should be a number of seconds' },\n  nbf: { isValid: isNumber, message: '\"nbf\" should be a number of seconds' }\n};\n\nfunction validate(schema, allowUnknown, object, parameterName) {\n  if (!isPlainObject(object)) {\n    throw new Error('Expected \"' + parameterName + '\" to be a plain object.');\n  }\n  Object.keys(object)\n    .forEach(function(key) {\n      const validator = schema[key];\n      if (!validator) {\n        if (!allowUnknown) {\n          throw new Error('\"' + key + '\" is not allowed in \"' + parameterName + '\"');\n        }\n        return;\n      }\n      if (!validator.isValid(object[key])) {\n        throw new Error(validator.message);\n      }\n    });\n}\n\nfunction validateOptions(options) {\n  return validate(sign_options_schema, false, options, 'options');\n}\n\nfunction validatePayload(payload) {\n  return validate(registered_claims_schema, true, payload, 'payload');\n}\n\nconst options_to_payload = {\n  'audience': 'aud',\n  'issuer': 'iss',\n  'subject': 'sub',\n  'jwtid': 'jti'\n};\n\nconst options_for_objects = [\n  'expiresIn',\n  'notBefore',\n  'noTimestamp',\n  'audience',\n  'issuer',\n  'subject',\n  'jwtid',\n];\n\nmodule.exports = function (payload, secretOrPrivateKey, options, callback) {\n  if (typeof options === 'function') {\n    callback = options;\n    options = {};\n  } else {\n    options = options || {};\n  }\n\n  const isObjectPayload = typeof payload === 'object' &&\n                        !Buffer.isBuffer(payload);\n\n  const header = Object.assign({\n    alg: options.algorithm || 'HS256',\n    typ: isObjectPayload ? 'JWT' : undefined,\n    kid: options.keyid\n  }, options.header);\n\n  function failure(err) {\n    if (callback) {\n      return callback(err);\n    }\n    throw err;\n  }\n\n  if (!secretOrPrivateKey && options.algorithm !== 'none') {\n    return failure(new Error('secretOrPrivateKey must have a value'));\n  }\n\n  if (secretOrPrivateKey != null && !(secretOrPrivateKey instanceof KeyObject)) {\n    try {\n      secretOrPrivateKey = createPrivateKey(secretOrPrivateKey)\n    } catch (_) {\n      try {\n        secretOrPrivateKey = createSecretKey(typeof secretOrPrivateKey === 'string' ? Buffer.from(secretOrPrivateKey) : secretOrPrivateKey)\n      } catch (_) {\n        return failure(new Error('secretOrPrivateKey is not valid key material'));\n      }\n    }\n  }\n\n  if (header.alg.startsWith('HS') && secretOrPrivateKey.type !== 'secret') {\n    return failure(new Error((`secretOrPrivateKey must be a symmetric key when using ${header.alg}`)))\n  } else if (/^(?:RS|PS|ES)/.test(header.alg)) {\n    if (secretOrPrivateKey.type !== 'private') {\n      return failure(new Error((`secretOrPrivateKey must be an asymmetric key when using ${header.alg}`)))\n    }\n    if (!options.allowInsecureKeySizes &&\n      !header.alg.startsWith('ES') &&\n      secretOrPrivateKey.asymmetricKeyDetails !== undefined && //KeyObject.asymmetricKeyDetails is supported in Node 15+\n      secretOrPrivateKey.asymmetricKeyDetails.modulusLength < 2048) {\n      return failure(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n    }\n  }\n\n  if (typeof payload === 'undefined') {\n    return failure(new Error('payload is required'));\n  } else if (isObjectPayload) {\n    try {\n      validatePayload(payload);\n    }\n    catch (error) {\n      return failure(error);\n    }\n    if (!options.mutatePayload) {\n      payload = Object.assign({},payload);\n    }\n  } else {\n    const invalid_options = options_for_objects.filter(function (opt) {\n      return typeof options[opt] !== 'undefined';\n    });\n\n    if (invalid_options.length > 0) {\n      return failure(new Error('invalid ' + invalid_options.join(',') + ' option for ' + (typeof payload ) + ' payload'));\n    }\n  }\n\n  if (typeof payload.exp !== 'undefined' && typeof options.expiresIn !== 'undefined') {\n    return failure(new Error('Bad \"options.expiresIn\" option the payload already has an \"exp\" property.'));\n  }\n\n  if (typeof payload.nbf !== 'undefined' && typeof options.notBefore !== 'undefined') {\n    return failure(new Error('Bad \"options.notBefore\" option the payload already has an \"nbf\" property.'));\n  }\n\n  try {\n    validateOptions(options);\n  }\n  catch (error) {\n    return failure(error);\n  }\n\n  if (!options.allowInvalidAsymmetricKeyTypes) {\n    try {\n      validateAsymmetricKey(header.alg, secretOrPrivateKey);\n    } catch (error) {\n      return failure(error);\n    }\n  }\n\n  const timestamp = payload.iat || Math.floor(Date.now() / 1000);\n\n  if (options.noTimestamp) {\n    delete payload.iat;\n  } else if (isObjectPayload) {\n    payload.iat = timestamp;\n  }\n\n  if (typeof options.notBefore !== 'undefined') {\n    try {\n      payload.nbf = timespan(options.notBefore, timestamp);\n    }\n    catch (err) {\n      return failure(err);\n    }\n    if (typeof payload.nbf === 'undefined') {\n      return failure(new Error('\"notBefore\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n    }\n  }\n\n  if (typeof options.expiresIn !== 'undefined' && typeof payload === 'object') {\n    try {\n      payload.exp = timespan(options.expiresIn, timestamp);\n    }\n    catch (err) {\n      return failure(err);\n    }\n    if (typeof payload.exp === 'undefined') {\n      return failure(new Error('\"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n    }\n  }\n\n  Object.keys(options_to_payload).forEach(function (key) {\n    const claim = options_to_payload[key];\n    if (typeof options[key] !== 'undefined') {\n      if (typeof payload[claim] !== 'undefined') {\n        return failure(new Error('Bad \"options.' + key + '\" option. The payload already has an \"' + claim + '\" property.'));\n      }\n      payload[claim] = options[key];\n    }\n  });\n\n  const encoding = options.encoding || 'utf8';\n\n  if (typeof callback === 'function') {\n    callback = callback && once(callback);\n\n    jws.createSign({\n      header: header,\n      privateKey: secretOrPrivateKey,\n      payload: payload,\n      encoding: encoding\n    }).once('error', callback)\n      .once('done', function (signature) {\n        // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n        if(!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n          return callback(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`))\n        }\n        callback(null, signature);\n      });\n  } else {\n    let signature = jws.sign({header: header, payload: payload, secret: secretOrPrivateKey, encoding: encoding});\n    // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n    if(!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n      throw new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`)\n    }\n    return signature\n  }\n};\n"], "names": [], "mappings": "AAAA,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,gBAAgB,EAAE;AAEtD,MAAM,iBAAiB;IAAC;IAAS;IAAS;IAAS;IAAS;IAAS;IAAS;IAAS;IAAS;IAAS;CAAO;AAChH,IAAI,cAAc;IAChB,eAAe,MAAM,CAAC,GAAG,GAAG,SAAS,SAAS;AAChD;AAEA,MAAM,sBAAsB;IAC1B,WAAW;QAAE,SAAS,SAAS,KAAK;YAAI,OAAO,UAAU,UAAW,SAAS,UAAU;QAAQ;QAAG,SAAS;IAA8E;IACzL,WAAW;QAAE,SAAS,SAAS,KAAK;YAAI,OAAO,UAAU,UAAW,SAAS,UAAU;QAAQ;QAAG,SAAS;IAA8E;IACzL,UAAU;QAAE,SAAS,SAAS,KAAK;YAAI,OAAO,SAAS,UAAU,MAAM,OAAO,CAAC;QAAQ;QAAG,SAAS;IAAuC;IAC1I,WAAW;QAAE,SAAS,SAAS,IAAI,CAAC,MAAM;QAAiB,SAAS;IAAgD;IACpH,QAAQ;QAAE,SAAS;QAAe,SAAS;IAA6B;IACxE,UAAU;QAAE,SAAS;QAAU,SAAS;IAA8B;IACtE,QAAQ;QAAE,SAAS;QAAU,SAAS;IAA4B;IAClE,SAAS;QAAE,SAAS;QAAU,SAAS;IAA6B;IACpE,OAAO;QAAE,SAAS;QAAU,SAAS;IAA2B;IAChE,aAAa;QAAE,SAAS;QAAW,SAAS;IAAkC;IAC9E,OAAO;QAAE,SAAS;QAAU,SAAS;IAA2B;IAChE,eAAe;QAAE,SAAS;QAAW,SAAS;IAAoC;IAClF,uBAAuB;QAAE,SAAS;QAAW,SAAS;IAA2C;IACjG,gCAAgC;QAAE,SAAS;QAAW,SAAS;IAAoD;AACrH;AAEA,MAAM,2BAA2B;IAC/B,KAAK;QAAE,SAAS;QAAU,SAAS;IAAsC;IACzE,KAAK;QAAE,SAAS;QAAU,SAAS;IAAsC;IACzE,KAAK;QAAE,SAAS;QAAU,SAAS;IAAsC;AAC3E;AAEA,SAAS,SAAS,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa;IAC3D,IAAI,CAAC,cAAc,SAAS;QAC1B,MAAM,IAAI,MAAM,eAAe,gBAAgB;IACjD;IACA,OAAO,IAAI,CAAC,QACT,OAAO,CAAC,SAAS,GAAG;QACnB,MAAM,YAAY,MAAM,CAAC,IAAI;QAC7B,IAAI,CAAC,WAAW;YACd,IAAI,CAAC,cAAc;gBACjB,MAAM,IAAI,MAAM,MAAM,MAAM,0BAA0B,gBAAgB;YACxE;YACA;QACF;QACA,IAAI,CAAC,UAAU,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG;YACnC,MAAM,IAAI,MAAM,UAAU,OAAO;QACnC;IACF;AACJ;AAEA,SAAS,gBAAgB,OAAO;IAC9B,OAAO,SAAS,qBAAqB,OAAO,SAAS;AACvD;AAEA,SAAS,gBAAgB,OAAO;IAC9B,OAAO,SAAS,0BAA0B,MAAM,SAAS;AAC3D;AAEA,MAAM,qBAAqB;IACzB,YAAY;IACZ,UAAU;IACV,WAAW;IACX,SAAS;AACX;AAEA,MAAM,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,OAAO,OAAO,GAAG,SAAU,OAAO,EAAE,kBAAkB,EAAE,OAAO,EAAE,QAAQ;IACvE,IAAI,OAAO,YAAY,YAAY;QACjC,WAAW;QACX,UAAU,CAAC;IACb,OAAO;QACL,UAAU,WAAW,CAAC;IACxB;IAEA,MAAM,kBAAkB,OAAO,YAAY,YACrB,CAAC,OAAO,QAAQ,CAAC;IAEvC,MAAM,SAAS,OAAO,MAAM,CAAC;QAC3B,KAAK,QAAQ,SAAS,IAAI;QAC1B,KAAK,kBAAkB,QAAQ;QAC/B,KAAK,QAAQ,KAAK;IACpB,GAAG,QAAQ,MAAM;IAEjB,SAAS,QAAQ,GAAG;QAClB,IAAI,UAAU;YACZ,OAAO,SAAS;QAClB;QACA,MAAM;IACR;IAEA,IAAI,CAAC,sBAAsB,QAAQ,SAAS,KAAK,QAAQ;QACvD,OAAO,QAAQ,IAAI,MAAM;IAC3B;IAEA,IAAI,sBAAsB,QAAQ,CAAC,CAAC,8BAA8B,SAAS,GAAG;QAC5E,IAAI;YACF,qBAAqB,iBAAiB;QACxC,EAAE,OAAO,GAAG;YACV,IAAI;gBACF,qBAAqB,gBAAgB,OAAO,uBAAuB,WAAW,OAAO,IAAI,CAAC,sBAAsB;YAClH,EAAE,OAAO,GAAG;gBACV,OAAO,QAAQ,IAAI,MAAM;YAC3B;QACF;IACF;IAEA,IAAI,OAAO,GAAG,CAAC,UAAU,CAAC,SAAS,mBAAmB,IAAI,KAAK,UAAU;QACvE,OAAO,QAAQ,IAAI,MAAO,CAAC,sDAAsD,EAAE,OAAO,GAAG,EAAE;IACjG,OAAO,IAAI,gBAAgB,IAAI,CAAC,OAAO,GAAG,GAAG;QAC3C,IAAI,mBAAmB,IAAI,KAAK,WAAW;YACzC,OAAO,QAAQ,IAAI,MAAO,CAAC,wDAAwD,EAAE,OAAO,GAAG,EAAE;QACnG;QACA,IAAI,CAAC,QAAQ,qBAAqB,IAChC,CAAC,OAAO,GAAG,CAAC,UAAU,CAAC,SACvB,mBAAmB,oBAAoB,KAAK,aAAa,yDAAyD;QAClH,mBAAmB,oBAAoB,CAAC,aAAa,GAAG,MAAM;YAC9D,OAAO,QAAQ,IAAI,MAAM,CAAC,2DAA2D,EAAE,OAAO,GAAG,EAAE;QACrG;IACF;IAEA,IAAI,OAAO,YAAY,aAAa;QAClC,OAAO,QAAQ,IAAI,MAAM;IAC3B,OAAO,IAAI,iBAAiB;QAC1B,IAAI;YACF,gBAAgB;QAClB,EACA,OAAO,OAAO;YACZ,OAAO,QAAQ;QACjB;QACA,IAAI,CAAC,QAAQ,aAAa,EAAE;YAC1B,UAAU,OAAO,MAAM,CAAC,CAAC,GAAE;QAC7B;IACF,OAAO;QACL,MAAM,kBAAkB,oBAAoB,MAAM,CAAC,SAAU,GAAG;YAC9D,OAAO,OAAO,OAAO,CAAC,IAAI,KAAK;QACjC;QAEA,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,OAAO,QAAQ,IAAI,MAAM,aAAa,gBAAgB,IAAI,CAAC,OAAO,iBAAkB,OAAO,UAAY;QACzG;IACF;IAEA,IAAI,OAAO,QAAQ,GAAG,KAAK,eAAe,OAAO,QAAQ,SAAS,KAAK,aAAa;QAClF,OAAO,QAAQ,IAAI,MAAM;IAC3B;IAEA,IAAI,OAAO,QAAQ,GAAG,KAAK,eAAe,OAAO,QAAQ,SAAS,KAAK,aAAa;QAClF,OAAO,QAAQ,IAAI,MAAM;IAC3B;IAEA,IAAI;QACF,gBAAgB;IAClB,EACA,OAAO,OAAO;QACZ,OAAO,QAAQ;IACjB;IAEA,IAAI,CAAC,QAAQ,8BAA8B,EAAE;QAC3C,IAAI;YACF,sBAAsB,OAAO,GAAG,EAAE;QACpC,EAAE,OAAO,OAAO;YACd,OAAO,QAAQ;QACjB;IACF;IAEA,MAAM,YAAY,QAAQ,GAAG,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;IAEzD,IAAI,QAAQ,WAAW,EAAE;QACvB,OAAO,QAAQ,GAAG;IACpB,OAAO,IAAI,iBAAiB;QAC1B,QAAQ,GAAG,GAAG;IAChB;IAEA,IAAI,OAAO,QAAQ,SAAS,KAAK,aAAa;QAC5C,IAAI;YACF,QAAQ,GAAG,GAAG,SAAS,QAAQ,SAAS,EAAE;QAC5C,EACA,OAAO,KAAK;YACV,OAAO,QAAQ;QACjB;QACA,IAAI,OAAO,QAAQ,GAAG,KAAK,aAAa;YACtC,OAAO,QAAQ,IAAI,MAAM;QAC3B;IACF;IAEA,IAAI,OAAO,QAAQ,SAAS,KAAK,eAAe,OAAO,YAAY,UAAU;QAC3E,IAAI;YACF,QAAQ,GAAG,GAAG,SAAS,QAAQ,SAAS,EAAE;QAC5C,EACA,OAAO,KAAK;YACV,OAAO,QAAQ;QACjB;QACA,IAAI,OAAO,QAAQ,GAAG,KAAK,aAAa;YACtC,OAAO,QAAQ,IAAI,MAAM;QAC3B;IACF;IAEA,OAAO,IAAI,CAAC,oBAAoB,OAAO,CAAC,SAAU,GAAG;QACnD,MAAM,QAAQ,kBAAkB,CAAC,IAAI;QACrC,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,aAAa;YACvC,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,aAAa;gBACzC,OAAO,QAAQ,IAAI,MAAM,kBAAkB,MAAM,2CAA2C,QAAQ;YACtG;YACA,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI;QAC/B;IACF;IAEA,MAAM,WAAW,QAAQ,QAAQ,IAAI;IAErC,IAAI,OAAO,aAAa,YAAY;QAClC,WAAW,YAAY,KAAK;QAE5B,IAAI,UAAU,CAAC;YACb,QAAQ;YACR,YAAY;YACZ,SAAS;YACT,UAAU;QACZ,GAAG,IAAI,CAAC,SAAS,UACd,IAAI,CAAC,QAAQ,SAAU,SAAS;YAC/B,kHAAkH;YAClH,IAAG,CAAC,QAAQ,qBAAqB,IAAI,aAAa,IAAI,CAAC,OAAO,GAAG,KAAK,UAAU,MAAM,GAAG,KAAK;gBAC5F,OAAO,SAAS,IAAI,MAAM,CAAC,2DAA2D,EAAE,OAAO,GAAG,EAAE;YACtG;YACA,SAAS,MAAM;QACjB;IACJ,OAAO;QACL,IAAI,YAAY,IAAI,IAAI,CAAC;YAAC,QAAQ;YAAQ,SAAS;YAAS,QAAQ;YAAoB,UAAU;QAAQ;QAC1G,kHAAkH;QAClH,IAAG,CAAC,QAAQ,qBAAqB,IAAI,aAAa,IAAI,CAAC,OAAO,GAAG,KAAK,UAAU,MAAM,GAAG,KAAK;YAC5F,MAAM,IAAI,MAAM,CAAC,2DAA2D,EAAE,OAAO,GAAG,EAAE;QAC5F;QACA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1482, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/jsonwebtoken/index.js"], "sourcesContent": ["module.exports = {\n  decode: require('./decode'),\n  verify: require('./verify'),\n  sign: require('./sign'),\n  JsonWebTokenError: require('./lib/JsonWebTokenError'),\n  NotBeforeError: require('./lib/NotBeforeError'),\n  TokenExpiredError: require('./lib/TokenExpiredError'),\n};\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG;IACf,MAAM;IACN,MAAM;IACN,IAAI;IACJ,iBAAiB;IACjB,cAAc;IACd,iBAAiB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1495, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/ms/index.js"], "sourcesContent": ["/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function (val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,IAAI,IAAI;AACR,IAAI,IAAI,IAAI;AACZ,IAAI,IAAI,IAAI;AACZ,IAAI,IAAI,IAAI;AACZ,IAAI,IAAI,IAAI;AACZ,IAAI,IAAI,IAAI;AAEZ;;;;;;;;;;;;CAYC,GAED,OAAO,OAAO,GAAG,SAAU,GAAG,EAAE,OAAO;IACrC,UAAU,WAAW,CAAC;IACtB,IAAI,OAAO,OAAO;IAClB,IAAI,SAAS,YAAY,IAAI,MAAM,GAAG,GAAG;QACvC,OAAO,MAAM;IACf,OAAO,IAAI,SAAS,YAAY,SAAS,MAAM;QAC7C,OAAO,QAAQ,IAAI,GAAG,QAAQ,OAAO,SAAS;IAChD;IACA,MAAM,IAAI,MACR,0DACE,KAAK,SAAS,CAAC;AAErB;AAEA;;;;;;CAMC,GAED,SAAS,MAAM,GAAG;IAChB,MAAM,OAAO;IACb,IAAI,IAAI,MAAM,GAAG,KAAK;QACpB;IACF;IACA,IAAI,QAAQ,mIAAmI,IAAI,CACjJ;IAEF,IAAI,CAAC,OAAO;QACV;IACF;IACA,IAAI,IAAI,WAAW,KAAK,CAAC,EAAE;IAC3B,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,EAAE,WAAW;IACzC,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA;;;;;;CAMC,GAED,SAAS,SAAS,EAAE;IAClB,IAAI,QAAQ,KAAK,GAAG,CAAC;IACrB,IAAI,SAAS,GAAG;QACd,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK;IAC9B;IACA,OAAO,KAAK;AACd;AAEA;;;;;;CAMC,GAED,SAAS,QAAQ,EAAE;IACjB,IAAI,QAAQ,KAAK,GAAG,CAAC;IACrB,IAAI,SAAS,GAAG;QACd,OAAO,OAAO,IAAI,OAAO,GAAG;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,OAAO,IAAI,OAAO,GAAG;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,OAAO,IAAI,OAAO,GAAG;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,OAAO,IAAI,OAAO,GAAG;IAC9B;IACA,OAAO,KAAK;AACd;AAEA;;CAEC,GAED,SAAS,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI;IAChC,IAAI,WAAW,SAAS,IAAI;IAC5B,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK,MAAM,OAAO,CAAC,WAAW,MAAM,EAAE;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1640, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/internal/constants.js"], "sourcesContent": ["'use strict'\n\n// Note: this is the semver.org version of the spec that it implements\n// Not necessarily the package version of this code.\nconst SEMVER_SPEC_VERSION = '2.0.0'\n\nconst MAX_LENGTH = 256\nconst MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER ||\n/* istanbul ignore next */ 9007199254740991\n\n// Max safe segment length for coercion.\nconst MAX_SAFE_COMPONENT_LENGTH = 16\n\n// Max safe length for a build identifier. The max length minus 6 characters for\n// the shortest version with a build 0.0.0+BUILD.\nconst MAX_SAFE_BUILD_LENGTH = MAX_LENGTH - 6\n\nconst RELEASE_TYPES = [\n  'major',\n  'premajor',\n  'minor',\n  'preminor',\n  'patch',\n  'prepatch',\n  'prerelease',\n]\n\nmodule.exports = {\n  MAX_LENGTH,\n  MAX_SAFE_COMPONENT_LENGTH,\n  MAX_SAFE_BUILD_LENGTH,\n  MAX_SAFE_INTEGER,\n  RELEASE_TYPES,\n  SEMVER_SPEC_VERSION,\n  FLAG_INCLUDE_PRERELEASE: 0b001,\n  FLAG_LOOSE: 0b010,\n}\n"], "names": [], "mappings": "AAAA;AAEA,sEAAsE;AACtE,oDAAoD;AACpD,MAAM,sBAAsB;AAE5B,MAAM,aAAa;AACnB,MAAM,mBAAmB,OAAO,gBAAgB,IAChD,wBAAwB,GAAG;AAE3B,wCAAwC;AACxC,MAAM,4BAA4B;AAElC,gFAAgF;AAChF,iDAAiD;AACjD,MAAM,wBAAwB,aAAa;AAE3C,MAAM,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,OAAO,OAAO,GAAG;IACf;IACA;IACA;IACA;IACA;IACA;IACA,yBAAyB;IACzB,YAAY;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1675, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/internal/debug.js"], "sourcesContent": ["'use strict'\n\nconst debug = (\n  typeof process === 'object' &&\n  process.env &&\n  process.env.NODE_DEBUG &&\n  /\\bsemver\\b/i.test(process.env.NODE_DEBUG)\n) ? (...args) => console.error('SEMVER', ...args)\n  : () => {}\n\nmodule.exports = debug\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,QAAQ,AACZ,OAAO,YAAY,YACnB,QAAQ,GAAG,IACX,QAAQ,GAAG,CAAC,UAAU,IACtB,cAAc,IAAI,CAAC,QAAQ,GAAG,CAAC,UAAU,IACvC,CAAC,GAAG,OAAS,QAAQ,KAAK,CAAC,aAAa,QACxC,KAAO;AAEX,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1683, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/internal/re.js"], "sourcesContent": ["'use strict'\n\nconst {\n  MAX_SAFE_COMPONENT_LENGTH,\n  MAX_SAFE_BUILD_LENGTH,\n  MAX_LENGTH,\n} = require('./constants')\nconst debug = require('./debug')\nexports = module.exports = {}\n\n// The actual regexps go on exports.re\nconst re = exports.re = []\nconst safeRe = exports.safeRe = []\nconst src = exports.src = []\nconst safeSrc = exports.safeSrc = []\nconst t = exports.t = {}\nlet R = 0\n\nconst LETTERDASHNUMBER = '[a-zA-Z0-9-]'\n\n// Replace some greedy regex tokens to prevent regex dos issues. These regex are\n// used internally via the safeRe object since all inputs in this library get\n// normalized first to trim and collapse all extra whitespace. The original\n// regexes are exported for userland consumption and lower level usage. A\n// future breaking change could export the safer regex only with a note that\n// all input should have extra whitespace removed.\nconst safeRegexReplacements = [\n  ['\\\\s', 1],\n  ['\\\\d', MAX_LENGTH],\n  [LETTERDASHNUMBER, MAX_SAFE_BUILD_LENGTH],\n]\n\nconst makeSafeRegex = (value) => {\n  for (const [token, max] of safeRegexReplacements) {\n    value = value\n      .split(`${token}*`).join(`${token}{0,${max}}`)\n      .split(`${token}+`).join(`${token}{1,${max}}`)\n  }\n  return value\n}\n\nconst createToken = (name, value, isGlobal) => {\n  const safe = makeSafeRegex(value)\n  const index = R++\n  debug(name, index, value)\n  t[name] = index\n  src[index] = value\n  safeSrc[index] = safe\n  re[index] = new RegExp(value, isGlobal ? 'g' : undefined)\n  safeRe[index] = new RegExp(safe, isGlobal ? 'g' : undefined)\n}\n\n// The following Regular Expressions can be used for tokenizing,\n// validating, and parsing SemVer version strings.\n\n// ## Numeric Identifier\n// A single `0`, or a non-zero digit followed by zero or more digits.\n\ncreateToken('NUMERICIDENTIFIER', '0|[1-9]\\\\d*')\ncreateToken('NUMERICIDENTIFIERLOOSE', '\\\\d+')\n\n// ## Non-numeric Identifier\n// Zero or more digits, followed by a letter or hyphen, and then zero or\n// more letters, digits, or hyphens.\n\ncreateToken('NONNUMERICIDENTIFIER', `\\\\d*[a-zA-Z-]${LETTERDASHNUMBER}*`)\n\n// ## Main Version\n// Three dot-separated numeric identifiers.\n\ncreateToken('MAINVERSION', `(${src[t.NUMERICIDENTIFIER]})\\\\.` +\n                   `(${src[t.NUMERICIDENTIFIER]})\\\\.` +\n                   `(${src[t.NUMERICIDENTIFIER]})`)\n\ncreateToken('MAINVERSIONLOOSE', `(${src[t.NUMERICIDENTIFIERLOOSE]})\\\\.` +\n                        `(${src[t.NUMERICIDENTIFIERLOOSE]})\\\\.` +\n                        `(${src[t.NUMERICIDENTIFIERLOOSE]})`)\n\n// ## Pre-release Version Identifier\n// A numeric identifier, or a non-numeric identifier.\n// Non-numberic identifiers include numberic identifiers but can be longer.\n// Therefore non-numberic identifiers must go first.\n\ncreateToken('PRERELEASEIDENTIFIER', `(?:${src[t.NONNUMERICIDENTIFIER]\n}|${src[t.NUMERICIDENTIFIER]})`)\n\ncreateToken('PRERELEASEIDENTIFIERLOOSE', `(?:${src[t.NONNUMERICIDENTIFIER]\n}|${src[t.NUMERICIDENTIFIERLOOSE]})`)\n\n// ## Pre-release Version\n// Hyphen, followed by one or more dot-separated pre-release version\n// identifiers.\n\ncreateToken('PRERELEASE', `(?:-(${src[t.PRERELEASEIDENTIFIER]\n}(?:\\\\.${src[t.PRERELEASEIDENTIFIER]})*))`)\n\ncreateToken('PRERELEASELOOSE', `(?:-?(${src[t.PRERELEASEIDENTIFIERLOOSE]\n}(?:\\\\.${src[t.PRERELEASEIDENTIFIERLOOSE]})*))`)\n\n// ## Build Metadata Identifier\n// Any combination of digits, letters, or hyphens.\n\ncreateToken('BUILDIDENTIFIER', `${LETTERDASHNUMBER}+`)\n\n// ## Build Metadata\n// Plus sign, followed by one or more period-separated build metadata\n// identifiers.\n\ncreateToken('BUILD', `(?:\\\\+(${src[t.BUILDIDENTIFIER]\n}(?:\\\\.${src[t.BUILDIDENTIFIER]})*))`)\n\n// ## Full Version String\n// A main version, followed optionally by a pre-release version and\n// build metadata.\n\n// Note that the only major, minor, patch, and pre-release sections of\n// the version string are capturing groups.  The build metadata is not a\n// capturing group, because it should not ever be used in version\n// comparison.\n\ncreateToken('FULLPLAIN', `v?${src[t.MAINVERSION]\n}${src[t.PRERELEASE]}?${\n  src[t.BUILD]}?`)\n\ncreateToken('FULL', `^${src[t.FULLPLAIN]}$`)\n\n// like full, but allows v1.2.3 and =1.2.3, which people do sometimes.\n// also, 1.0.0alpha1 (prerelease without the hyphen) which is pretty\n// common in the npm registry.\ncreateToken('LOOSEPLAIN', `[v=\\\\s]*${src[t.MAINVERSIONLOOSE]\n}${src[t.PRERELEASELOOSE]}?${\n  src[t.BUILD]}?`)\n\ncreateToken('LOOSE', `^${src[t.LOOSEPLAIN]}$`)\n\ncreateToken('GTLT', '((?:<|>)?=?)')\n\n// Something like \"2.*\" or \"1.2.x\".\n// Note that \"x.x\" is a valid xRange identifer, meaning \"any version\"\n// Only the first item is strictly required.\ncreateToken('XRANGEIDENTIFIERLOOSE', `${src[t.NUMERICIDENTIFIERLOOSE]}|x|X|\\\\*`)\ncreateToken('XRANGEIDENTIFIER', `${src[t.NUMERICIDENTIFIER]}|x|X|\\\\*`)\n\ncreateToken('XRANGEPLAIN', `[v=\\\\s]*(${src[t.XRANGEIDENTIFIER]})` +\n                   `(?:\\\\.(${src[t.XRANGEIDENTIFIER]})` +\n                   `(?:\\\\.(${src[t.XRANGEIDENTIFIER]})` +\n                   `(?:${src[t.PRERELEASE]})?${\n                     src[t.BUILD]}?` +\n                   `)?)?`)\n\ncreateToken('XRANGEPLAINLOOSE', `[v=\\\\s]*(${src[t.XRANGEIDENTIFIERLOOSE]})` +\n                        `(?:\\\\.(${src[t.XRANGEIDENTIFIERLOOSE]})` +\n                        `(?:\\\\.(${src[t.XRANGEIDENTIFIERLOOSE]})` +\n                        `(?:${src[t.PRERELEASELOOSE]})?${\n                          src[t.BUILD]}?` +\n                        `)?)?`)\n\ncreateToken('XRANGE', `^${src[t.GTLT]}\\\\s*${src[t.XRANGEPLAIN]}$`)\ncreateToken('XRANGELOOSE', `^${src[t.GTLT]}\\\\s*${src[t.XRANGEPLAINLOOSE]}$`)\n\n// Coercion.\n// Extract anything that could conceivably be a part of a valid semver\ncreateToken('COERCEPLAIN', `${'(^|[^\\\\d])' +\n              '(\\\\d{1,'}${MAX_SAFE_COMPONENT_LENGTH}})` +\n              `(?:\\\\.(\\\\d{1,${MAX_SAFE_COMPONENT_LENGTH}}))?` +\n              `(?:\\\\.(\\\\d{1,${MAX_SAFE_COMPONENT_LENGTH}}))?`)\ncreateToken('COERCE', `${src[t.COERCEPLAIN]}(?:$|[^\\\\d])`)\ncreateToken('COERCEFULL', src[t.COERCEPLAIN] +\n              `(?:${src[t.PRERELEASE]})?` +\n              `(?:${src[t.BUILD]})?` +\n              `(?:$|[^\\\\d])`)\ncreateToken('COERCERTL', src[t.COERCE], true)\ncreateToken('COERCERTLFULL', src[t.COERCEFULL], true)\n\n// Tilde ranges.\n// Meaning is \"reasonably at or greater than\"\ncreateToken('LONETILDE', '(?:~>?)')\n\ncreateToken('TILDETRIM', `(\\\\s*)${src[t.LONETILDE]}\\\\s+`, true)\nexports.tildeTrimReplace = '$1~'\n\ncreateToken('TILDE', `^${src[t.LONETILDE]}${src[t.XRANGEPLAIN]}$`)\ncreateToken('TILDELOOSE', `^${src[t.LONETILDE]}${src[t.XRANGEPLAINLOOSE]}$`)\n\n// Caret ranges.\n// Meaning is \"at least and backwards compatible with\"\ncreateToken('LONECARET', '(?:\\\\^)')\n\ncreateToken('CARETTRIM', `(\\\\s*)${src[t.LONECARET]}\\\\s+`, true)\nexports.caretTrimReplace = '$1^'\n\ncreateToken('CARET', `^${src[t.LONECARET]}${src[t.XRANGEPLAIN]}$`)\ncreateToken('CARETLOOSE', `^${src[t.LONECARET]}${src[t.XRANGEPLAINLOOSE]}$`)\n\n// A simple gt/lt/eq thing, or just \"\" to indicate \"any version\"\ncreateToken('COMPARATORLOOSE', `^${src[t.GTLT]}\\\\s*(${src[t.LOOSEPLAIN]})$|^$`)\ncreateToken('COMPARATOR', `^${src[t.GTLT]}\\\\s*(${src[t.FULLPLAIN]})$|^$`)\n\n// An expression to strip any whitespace between the gtlt and the thing\n// it modifies, so that `> 1.2.3` ==> `>1.2.3`\ncreateToken('COMPARATORTRIM', `(\\\\s*)${src[t.GTLT]\n}\\\\s*(${src[t.LOOSEPLAIN]}|${src[t.XRANGEPLAIN]})`, true)\nexports.comparatorTrimReplace = '$1$2$3'\n\n// Something like `1.2.3 - 1.2.4`\n// Note that these all use the loose form, because they'll be\n// checked against either the strict or loose comparator form\n// later.\ncreateToken('HYPHENRANGE', `^\\\\s*(${src[t.XRANGEPLAIN]})` +\n                   `\\\\s+-\\\\s+` +\n                   `(${src[t.XRANGEPLAIN]})` +\n                   `\\\\s*$`)\n\ncreateToken('HYPHENRANGELOOSE', `^\\\\s*(${src[t.XRANGEPLAINLOOSE]})` +\n                        `\\\\s+-\\\\s+` +\n                        `(${src[t.XRANGEPLAINLOOSE]})` +\n                        `\\\\s*$`)\n\n// Star ranges basically just allow anything at all.\ncreateToken('STAR', '(<|>)?=?\\\\s*\\\\*')\n// >=0.0.0 is like a star\ncreateToken('GTE0', '^\\\\s*>=\\\\s*0\\\\.0\\\\.0\\\\s*$')\ncreateToken('GTE0PRE', '^\\\\s*>=\\\\s*0\\\\.0\\\\.0-0\\\\s*$')\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,EACJ,yBAAyB,EACzB,qBAAqB,EACrB,UAAU,EACX;AACD,MAAM;AACN,UAAU,OAAO,OAAO,GAAG,CAAC;AAE5B,sCAAsC;AACtC,MAAM,KAAK,QAAQ,EAAE,GAAG,EAAE;AAC1B,MAAM,SAAS,QAAQ,MAAM,GAAG,EAAE;AAClC,MAAM,MAAM,QAAQ,GAAG,GAAG,EAAE;AAC5B,MAAM,UAAU,QAAQ,OAAO,GAAG,EAAE;AACpC,MAAM,IAAI,QAAQ,CAAC,GAAG,CAAC;AACvB,IAAI,IAAI;AAER,MAAM,mBAAmB;AAEzB,gFAAgF;AAChF,6EAA6E;AAC7E,2EAA2E;AAC3E,yEAAyE;AACzE,4EAA4E;AAC5E,kDAAkD;AAClD,MAAM,wBAAwB;IAC5B;QAAC;QAAO;KAAE;IACV;QAAC;QAAO;KAAW;IACnB;QAAC;QAAkB;KAAsB;CAC1C;AAED,MAAM,gBAAgB,CAAC;IACrB,KAAK,MAAM,CAAC,OAAO,IAAI,IAAI,sBAAuB;QAChD,QAAQ,MACL,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,GAAG,EAAE,IAAI,CAAC,CAAC,EAC5C,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,GAAG,EAAE,IAAI,CAAC,CAAC;IACjD;IACA,OAAO;AACT;AAEA,MAAM,cAAc,CAAC,MAAM,OAAO;IAChC,MAAM,OAAO,cAAc;IAC3B,MAAM,QAAQ;IACd,MAAM,MAAM,OAAO;IACnB,CAAC,CAAC,KAAK,GAAG;IACV,GAAG,CAAC,MAAM,GAAG;IACb,OAAO,CAAC,MAAM,GAAG;IACjB,EAAE,CAAC,MAAM,GAAG,IAAI,OAAO,OAAO,WAAW,MAAM;IAC/C,MAAM,CAAC,MAAM,GAAG,IAAI,OAAO,MAAM,WAAW,MAAM;AACpD;AAEA,gEAAgE;AAChE,kDAAkD;AAElD,wBAAwB;AACxB,qEAAqE;AAErE,YAAY,qBAAqB;AACjC,YAAY,0BAA0B;AAEtC,4BAA4B;AAC5B,wEAAwE;AACxE,oCAAoC;AAEpC,YAAY,wBAAwB,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;AAEvE,kBAAkB;AAClB,2CAA2C;AAE3C,YAAY,eAAe,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,iBAAiB,CAAC,CAAC,IAAI,CAAC,GAC1C,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,iBAAiB,CAAC,CAAC,IAAI,CAAC,GAClC,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAElD,YAAY,oBAAoB,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,sBAAsB,CAAC,CAAC,IAAI,CAAC,GAC/C,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,sBAAsB,CAAC,CAAC,IAAI,CAAC,GACvC,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,sBAAsB,CAAC,CAAC,CAAC,CAAC;AAE5D,oCAAoC;AACpC,qDAAqD;AACrD,2EAA2E;AAC3E,oDAAoD;AAEpD,YAAY,wBAAwB,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,oBAAoB,CAAC,CACpE,CAAC,EAAE,GAAG,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAE/B,YAAY,6BAA6B,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,oBAAoB,CAAC,CACzE,CAAC,EAAE,GAAG,CAAC,EAAE,sBAAsB,CAAC,CAAC,CAAC,CAAC;AAEpC,yBAAyB;AACzB,oEAAoE;AACpE,eAAe;AAEf,YAAY,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,oBAAoB,CAAC,CAC5D,MAAM,EAAE,GAAG,CAAC,EAAE,oBAAoB,CAAC,CAAC,IAAI,CAAC;AAE1C,YAAY,mBAAmB,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,yBAAyB,CAAC,CACvE,MAAM,EAAE,GAAG,CAAC,EAAE,yBAAyB,CAAC,CAAC,IAAI,CAAC;AAE/C,+BAA+B;AAC/B,kDAAkD;AAElD,YAAY,mBAAmB,GAAG,iBAAiB,CAAC,CAAC;AAErD,oBAAoB;AACpB,qEAAqE;AACrE,eAAe;AAEf,YAAY,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,eAAe,CAAC,CACpD,MAAM,EAAE,GAAG,CAAC,EAAE,eAAe,CAAC,CAAC,IAAI,CAAC;AAErC,yBAAyB;AACzB,mEAAmE;AACnE,kBAAkB;AAElB,sEAAsE;AACtE,wEAAwE;AACxE,iEAAiE;AACjE,cAAc;AAEd,YAAY,aAAa,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,WAAW,CAAC,GAC7C,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EACpB,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AAEjB,YAAY,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;AAE3C,sEAAsE;AACtE,oEAAoE;AACpE,8BAA8B;AAC9B,YAAY,cAAc,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,gBAAgB,CAAC,GACzD,GAAG,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EACzB,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AAEjB,YAAY,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;AAE7C,YAAY,QAAQ;AAEpB,mCAAmC;AACnC,qEAAqE;AACrE,4CAA4C;AAC5C,YAAY,yBAAyB,GAAG,GAAG,CAAC,EAAE,sBAAsB,CAAC,CAAC,QAAQ,CAAC;AAC/E,YAAY,oBAAoB,GAAG,GAAG,CAAC,EAAE,iBAAiB,CAAC,CAAC,QAAQ,CAAC;AAErE,YAAY,eAAe,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAC9C,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,GACpC,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,GACpC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,EACxB,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,GACjB,CAAC,IAAI,CAAC;AAEzB,YAAY,oBAAoB,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE,qBAAqB,CAAC,CAAC,CAAC,CAAC,GACnD,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,qBAAqB,CAAC,CAAC,CAAC,CAAC,GACzC,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,qBAAqB,CAAC,CAAC,CAAC,CAAC,GACzC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,EAC7B,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,GACjB,CAAC,IAAI,CAAC;AAE9B,YAAY,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;AACjE,YAAY,eAAe,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAE3E,YAAY;AACZ,sEAAsE;AACtE,YAAY,eAAe,GAAG,eAChB,YAAY,0BAA0B,EAAE,CAAC,GACzC,CAAC,aAAa,EAAE,0BAA0B,IAAI,CAAC,GAC/C,CAAC,aAAa,EAAE,0BAA0B,IAAI,CAAC;AAC7D,YAAY,UAAU,GAAG,GAAG,CAAC,EAAE,WAAW,CAAC,CAAC,YAAY,CAAC;AACzD,YAAY,cAAc,GAAG,CAAC,EAAE,WAAW,CAAC,GAC9B,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,GAC3B,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,GACtB,CAAC,YAAY,CAAC;AAC5B,YAAY,aAAa,GAAG,CAAC,EAAE,MAAM,CAAC,EAAE;AACxC,YAAY,iBAAiB,GAAG,CAAC,EAAE,UAAU,CAAC,EAAE;AAEhD,gBAAgB;AAChB,6CAA6C;AAC7C,YAAY,aAAa;AAEzB,YAAY,aAAa,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE;AAC1D,QAAQ,gBAAgB,GAAG;AAE3B,YAAY,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;AACjE,YAAY,cAAc,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAE3E,gBAAgB;AAChB,sDAAsD;AACtD,YAAY,aAAa;AAEzB,YAAY,aAAa,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE;AAC1D,QAAQ,gBAAgB,GAAG;AAE3B,YAAY,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;AACjE,YAAY,cAAc,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAE3E,gEAAgE;AAChE,YAAY,mBAAmB,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC,KAAK,CAAC;AAC9E,YAAY,cAAc,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC,KAAK,CAAC;AAExE,uEAAuE;AACvE,8CAA8C;AAC9C,YAAY,kBAAkB,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CACjD,KAAK,EAAE,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;AACpD,QAAQ,qBAAqB,GAAG;AAEhC,iCAAiC;AACjC,6DAA6D;AAC7D,6DAA6D;AAC7D,SAAS;AACT,YAAY,eAAe,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,GACtC,CAAC,SAAS,CAAC,GACX,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,GACzB,CAAC,KAAK,CAAC;AAE1B,YAAY,oBAAoB,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAC3C,CAAC,SAAS,CAAC,GACX,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAC9B,CAAC,KAAK,CAAC;AAE/B,oDAAoD;AACpD,YAAY,QAAQ;AACpB,yBAAyB;AACzB,YAAY,QAAQ;AACpB,YAAY,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1831, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/internal/parse-options.js"], "sourcesContent": ["'use strict'\n\n// parse out just the options we care about\nconst looseOption = Object.freeze({ loose: true })\nconst emptyOpts = Object.freeze({ })\nconst parseOptions = options => {\n  if (!options) {\n    return emptyOpts\n  }\n\n  if (typeof options !== 'object') {\n    return looseOption\n  }\n\n  return options\n}\nmodule.exports = parseOptions\n"], "names": [], "mappings": "AAAA;AAEA,2CAA2C;AAC3C,MAAM,cAAc,OAAO,MAAM,CAAC;IAAE,OAAO;AAAK;AAChD,MAAM,YAAY,OAAO,MAAM,CAAC,CAAE;AAClC,MAAM,eAAe,CAAA;IACnB,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,OAAO;IACT;IAEA,OAAO;AACT;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1852, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/internal/identifiers.js"], "sourcesContent": ["'use strict'\n\nconst numeric = /^[0-9]+$/\nconst compareIdentifiers = (a, b) => {\n  const anum = numeric.test(a)\n  const bnum = numeric.test(b)\n\n  if (anum && bnum) {\n    a = +a\n    b = +b\n  }\n\n  return a === b ? 0\n    : (anum && !bnum) ? -1\n    : (bnum && !anum) ? 1\n    : a < b ? -1\n    : 1\n}\n\nconst rcompareIdentifiers = (a, b) => compareIdentifiers(b, a)\n\nmodule.exports = {\n  compareIdentifiers,\n  rcompareIdentifiers,\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,UAAU;AAChB,MAAM,qBAAqB,CAAC,GAAG;IAC7B,MAAM,OAAO,QAAQ,IAAI,CAAC;IAC1B,MAAM,OAAO,QAAQ,IAAI,CAAC;IAE1B,IAAI,QAAQ,MAAM;QAChB,IAAI,CAAC;QACL,IAAI,CAAC;IACP;IAEA,OAAO,MAAM,IAAI,IACb,AAAC,QAAQ,CAAC,OAAQ,CAAC,IACnB,AAAC,QAAQ,CAAC,OAAQ,IAClB,IAAI,IAAI,CAAC,IACT;AACN;AAEA,MAAM,sBAAsB,CAAC,GAAG,IAAM,mBAAmB,GAAG;AAE5D,OAAO,OAAO,GAAG;IACf;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1873, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/classes/semver.js"], "sourcesContent": ["'use strict'\n\nconst debug = require('../internal/debug')\nconst { MAX_LENGTH, MAX_SAFE_INTEGER } = require('../internal/constants')\nconst { safeRe: re, t } = require('../internal/re')\n\nconst parseOptions = require('../internal/parse-options')\nconst { compareIdentifiers } = require('../internal/identifiers')\nclass SemVer {\n  constructor (version, options) {\n    options = parseOptions(options)\n\n    if (version instanceof SemVer) {\n      if (version.loose === !!options.loose &&\n        version.includePrerelease === !!options.includePrerelease) {\n        return version\n      } else {\n        version = version.version\n      }\n    } else if (typeof version !== 'string') {\n      throw new TypeError(`Invalid version. Must be a string. Got type \"${typeof version}\".`)\n    }\n\n    if (version.length > MAX_LENGTH) {\n      throw new TypeError(\n        `version is longer than ${MAX_LENGTH} characters`\n      )\n    }\n\n    debug('SemVer', version, options)\n    this.options = options\n    this.loose = !!options.loose\n    // this isn't actually relevant for versions, but keep it so that we\n    // don't run into trouble passing this.options around.\n    this.includePrerelease = !!options.includePrerelease\n\n    const m = version.trim().match(options.loose ? re[t.LOOSE] : re[t.FULL])\n\n    if (!m) {\n      throw new TypeError(`Invalid Version: ${version}`)\n    }\n\n    this.raw = version\n\n    // these are actually numbers\n    this.major = +m[1]\n    this.minor = +m[2]\n    this.patch = +m[3]\n\n    if (this.major > MAX_SAFE_INTEGER || this.major < 0) {\n      throw new TypeError('Invalid major version')\n    }\n\n    if (this.minor > MAX_SAFE_INTEGER || this.minor < 0) {\n      throw new TypeError('Invalid minor version')\n    }\n\n    if (this.patch > MAX_SAFE_INTEGER || this.patch < 0) {\n      throw new TypeError('Invalid patch version')\n    }\n\n    // numberify any prerelease numeric ids\n    if (!m[4]) {\n      this.prerelease = []\n    } else {\n      this.prerelease = m[4].split('.').map((id) => {\n        if (/^[0-9]+$/.test(id)) {\n          const num = +id\n          if (num >= 0 && num < MAX_SAFE_INTEGER) {\n            return num\n          }\n        }\n        return id\n      })\n    }\n\n    this.build = m[5] ? m[5].split('.') : []\n    this.format()\n  }\n\n  format () {\n    this.version = `${this.major}.${this.minor}.${this.patch}`\n    if (this.prerelease.length) {\n      this.version += `-${this.prerelease.join('.')}`\n    }\n    return this.version\n  }\n\n  toString () {\n    return this.version\n  }\n\n  compare (other) {\n    debug('SemVer.compare', this.version, this.options, other)\n    if (!(other instanceof SemVer)) {\n      if (typeof other === 'string' && other === this.version) {\n        return 0\n      }\n      other = new SemVer(other, this.options)\n    }\n\n    if (other.version === this.version) {\n      return 0\n    }\n\n    return this.compareMain(other) || this.comparePre(other)\n  }\n\n  compareMain (other) {\n    if (!(other instanceof SemVer)) {\n      other = new SemVer(other, this.options)\n    }\n\n    return (\n      compareIdentifiers(this.major, other.major) ||\n      compareIdentifiers(this.minor, other.minor) ||\n      compareIdentifiers(this.patch, other.patch)\n    )\n  }\n\n  comparePre (other) {\n    if (!(other instanceof SemVer)) {\n      other = new SemVer(other, this.options)\n    }\n\n    // NOT having a prerelease is > having one\n    if (this.prerelease.length && !other.prerelease.length) {\n      return -1\n    } else if (!this.prerelease.length && other.prerelease.length) {\n      return 1\n    } else if (!this.prerelease.length && !other.prerelease.length) {\n      return 0\n    }\n\n    let i = 0\n    do {\n      const a = this.prerelease[i]\n      const b = other.prerelease[i]\n      debug('prerelease compare', i, a, b)\n      if (a === undefined && b === undefined) {\n        return 0\n      } else if (b === undefined) {\n        return 1\n      } else if (a === undefined) {\n        return -1\n      } else if (a === b) {\n        continue\n      } else {\n        return compareIdentifiers(a, b)\n      }\n    } while (++i)\n  }\n\n  compareBuild (other) {\n    if (!(other instanceof SemVer)) {\n      other = new SemVer(other, this.options)\n    }\n\n    let i = 0\n    do {\n      const a = this.build[i]\n      const b = other.build[i]\n      debug('build compare', i, a, b)\n      if (a === undefined && b === undefined) {\n        return 0\n      } else if (b === undefined) {\n        return 1\n      } else if (a === undefined) {\n        return -1\n      } else if (a === b) {\n        continue\n      } else {\n        return compareIdentifiers(a, b)\n      }\n    } while (++i)\n  }\n\n  // preminor will bump the version up to the next minor release, and immediately\n  // down to pre-release. premajor and prepatch work the same way.\n  inc (release, identifier, identifierBase) {\n    if (release.startsWith('pre')) {\n      if (!identifier && identifierBase === false) {\n        throw new Error('invalid increment argument: identifier is empty')\n      }\n      // Avoid an invalid semver results\n      if (identifier) {\n        const match = `-${identifier}`.match(this.options.loose ? re[t.PRERELEASELOOSE] : re[t.PRERELEASE])\n        if (!match || match[1] !== identifier) {\n          throw new Error(`invalid identifier: ${identifier}`)\n        }\n      }\n    }\n\n    switch (release) {\n      case 'premajor':\n        this.prerelease.length = 0\n        this.patch = 0\n        this.minor = 0\n        this.major++\n        this.inc('pre', identifier, identifierBase)\n        break\n      case 'preminor':\n        this.prerelease.length = 0\n        this.patch = 0\n        this.minor++\n        this.inc('pre', identifier, identifierBase)\n        break\n      case 'prepatch':\n        // If this is already a prerelease, it will bump to the next version\n        // drop any prereleases that might already exist, since they are not\n        // relevant at this point.\n        this.prerelease.length = 0\n        this.inc('patch', identifier, identifierBase)\n        this.inc('pre', identifier, identifierBase)\n        break\n      // If the input is a non-prerelease version, this acts the same as\n      // prepatch.\n      case 'prerelease':\n        if (this.prerelease.length === 0) {\n          this.inc('patch', identifier, identifierBase)\n        }\n        this.inc('pre', identifier, identifierBase)\n        break\n      case 'release':\n        if (this.prerelease.length === 0) {\n          throw new Error(`version ${this.raw} is not a prerelease`)\n        }\n        this.prerelease.length = 0\n        break\n\n      case 'major':\n        // If this is a pre-major version, bump up to the same major version.\n        // Otherwise increment major.\n        // 1.0.0-5 bumps to 1.0.0\n        // 1.1.0 bumps to 2.0.0\n        if (\n          this.minor !== 0 ||\n          this.patch !== 0 ||\n          this.prerelease.length === 0\n        ) {\n          this.major++\n        }\n        this.minor = 0\n        this.patch = 0\n        this.prerelease = []\n        break\n      case 'minor':\n        // If this is a pre-minor version, bump up to the same minor version.\n        // Otherwise increment minor.\n        // 1.2.0-5 bumps to 1.2.0\n        // 1.2.1 bumps to 1.3.0\n        if (this.patch !== 0 || this.prerelease.length === 0) {\n          this.minor++\n        }\n        this.patch = 0\n        this.prerelease = []\n        break\n      case 'patch':\n        // If this is not a pre-release version, it will increment the patch.\n        // If it is a pre-release it will bump up to the same patch version.\n        // 1.2.0-5 patches to 1.2.0\n        // 1.2.0 patches to 1.2.1\n        if (this.prerelease.length === 0) {\n          this.patch++\n        }\n        this.prerelease = []\n        break\n      // This probably shouldn't be used publicly.\n      // 1.0.0 'pre' would become 1.0.0-0 which is the wrong direction.\n      case 'pre': {\n        const base = Number(identifierBase) ? 1 : 0\n\n        if (this.prerelease.length === 0) {\n          this.prerelease = [base]\n        } else {\n          let i = this.prerelease.length\n          while (--i >= 0) {\n            if (typeof this.prerelease[i] === 'number') {\n              this.prerelease[i]++\n              i = -2\n            }\n          }\n          if (i === -1) {\n            // didn't increment anything\n            if (identifier === this.prerelease.join('.') && identifierBase === false) {\n              throw new Error('invalid increment argument: identifier already exists')\n            }\n            this.prerelease.push(base)\n          }\n        }\n        if (identifier) {\n          // 1.2.0-beta.1 bumps to 1.2.0-beta.2,\n          // 1.2.0-beta.fooblz or 1.2.0-beta bumps to 1.2.0-beta.0\n          let prerelease = [identifier, base]\n          if (identifierBase === false) {\n            prerelease = [identifier]\n          }\n          if (compareIdentifiers(this.prerelease[0], identifier) === 0) {\n            if (isNaN(this.prerelease[1])) {\n              this.prerelease = prerelease\n            }\n          } else {\n            this.prerelease = prerelease\n          }\n        }\n        break\n      }\n      default:\n        throw new Error(`invalid increment argument: ${release}`)\n    }\n    this.raw = this.format()\n    if (this.build.length) {\n      this.raw += `+${this.build.join('.')}`\n    }\n    return this\n  }\n}\n\nmodule.exports = SemVer\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE;AACtC,MAAM,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE;AAEvB,MAAM;AACN,MAAM,EAAE,kBAAkB,EAAE;AAC5B,MAAM;IACJ,YAAa,OAAO,EAAE,OAAO,CAAE;QAC7B,UAAU,aAAa;QAEvB,IAAI,mBAAmB,QAAQ;YAC7B,IAAI,QAAQ,KAAK,KAAK,CAAC,CAAC,QAAQ,KAAK,IACnC,QAAQ,iBAAiB,KAAK,CAAC,CAAC,QAAQ,iBAAiB,EAAE;gBAC3D,OAAO;YACT,OAAO;gBACL,UAAU,QAAQ,OAAO;YAC3B;QACF,OAAO,IAAI,OAAO,YAAY,UAAU;YACtC,MAAM,IAAI,UAAU,CAAC,6CAA6C,EAAE,OAAO,QAAQ,EAAE,CAAC;QACxF;QAEA,IAAI,QAAQ,MAAM,GAAG,YAAY;YAC/B,MAAM,IAAI,UACR,CAAC,uBAAuB,EAAE,WAAW,WAAW,CAAC;QAErD;QAEA,MAAM,UAAU,SAAS;QACzB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,QAAQ,KAAK;QAC5B,oEAAoE;QACpE,sDAAsD;QACtD,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,QAAQ,iBAAiB;QAEpD,MAAM,IAAI,QAAQ,IAAI,GAAG,KAAK,CAAC,QAAQ,KAAK,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC;QAEvE,IAAI,CAAC,GAAG;YACN,MAAM,IAAI,UAAU,CAAC,iBAAiB,EAAE,SAAS;QACnD;QAEA,IAAI,CAAC,GAAG,GAAG;QAEX,6BAA6B;QAC7B,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;QAClB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;QAClB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;QAElB,IAAI,IAAI,CAAC,KAAK,GAAG,oBAAoB,IAAI,CAAC,KAAK,GAAG,GAAG;YACnD,MAAM,IAAI,UAAU;QACtB;QAEA,IAAI,IAAI,CAAC,KAAK,GAAG,oBAAoB,IAAI,CAAC,KAAK,GAAG,GAAG;YACnD,MAAM,IAAI,UAAU;QACtB;QAEA,IAAI,IAAI,CAAC,KAAK,GAAG,oBAAoB,IAAI,CAAC,KAAK,GAAG,GAAG;YACnD,MAAM,IAAI,UAAU;QACtB;QAEA,uCAAuC;QACvC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,IAAI,CAAC,UAAU,GAAG,EAAE;QACtB,OAAO;YACL,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;gBACrC,IAAI,WAAW,IAAI,CAAC,KAAK;oBACvB,MAAM,MAAM,CAAC;oBACb,IAAI,OAAO,KAAK,MAAM,kBAAkB;wBACtC,OAAO;oBACT;gBACF;gBACA,OAAO;YACT;QACF;QAEA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE;QACxC,IAAI,CAAC,MAAM;IACb;IAEA,SAAU;QACR,IAAI,CAAC,OAAO,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE;QAC1D,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YAC1B,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM;QACjD;QACA,OAAO,IAAI,CAAC,OAAO;IACrB;IAEA,WAAY;QACV,OAAO,IAAI,CAAC,OAAO;IACrB;IAEA,QAAS,KAAK,EAAE;QACd,MAAM,kBAAkB,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QACpD,IAAI,CAAC,CAAC,iBAAiB,MAAM,GAAG;YAC9B,IAAI,OAAO,UAAU,YAAY,UAAU,IAAI,CAAC,OAAO,EAAE;gBACvD,OAAO;YACT;YACA,QAAQ,IAAI,OAAO,OAAO,IAAI,CAAC,OAAO;QACxC;QAEA,IAAI,MAAM,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;YAClC,OAAO;QACT;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC;IACpD;IAEA,YAAa,KAAK,EAAE;QAClB,IAAI,CAAC,CAAC,iBAAiB,MAAM,GAAG;YAC9B,QAAQ,IAAI,OAAO,OAAO,IAAI,CAAC,OAAO;QACxC;QAEA,OACE,mBAAmB,IAAI,CAAC,KAAK,EAAE,MAAM,KAAK,KAC1C,mBAAmB,IAAI,CAAC,KAAK,EAAE,MAAM,KAAK,KAC1C,mBAAmB,IAAI,CAAC,KAAK,EAAE,MAAM,KAAK;IAE9C;IAEA,WAAY,KAAK,EAAE;QACjB,IAAI,CAAC,CAAC,iBAAiB,MAAM,GAAG;YAC9B,QAAQ,IAAI,OAAO,OAAO,IAAI,CAAC,OAAO;QACxC;QAEA,0CAA0C;QAC1C,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,MAAM,UAAU,CAAC,MAAM,EAAE;YACtD,OAAO,CAAC;QACV,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,MAAM,UAAU,CAAC,MAAM,EAAE;YAC7D,OAAO;QACT,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,MAAM,UAAU,CAAC,MAAM,EAAE;YAC9D,OAAO;QACT;QAEA,IAAI,IAAI;QACR,GAAG;YACD,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE;YAC5B,MAAM,IAAI,MAAM,UAAU,CAAC,EAAE;YAC7B,MAAM,sBAAsB,GAAG,GAAG;YAClC,IAAI,MAAM,aAAa,MAAM,WAAW;gBACtC,OAAO;YACT,OAAO,IAAI,MAAM,WAAW;gBAC1B,OAAO;YACT,OAAO,IAAI,MAAM,WAAW;gBAC1B,OAAO,CAAC;YACV,OAAO,IAAI,MAAM,GAAG;gBAClB;YACF,OAAO;gBACL,OAAO,mBAAmB,GAAG;YAC/B;QACF,QAAS,EAAE,EAAE;IACf;IAEA,aAAc,KAAK,EAAE;QACnB,IAAI,CAAC,CAAC,iBAAiB,MAAM,GAAG;YAC9B,QAAQ,IAAI,OAAO,OAAO,IAAI,CAAC,OAAO;QACxC;QAEA,IAAI,IAAI;QACR,GAAG;YACD,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;YACvB,MAAM,IAAI,MAAM,KAAK,CAAC,EAAE;YACxB,MAAM,iBAAiB,GAAG,GAAG;YAC7B,IAAI,MAAM,aAAa,MAAM,WAAW;gBACtC,OAAO;YACT,OAAO,IAAI,MAAM,WAAW;gBAC1B,OAAO;YACT,OAAO,IAAI,MAAM,WAAW;gBAC1B,OAAO,CAAC;YACV,OAAO,IAAI,MAAM,GAAG;gBAClB;YACF,OAAO;gBACL,OAAO,mBAAmB,GAAG;YAC/B;QACF,QAAS,EAAE,EAAE;IACf;IAEA,+EAA+E;IAC/E,gEAAgE;IAChE,IAAK,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE;QACxC,IAAI,QAAQ,UAAU,CAAC,QAAQ;YAC7B,IAAI,CAAC,cAAc,mBAAmB,OAAO;gBAC3C,MAAM,IAAI,MAAM;YAClB;YACA,kCAAkC;YAClC,IAAI,YAAY;gBACd,MAAM,QAAQ,CAAC,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC,EAAE,eAAe,CAAC,GAAG,EAAE,CAAC,EAAE,UAAU,CAAC;gBAClG,IAAI,CAAC,SAAS,KAAK,CAAC,EAAE,KAAK,YAAY;oBACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,YAAY;gBACrD;YACF;QACF;QAEA,OAAQ;YACN,KAAK;gBACH,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG;gBACzB,IAAI,CAAC,KAAK,GAAG;gBACb,IAAI,CAAC,KAAK,GAAG;gBACb,IAAI,CAAC,KAAK;gBACV,IAAI,CAAC,GAAG,CAAC,OAAO,YAAY;gBAC5B;YACF,KAAK;gBACH,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG;gBACzB,IAAI,CAAC,KAAK,GAAG;gBACb,IAAI,CAAC,KAAK;gBACV,IAAI,CAAC,GAAG,CAAC,OAAO,YAAY;gBAC5B;YACF,KAAK;gBACH,oEAAoE;gBACpE,oEAAoE;gBACpE,0BAA0B;gBAC1B,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG;gBACzB,IAAI,CAAC,GAAG,CAAC,SAAS,YAAY;gBAC9B,IAAI,CAAC,GAAG,CAAC,OAAO,YAAY;gBAC5B;YACF,kEAAkE;YAClE,YAAY;YACZ,KAAK;gBACH,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,GAAG;oBAChC,IAAI,CAAC,GAAG,CAAC,SAAS,YAAY;gBAChC;gBACA,IAAI,CAAC,GAAG,CAAC,OAAO,YAAY;gBAC5B;YACF,KAAK;gBACH,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,GAAG;oBAChC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC;gBAC3D;gBACA,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG;gBACzB;YAEF,KAAK;gBACH,qEAAqE;gBACrE,6BAA6B;gBAC7B,yBAAyB;gBACzB,uBAAuB;gBACvB,IACE,IAAI,CAAC,KAAK,KAAK,KACf,IAAI,CAAC,KAAK,KAAK,KACf,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,GAC3B;oBACA,IAAI,CAAC,KAAK;gBACZ;gBACA,IAAI,CAAC,KAAK,GAAG;gBACb,IAAI,CAAC,KAAK,GAAG;gBACb,IAAI,CAAC,UAAU,GAAG,EAAE;gBACpB;YACF,KAAK;gBACH,qEAAqE;gBACrE,6BAA6B;gBAC7B,yBAAyB;gBACzB,uBAAuB;gBACvB,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,GAAG;oBACpD,IAAI,CAAC,KAAK;gBACZ;gBACA,IAAI,CAAC,KAAK,GAAG;gBACb,IAAI,CAAC,UAAU,GAAG,EAAE;gBACpB;YACF,KAAK;gBACH,qEAAqE;gBACrE,oEAAoE;gBACpE,2BAA2B;gBAC3B,yBAAyB;gBACzB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,GAAG;oBAChC,IAAI,CAAC,KAAK;gBACZ;gBACA,IAAI,CAAC,UAAU,GAAG,EAAE;gBACpB;YACF,4CAA4C;YAC5C,iEAAiE;YACjE,KAAK;gBAAO;oBACV,MAAM,OAAO,OAAO,kBAAkB,IAAI;oBAE1C,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,GAAG;wBAChC,IAAI,CAAC,UAAU,GAAG;4BAAC;yBAAK;oBAC1B,OAAO;wBACL,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM;wBAC9B,MAAO,EAAE,KAAK,EAAG;4BACf,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,UAAU;gCAC1C,IAAI,CAAC,UAAU,CAAC,EAAE;gCAClB,IAAI,CAAC;4BACP;wBACF;wBACA,IAAI,MAAM,CAAC,GAAG;4BACZ,4BAA4B;4BAC5B,IAAI,eAAe,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,mBAAmB,OAAO;gCACxE,MAAM,IAAI,MAAM;4BAClB;4BACA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;wBACvB;oBACF;oBACA,IAAI,YAAY;wBACd,sCAAsC;wBACtC,wDAAwD;wBACxD,IAAI,aAAa;4BAAC;4BAAY;yBAAK;wBACnC,IAAI,mBAAmB,OAAO;4BAC5B,aAAa;gCAAC;6BAAW;wBAC3B;wBACA,IAAI,mBAAmB,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,gBAAgB,GAAG;4BAC5D,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG;gCAC7B,IAAI,CAAC,UAAU,GAAG;4BACpB;wBACF,OAAO;4BACL,IAAI,CAAC,UAAU,GAAG;wBACpB;oBACF;oBACA;gBACF;YACA;gBACE,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,SAAS;QAC5D;QACA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;QACtB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACrB,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM;QACxC;QACA,OAAO,IAAI;IACb;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/parse.js"], "sourcesContent": ["'use strict'\n\nconst SemVer = require('../classes/semver')\nconst parse = (version, options, throwErrors = false) => {\n  if (version instanceof SemVer) {\n    return version\n  }\n  try {\n    return new SemVer(version, options)\n  } catch (er) {\n    if (!throwErrors) {\n      return null\n    }\n    throw er\n  }\n}\n\nmodule.exports = parse\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,QAAQ,CAAC,SAAS,SAAS,cAAc,KAAK;IAClD,IAAI,mBAAmB,QAAQ;QAC7B,OAAO;IACT;IACA,IAAI;QACF,OAAO,IAAI,OAAO,SAAS;IAC7B,EAAE,OAAO,IAAI;QACX,IAAI,CAAC,aAAa;YAChB,OAAO;QACT;QACA,MAAM;IACR;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/valid.js"], "sourcesContent": ["'use strict'\n\nconst parse = require('./parse')\nconst valid = (version, options) => {\n  const v = parse(version, options)\n  return v ? v.version : null\n}\nmodule.exports = valid\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,QAAQ,CAAC,SAAS;IACtB,MAAM,IAAI,MAAM,SAAS;IACzB,OAAO,IAAI,EAAE,OAAO,GAAG;AACzB;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/clean.js"], "sourcesContent": ["'use strict'\n\nconst parse = require('./parse')\nconst clean = (version, options) => {\n  const s = parse(version.trim().replace(/^[=v]+/, ''), options)\n  return s ? s.version : null\n}\nmodule.exports = clean\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,QAAQ,CAAC,SAAS;IACtB,MAAM,IAAI,MAAM,QAAQ,IAAI,GAAG,OAAO,CAAC,UAAU,KAAK;IACtD,OAAO,IAAI,EAAE,OAAO,GAAG;AACzB;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/inc.js"], "sourcesContent": ["'use strict'\n\nconst SemVer = require('../classes/semver')\n\nconst inc = (version, release, options, identifier, identifierBase) => {\n  if (typeof (options) === 'string') {\n    identifierBase = identifier\n    identifier = options\n    options = undefined\n  }\n\n  try {\n    return new SemVer(\n      version instanceof SemVer ? version.version : version,\n      options\n    ).inc(release, identifier, identifierBase).version\n  } catch (er) {\n    return null\n  }\n}\nmodule.exports = inc\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AAEN,MAAM,MAAM,CAAC,SAAS,SAAS,SAAS,YAAY;IAClD,IAAI,OAAQ,YAAa,UAAU;QACjC,iBAAiB;QACjB,aAAa;QACb,UAAU;IACZ;IAEA,IAAI;QACF,OAAO,IAAI,OACT,mBAAmB,SAAS,QAAQ,OAAO,GAAG,SAC9C,SACA,GAAG,CAAC,SAAS,YAAY,gBAAgB,OAAO;IACpD,EAAE,OAAO,IAAI;QACX,OAAO;IACT;AACF;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/diff.js"], "sourcesContent": ["'use strict'\n\nconst parse = require('./parse.js')\n\nconst diff = (version1, version2) => {\n  const v1 = parse(version1, null, true)\n  const v2 = parse(version2, null, true)\n  const comparison = v1.compare(v2)\n\n  if (comparison === 0) {\n    return null\n  }\n\n  const v1Higher = comparison > 0\n  const highVersion = v1Higher ? v1 : v2\n  const lowVersion = v1Higher ? v2 : v1\n  const highHasPre = !!highVersion.prerelease.length\n  const lowHasPre = !!lowVersion.prerelease.length\n\n  if (lowHasPre && !highHasPre) {\n    // Going from prerelease -> no prerelease requires some special casing\n\n    // If the low version has only a major, then it will always be a major\n    // Some examples:\n    // 1.0.0-1 -> 1.0.0\n    // 1.0.0-1 -> 1.1.1\n    // 1.0.0-1 -> 2.0.0\n    if (!lowVersion.patch && !lowVersion.minor) {\n      return 'major'\n    }\n\n    // If the main part has no difference\n    if (lowVersion.compareMain(highVersion) === 0) {\n      if (lowVersion.minor && !lowVersion.patch) {\n        return 'minor'\n      }\n      return 'patch'\n    }\n  }\n\n  // add the `pre` prefix if we are going to a prerelease version\n  const prefix = highHasPre ? 'pre' : ''\n\n  if (v1.major !== v2.major) {\n    return prefix + 'major'\n  }\n\n  if (v1.minor !== v2.minor) {\n    return prefix + 'minor'\n  }\n\n  if (v1.patch !== v2.patch) {\n    return prefix + 'patch'\n  }\n\n  // high and low are preleases\n  return 'prerelease'\n}\n\nmodule.exports = diff\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AAEN,MAAM,OAAO,CAAC,UAAU;IACtB,MAAM,KAAK,MAAM,UAAU,MAAM;IACjC,MAAM,KAAK,MAAM,UAAU,MAAM;IACjC,MAAM,aAAa,GAAG,OAAO,CAAC;IAE9B,IAAI,eAAe,GAAG;QACpB,OAAO;IACT;IAEA,MAAM,WAAW,aAAa;IAC9B,MAAM,cAAc,WAAW,KAAK;IACpC,MAAM,aAAa,WAAW,KAAK;IACnC,MAAM,aAAa,CAAC,CAAC,YAAY,UAAU,CAAC,MAAM;IAClD,MAAM,YAAY,CAAC,CAAC,WAAW,UAAU,CAAC,MAAM;IAEhD,IAAI,aAAa,CAAC,YAAY;QAC5B,sEAAsE;QAEtE,sEAAsE;QACtE,iBAAiB;QACjB,mBAAmB;QACnB,mBAAmB;QACnB,mBAAmB;QACnB,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,KAAK,EAAE;YAC1C,OAAO;QACT;QAEA,qCAAqC;QACrC,IAAI,WAAW,WAAW,CAAC,iBAAiB,GAAG;YAC7C,IAAI,WAAW,KAAK,IAAI,CAAC,WAAW,KAAK,EAAE;gBACzC,OAAO;YACT;YACA,OAAO;QACT;IACF;IAEA,+DAA+D;IAC/D,MAAM,SAAS,aAAa,QAAQ;IAEpC,IAAI,GAAG,KAAK,KAAK,GAAG,KAAK,EAAE;QACzB,OAAO,SAAS;IAClB;IAEA,IAAI,GAAG,KAAK,KAAK,GAAG,KAAK,EAAE;QACzB,OAAO,SAAS;IAClB;IAEA,IAAI,GAAG,KAAK,KAAK,GAAG,KAAK,EAAE;QACzB,OAAO,SAAS;IAClB;IAEA,6BAA6B;IAC7B,OAAO;AACT;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2280, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/major.js"], "sourcesContent": ["'use strict'\n\nconst SemVer = require('../classes/semver')\nconst major = (a, loose) => new SemVer(a, loose).major\nmodule.exports = major\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,QAAQ,CAAC,GAAG,QAAU,IAAI,OAAO,GAAG,OAAO,KAAK;AACtD,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2289, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/minor.js"], "sourcesContent": ["'use strict'\n\nconst SemVer = require('../classes/semver')\nconst minor = (a, loose) => new SemVer(a, loose).minor\nmodule.exports = minor\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,QAAQ,CAAC,GAAG,QAAU,IAAI,OAAO,GAAG,OAAO,KAAK;AACtD,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/patch.js"], "sourcesContent": ["'use strict'\n\nconst SemVer = require('../classes/semver')\nconst patch = (a, loose) => new SemVer(a, loose).patch\nmodule.exports = patch\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,QAAQ,CAAC,GAAG,QAAU,IAAI,OAAO,GAAG,OAAO,KAAK;AACtD,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2307, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/prerelease.js"], "sourcesContent": ["'use strict'\n\nconst parse = require('./parse')\nconst prerelease = (version, options) => {\n  const parsed = parse(version, options)\n  return (parsed && parsed.prerelease.length) ? parsed.prerelease : null\n}\nmodule.exports = prerelease\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,aAAa,CAAC,SAAS;IAC3B,MAAM,SAAS,MAAM,SAAS;IAC9B,OAAO,AAAC,UAAU,OAAO,UAAU,CAAC,MAAM,GAAI,OAAO,UAAU,GAAG;AACpE;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/compare.js"], "sourcesContent": ["'use strict'\n\nconst SemVer = require('../classes/semver')\nconst compare = (a, b, loose) =>\n  new SemVer(a, loose).compare(new SemVer(b, loose))\n\nmodule.exports = compare\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,UAAU,CAAC,GAAG,GAAG,QACrB,IAAI,OAAO,GAAG,OAAO,OAAO,CAAC,IAAI,OAAO,GAAG;AAE7C,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2328, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/rcompare.js"], "sourcesContent": ["'use strict'\n\nconst compare = require('./compare')\nconst rcompare = (a, b, loose) => compare(b, a, loose)\nmodule.exports = rcompare\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,WAAW,CAAC,GAAG,GAAG,QAAU,QAAQ,GAAG,GAAG;AAChD,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/compare-loose.js"], "sourcesContent": ["'use strict'\n\nconst compare = require('./compare')\nconst compareLoose = (a, b) => compare(a, b, true)\nmodule.exports = compareLoose\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,eAAe,CAAC,GAAG,IAAM,QAAQ,GAAG,GAAG;AAC7C,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2346, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/compare-build.js"], "sourcesContent": ["'use strict'\n\nconst SemVer = require('../classes/semver')\nconst compareBuild = (a, b, loose) => {\n  const versionA = new SemVer(a, loose)\n  const versionB = new SemVer(b, loose)\n  return versionA.compare(versionB) || versionA.compareBuild(versionB)\n}\nmodule.exports = compareBuild\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,eAAe,CAAC,GAAG,GAAG;IAC1B,MAAM,WAAW,IAAI,OAAO,GAAG;IAC/B,MAAM,WAAW,IAAI,OAAO,GAAG;IAC/B,OAAO,SAAS,OAAO,CAAC,aAAa,SAAS,YAAY,CAAC;AAC7D;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/sort.js"], "sourcesContent": ["'use strict'\n\nconst compareBuild = require('./compare-build')\nconst sort = (list, loose) => list.sort((a, b) => compareBuild(a, b, loose))\nmodule.exports = sort\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,OAAO,CAAC,MAAM,QAAU,KAAK,IAAI,CAAC,CAAC,GAAG,IAAM,aAAa,GAAG,GAAG;AACrE,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2368, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/rsort.js"], "sourcesContent": ["'use strict'\n\nconst compareBuild = require('./compare-build')\nconst rsort = (list, loose) => list.sort((a, b) => compareBuild(b, a, loose))\nmodule.exports = rsort\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,QAAQ,CAAC,MAAM,QAAU,KAAK,IAAI,CAAC,CAAC,GAAG,IAAM,aAAa,GAAG,GAAG;AACtE,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/gt.js"], "sourcesContent": ["'use strict'\n\nconst compare = require('./compare')\nconst gt = (a, b, loose) => compare(a, b, loose) > 0\nmodule.exports = gt\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,KAAK,CAAC,GAAG,GAAG,QAAU,QAAQ,GAAG,GAAG,SAAS;AACnD,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/lt.js"], "sourcesContent": ["'use strict'\n\nconst compare = require('./compare')\nconst lt = (a, b, loose) => compare(a, b, loose) < 0\nmodule.exports = lt\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,KAAK,CAAC,GAAG,GAAG,QAAU,QAAQ,GAAG,GAAG,SAAS;AACnD,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2395, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/eq.js"], "sourcesContent": ["'use strict'\n\nconst compare = require('./compare')\nconst eq = (a, b, loose) => compare(a, b, loose) === 0\nmodule.exports = eq\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,KAAK,CAAC,GAAG,GAAG,QAAU,QAAQ,GAAG,GAAG,WAAW;AACrD,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/neq.js"], "sourcesContent": ["'use strict'\n\nconst compare = require('./compare')\nconst neq = (a, b, loose) => compare(a, b, loose) !== 0\nmodule.exports = neq\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,MAAM,CAAC,GAAG,GAAG,QAAU,QAAQ,GAAG,GAAG,WAAW;AACtD,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/gte.js"], "sourcesContent": ["'use strict'\n\nconst compare = require('./compare')\nconst gte = (a, b, loose) => compare(a, b, loose) >= 0\nmodule.exports = gte\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,MAAM,CAAC,GAAG,GAAG,QAAU,QAAQ,GAAG,GAAG,UAAU;AACrD,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2422, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/lte.js"], "sourcesContent": ["'use strict'\n\nconst compare = require('./compare')\nconst lte = (a, b, loose) => compare(a, b, loose) <= 0\nmodule.exports = lte\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,MAAM,CAAC,GAAG,GAAG,QAAU,QAAQ,GAAG,GAAG,UAAU;AACrD,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/cmp.js"], "sourcesContent": ["'use strict'\n\nconst eq = require('./eq')\nconst neq = require('./neq')\nconst gt = require('./gt')\nconst gte = require('./gte')\nconst lt = require('./lt')\nconst lte = require('./lte')\n\nconst cmp = (a, op, b, loose) => {\n  switch (op) {\n    case '===':\n      if (typeof a === 'object') {\n        a = a.version\n      }\n      if (typeof b === 'object') {\n        b = b.version\n      }\n      return a === b\n\n    case '!==':\n      if (typeof a === 'object') {\n        a = a.version\n      }\n      if (typeof b === 'object') {\n        b = b.version\n      }\n      return a !== b\n\n    case '':\n    case '=':\n    case '==':\n      return eq(a, b, loose)\n\n    case '!=':\n      return neq(a, b, loose)\n\n    case '>':\n      return gt(a, b, loose)\n\n    case '>=':\n      return gte(a, b, loose)\n\n    case '<':\n      return lt(a, b, loose)\n\n    case '<=':\n      return lte(a, b, loose)\n\n    default:\n      throw new TypeError(`Invalid operator: ${op}`)\n  }\n}\nmodule.exports = cmp\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AAEN,MAAM,MAAM,CAAC,GAAG,IAAI,GAAG;IACrB,OAAQ;QACN,KAAK;YACH,IAAI,OAAO,MAAM,UAAU;gBACzB,IAAI,EAAE,OAAO;YACf;YACA,IAAI,OAAO,MAAM,UAAU;gBACzB,IAAI,EAAE,OAAO;YACf;YACA,OAAO,MAAM;QAEf,KAAK;YACH,IAAI,OAAO,MAAM,UAAU;gBACzB,IAAI,EAAE,OAAO;YACf;YACA,IAAI,OAAO,MAAM,UAAU;gBACzB,IAAI,EAAE,OAAO;YACf;YACA,OAAO,MAAM;QAEf,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,GAAG,GAAG,GAAG;QAElB,KAAK;YACH,OAAO,IAAI,GAAG,GAAG;QAEnB,KAAK;YACH,OAAO,GAAG,GAAG,GAAG;QAElB,KAAK;YACH,OAAO,IAAI,GAAG,GAAG;QAEnB,KAAK;YACH,OAAO,GAAG,GAAG,GAAG;QAElB,KAAK;YACH,OAAO,IAAI,GAAG,GAAG;QAEnB;YACE,MAAM,IAAI,UAAU,CAAC,kBAAkB,EAAE,IAAI;IACjD;AACF;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2480, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/coerce.js"], "sourcesContent": ["'use strict'\n\nconst SemVer = require('../classes/semver')\nconst parse = require('./parse')\nconst { safeRe: re, t } = require('../internal/re')\n\nconst coerce = (version, options) => {\n  if (version instanceof SemVer) {\n    return version\n  }\n\n  if (typeof version === 'number') {\n    version = String(version)\n  }\n\n  if (typeof version !== 'string') {\n    return null\n  }\n\n  options = options || {}\n\n  let match = null\n  if (!options.rtl) {\n    match = version.match(options.includePrerelease ? re[t.COERCEFULL] : re[t.COERCE])\n  } else {\n    // Find the right-most coercible string that does not share\n    // a terminus with a more left-ward coercible string.\n    // Eg, '1.2.3.4' wants to coerce '2.3.4', not '3.4' or '4'\n    // With includePrerelease option set, '1.2.3.4-rc' wants to coerce '2.3.4-rc', not '2.3.4'\n    //\n    // Walk through the string checking with a /g regexp\n    // Manually set the index so as to pick up overlapping matches.\n    // Stop when we get a match that ends at the string end, since no\n    // coercible string can be more right-ward without the same terminus.\n    const coerceRtlRegex = options.includePrerelease ? re[t.COERCERTLFULL] : re[t.COERCERTL]\n    let next\n    while ((next = coerceRtlRegex.exec(version)) &&\n        (!match || match.index + match[0].length !== version.length)\n    ) {\n      if (!match ||\n            next.index + next[0].length !== match.index + match[0].length) {\n        match = next\n      }\n      coerceRtlRegex.lastIndex = next.index + next[1].length + next[2].length\n    }\n    // leave it in a clean state\n    coerceRtlRegex.lastIndex = -1\n  }\n\n  if (match === null) {\n    return null\n  }\n\n  const major = match[2]\n  const minor = match[3] || '0'\n  const patch = match[4] || '0'\n  const prerelease = options.includePrerelease && match[5] ? `-${match[5]}` : ''\n  const build = options.includePrerelease && match[6] ? `+${match[6]}` : ''\n\n  return parse(`${major}.${minor}.${patch}${prerelease}${build}`, options)\n}\nmodule.exports = coerce\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM;AACN,MAAM,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE;AAEvB,MAAM,SAAS,CAAC,SAAS;IACvB,IAAI,mBAAmB,QAAQ;QAC7B,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU,OAAO;IACnB;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,OAAO;IACT;IAEA,UAAU,WAAW,CAAC;IAEtB,IAAI,QAAQ;IACZ,IAAI,CAAC,QAAQ,GAAG,EAAE;QAChB,QAAQ,QAAQ,KAAK,CAAC,QAAQ,iBAAiB,GAAG,EAAE,CAAC,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC;IACnF,OAAO;QACL,2DAA2D;QAC3D,qDAAqD;QACrD,0DAA0D;QAC1D,0FAA0F;QAC1F,EAAE;QACF,oDAAoD;QACpD,+DAA+D;QAC/D,iEAAiE;QACjE,qEAAqE;QACrE,MAAM,iBAAiB,QAAQ,iBAAiB,GAAG,EAAE,CAAC,EAAE,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,SAAS,CAAC;QACxF,IAAI;QACJ,MAAO,CAAC,OAAO,eAAe,IAAI,CAAC,QAAQ,KACvC,CAAC,CAAC,SAAS,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM,KAAK,QAAQ,MAAM,EAC7D;YACA,IAAI,CAAC,SACC,KAAK,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,MAAM,KAAK,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE;gBACnE,QAAQ;YACV;YACA,eAAe,SAAS,GAAG,KAAK,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,MAAM;QACzE;QACA,4BAA4B;QAC5B,eAAe,SAAS,GAAG,CAAC;IAC9B;IAEA,IAAI,UAAU,MAAM;QAClB,OAAO;IACT;IAEA,MAAM,QAAQ,KAAK,CAAC,EAAE;IACtB,MAAM,QAAQ,KAAK,CAAC,EAAE,IAAI;IAC1B,MAAM,QAAQ,KAAK,CAAC,EAAE,IAAI;IAC1B,MAAM,aAAa,QAAQ,iBAAiB,IAAI,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,GAAG;IAC5E,MAAM,QAAQ,QAAQ,iBAAiB,IAAI,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,GAAG;IAEvE,OAAO,MAAM,GAAG,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,QAAQ,aAAa,OAAO,EAAE;AAClE;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/internal/lrucache.js"], "sourcesContent": ["'use strict'\n\nclass LRUCache {\n  constructor () {\n    this.max = 1000\n    this.map = new Map()\n  }\n\n  get (key) {\n    const value = this.map.get(key)\n    if (value === undefined) {\n      return undefined\n    } else {\n      // Remove the key from the map and add it to the end\n      this.map.delete(key)\n      this.map.set(key, value)\n      return value\n    }\n  }\n\n  delete (key) {\n    return this.map.delete(key)\n  }\n\n  set (key, value) {\n    const deleted = this.delete(key)\n\n    if (!deleted && value !== undefined) {\n      // If cache is full, delete the least recently used item\n      if (this.map.size >= this.max) {\n        const firstKey = this.map.keys().next().value\n        this.delete(firstKey)\n      }\n\n      this.map.set(key, value)\n    }\n\n    return this\n  }\n}\n\nmodule.exports = LRUCache\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;IACJ,aAAe;QACb,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG,IAAI;IACjB;IAEA,IAAK,GAAG,EAAE;QACR,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QAC3B,IAAI,UAAU,WAAW;YACvB,OAAO;QACT,OAAO;YACL,oDAAoD;YACpD,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;YAChB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK;YAClB,OAAO;QACT;IACF;IAEA,OAAQ,GAAG,EAAE;QACX,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;IACzB;IAEA,IAAK,GAAG,EAAE,KAAK,EAAE;QACf,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC;QAE5B,IAAI,CAAC,WAAW,UAAU,WAAW;YACnC,wDAAwD;YACxD,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE;gBAC7B,MAAM,WAAW,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK;gBAC7C,IAAI,CAAC,MAAM,CAAC;YACd;YAEA,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK;QACpB;QAEA,OAAO,IAAI;IACb;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2574, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/classes/range.js"], "sourcesContent": ["'use strict'\n\nconst SPACE_CHARACTERS = /\\s+/g\n\n// hoisted class for cyclic dependency\nclass Range {\n  constructor (range, options) {\n    options = parseOptions(options)\n\n    if (range instanceof Range) {\n      if (\n        range.loose === !!options.loose &&\n        range.includePrerelease === !!options.includePrerelease\n      ) {\n        return range\n      } else {\n        return new Range(range.raw, options)\n      }\n    }\n\n    if (range instanceof Comparator) {\n      // just put it in the set and return\n      this.raw = range.value\n      this.set = [[range]]\n      this.formatted = undefined\n      return this\n    }\n\n    this.options = options\n    this.loose = !!options.loose\n    this.includePrerelease = !!options.includePrerelease\n\n    // First reduce all whitespace as much as possible so we do not have to rely\n    // on potentially slow regexes like \\s*. This is then stored and used for\n    // future error messages as well.\n    this.raw = range.trim().replace(SPACE_CHARACTERS, ' ')\n\n    // First, split on ||\n    this.set = this.raw\n      .split('||')\n      // map the range to a 2d array of comparators\n      .map(r => this.parseRange(r.trim()))\n      // throw out any comparator lists that are empty\n      // this generally means that it was not a valid range, which is allowed\n      // in loose mode, but will still throw if the WHOLE range is invalid.\n      .filter(c => c.length)\n\n    if (!this.set.length) {\n      throw new TypeError(`Invalid SemVer Range: ${this.raw}`)\n    }\n\n    // if we have any that are not the null set, throw out null sets.\n    if (this.set.length > 1) {\n      // keep the first one, in case they're all null sets\n      const first = this.set[0]\n      this.set = this.set.filter(c => !isNullSet(c[0]))\n      if (this.set.length === 0) {\n        this.set = [first]\n      } else if (this.set.length > 1) {\n        // if we have any that are *, then the range is just *\n        for (const c of this.set) {\n          if (c.length === 1 && isAny(c[0])) {\n            this.set = [c]\n            break\n          }\n        }\n      }\n    }\n\n    this.formatted = undefined\n  }\n\n  get range () {\n    if (this.formatted === undefined) {\n      this.formatted = ''\n      for (let i = 0; i < this.set.length; i++) {\n        if (i > 0) {\n          this.formatted += '||'\n        }\n        const comps = this.set[i]\n        for (let k = 0; k < comps.length; k++) {\n          if (k > 0) {\n            this.formatted += ' '\n          }\n          this.formatted += comps[k].toString().trim()\n        }\n      }\n    }\n    return this.formatted\n  }\n\n  format () {\n    return this.range\n  }\n\n  toString () {\n    return this.range\n  }\n\n  parseRange (range) {\n    // memoize range parsing for performance.\n    // this is a very hot path, and fully deterministic.\n    const memoOpts =\n      (this.options.includePrerelease && FLAG_INCLUDE_PRERELEASE) |\n      (this.options.loose && FLAG_LOOSE)\n    const memoKey = memoOpts + ':' + range\n    const cached = cache.get(memoKey)\n    if (cached) {\n      return cached\n    }\n\n    const loose = this.options.loose\n    // `1.2.3 - 1.2.4` => `>=1.2.3 <=1.2.4`\n    const hr = loose ? re[t.HYPHENRANGELOOSE] : re[t.HYPHENRANGE]\n    range = range.replace(hr, hyphenReplace(this.options.includePrerelease))\n    debug('hyphen replace', range)\n\n    // `> 1.2.3 < 1.2.5` => `>1.2.3 <1.2.5`\n    range = range.replace(re[t.COMPARATORTRIM], comparatorTrimReplace)\n    debug('comparator trim', range)\n\n    // `~ 1.2.3` => `~1.2.3`\n    range = range.replace(re[t.TILDETRIM], tildeTrimReplace)\n    debug('tilde trim', range)\n\n    // `^ 1.2.3` => `^1.2.3`\n    range = range.replace(re[t.CARETTRIM], caretTrimReplace)\n    debug('caret trim', range)\n\n    // At this point, the range is completely trimmed and\n    // ready to be split into comparators.\n\n    let rangeList = range\n      .split(' ')\n      .map(comp => parseComparator(comp, this.options))\n      .join(' ')\n      .split(/\\s+/)\n      // >=0.0.0 is equivalent to *\n      .map(comp => replaceGTE0(comp, this.options))\n\n    if (loose) {\n      // in loose mode, throw out any that are not valid comparators\n      rangeList = rangeList.filter(comp => {\n        debug('loose invalid filter', comp, this.options)\n        return !!comp.match(re[t.COMPARATORLOOSE])\n      })\n    }\n    debug('range list', rangeList)\n\n    // if any comparators are the null set, then replace with JUST null set\n    // if more than one comparator, remove any * comparators\n    // also, don't include the same comparator more than once\n    const rangeMap = new Map()\n    const comparators = rangeList.map(comp => new Comparator(comp, this.options))\n    for (const comp of comparators) {\n      if (isNullSet(comp)) {\n        return [comp]\n      }\n      rangeMap.set(comp.value, comp)\n    }\n    if (rangeMap.size > 1 && rangeMap.has('')) {\n      rangeMap.delete('')\n    }\n\n    const result = [...rangeMap.values()]\n    cache.set(memoKey, result)\n    return result\n  }\n\n  intersects (range, options) {\n    if (!(range instanceof Range)) {\n      throw new TypeError('a Range is required')\n    }\n\n    return this.set.some((thisComparators) => {\n      return (\n        isSatisfiable(thisComparators, options) &&\n        range.set.some((rangeComparators) => {\n          return (\n            isSatisfiable(rangeComparators, options) &&\n            thisComparators.every((thisComparator) => {\n              return rangeComparators.every((rangeComparator) => {\n                return thisComparator.intersects(rangeComparator, options)\n              })\n            })\n          )\n        })\n      )\n    })\n  }\n\n  // if ANY of the sets match ALL of its comparators, then pass\n  test (version) {\n    if (!version) {\n      return false\n    }\n\n    if (typeof version === 'string') {\n      try {\n        version = new SemVer(version, this.options)\n      } catch (er) {\n        return false\n      }\n    }\n\n    for (let i = 0; i < this.set.length; i++) {\n      if (testSet(this.set[i], version, this.options)) {\n        return true\n      }\n    }\n    return false\n  }\n}\n\nmodule.exports = Range\n\nconst LRU = require('../internal/lrucache')\nconst cache = new LRU()\n\nconst parseOptions = require('../internal/parse-options')\nconst Comparator = require('./comparator')\nconst debug = require('../internal/debug')\nconst SemVer = require('./semver')\nconst {\n  safeRe: re,\n  t,\n  comparatorTrimReplace,\n  tildeTrimReplace,\n  caretTrimReplace,\n} = require('../internal/re')\nconst { FLAG_INCLUDE_PRERELEASE, FLAG_LOOSE } = require('../internal/constants')\n\nconst isNullSet = c => c.value === '<0.0.0-0'\nconst isAny = c => c.value === ''\n\n// take a set of comparators and determine whether there\n// exists a version which can satisfy it\nconst isSatisfiable = (comparators, options) => {\n  let result = true\n  const remainingComparators = comparators.slice()\n  let testComparator = remainingComparators.pop()\n\n  while (result && remainingComparators.length) {\n    result = remainingComparators.every((otherComparator) => {\n      return testComparator.intersects(otherComparator, options)\n    })\n\n    testComparator = remainingComparators.pop()\n  }\n\n  return result\n}\n\n// comprised of xranges, tildes, stars, and gtlt's at this point.\n// already replaced the hyphen ranges\n// turn into a set of JUST comparators.\nconst parseComparator = (comp, options) => {\n  debug('comp', comp, options)\n  comp = replaceCarets(comp, options)\n  debug('caret', comp)\n  comp = replaceTildes(comp, options)\n  debug('tildes', comp)\n  comp = replaceXRanges(comp, options)\n  debug('xrange', comp)\n  comp = replaceStars(comp, options)\n  debug('stars', comp)\n  return comp\n}\n\nconst isX = id => !id || id.toLowerCase() === 'x' || id === '*'\n\n// ~, ~> --> * (any, kinda silly)\n// ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0-0\n// ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0-0\n// ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0-0\n// ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0-0\n// ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0-0\n// ~0.0.1 --> >=0.0.1 <0.1.0-0\nconst replaceTildes = (comp, options) => {\n  return comp\n    .trim()\n    .split(/\\s+/)\n    .map((c) => replaceTilde(c, options))\n    .join(' ')\n}\n\nconst replaceTilde = (comp, options) => {\n  const r = options.loose ? re[t.TILDELOOSE] : re[t.TILDE]\n  return comp.replace(r, (_, M, m, p, pr) => {\n    debug('tilde', comp, _, M, m, p, pr)\n    let ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = `>=${M}.0.0 <${+M + 1}.0.0-0`\n    } else if (isX(p)) {\n      // ~1.2 == >=1.2.0 <1.3.0-0\n      ret = `>=${M}.${m}.0 <${M}.${+m + 1}.0-0`\n    } else if (pr) {\n      debug('replaceTilde pr', pr)\n      ret = `>=${M}.${m}.${p}-${pr\n      } <${M}.${+m + 1}.0-0`\n    } else {\n      // ~1.2.3 == >=1.2.3 <1.3.0-0\n      ret = `>=${M}.${m}.${p\n      } <${M}.${+m + 1}.0-0`\n    }\n\n    debug('tilde return', ret)\n    return ret\n  })\n}\n\n// ^ --> * (any, kinda silly)\n// ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0-0\n// ^2.0, ^2.0.x --> >=2.0.0 <3.0.0-0\n// ^1.2, ^1.2.x --> >=1.2.0 <2.0.0-0\n// ^1.2.3 --> >=1.2.3 <2.0.0-0\n// ^1.2.0 --> >=1.2.0 <2.0.0-0\n// ^0.0.1 --> >=0.0.1 <0.0.2-0\n// ^0.1.0 --> >=0.1.0 <0.2.0-0\nconst replaceCarets = (comp, options) => {\n  return comp\n    .trim()\n    .split(/\\s+/)\n    .map((c) => replaceCaret(c, options))\n    .join(' ')\n}\n\nconst replaceCaret = (comp, options) => {\n  debug('caret', comp, options)\n  const r = options.loose ? re[t.CARETLOOSE] : re[t.CARET]\n  const z = options.includePrerelease ? '-0' : ''\n  return comp.replace(r, (_, M, m, p, pr) => {\n    debug('caret', comp, _, M, m, p, pr)\n    let ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = `>=${M}.0.0${z} <${+M + 1}.0.0-0`\n    } else if (isX(p)) {\n      if (M === '0') {\n        ret = `>=${M}.${m}.0${z} <${M}.${+m + 1}.0-0`\n      } else {\n        ret = `>=${M}.${m}.0${z} <${+M + 1}.0.0-0`\n      }\n    } else if (pr) {\n      debug('replaceCaret pr', pr)\n      if (M === '0') {\n        if (m === '0') {\n          ret = `>=${M}.${m}.${p}-${pr\n          } <${M}.${m}.${+p + 1}-0`\n        } else {\n          ret = `>=${M}.${m}.${p}-${pr\n          } <${M}.${+m + 1}.0-0`\n        }\n      } else {\n        ret = `>=${M}.${m}.${p}-${pr\n        } <${+M + 1}.0.0-0`\n      }\n    } else {\n      debug('no pr')\n      if (M === '0') {\n        if (m === '0') {\n          ret = `>=${M}.${m}.${p\n          }${z} <${M}.${m}.${+p + 1}-0`\n        } else {\n          ret = `>=${M}.${m}.${p\n          }${z} <${M}.${+m + 1}.0-0`\n        }\n      } else {\n        ret = `>=${M}.${m}.${p\n        } <${+M + 1}.0.0-0`\n      }\n    }\n\n    debug('caret return', ret)\n    return ret\n  })\n}\n\nconst replaceXRanges = (comp, options) => {\n  debug('replaceXRanges', comp, options)\n  return comp\n    .split(/\\s+/)\n    .map((c) => replaceXRange(c, options))\n    .join(' ')\n}\n\nconst replaceXRange = (comp, options) => {\n  comp = comp.trim()\n  const r = options.loose ? re[t.XRANGELOOSE] : re[t.XRANGE]\n  return comp.replace(r, (ret, gtlt, M, m, p, pr) => {\n    debug('xRange', comp, ret, gtlt, M, m, p, pr)\n    const xM = isX(M)\n    const xm = xM || isX(m)\n    const xp = xm || isX(p)\n    const anyX = xp\n\n    if (gtlt === '=' && anyX) {\n      gtlt = ''\n    }\n\n    // if we're including prereleases in the match, then we need\n    // to fix this to -0, the lowest possible prerelease value\n    pr = options.includePrerelease ? '-0' : ''\n\n    if (xM) {\n      if (gtlt === '>' || gtlt === '<') {\n        // nothing is allowed\n        ret = '<0.0.0-0'\n      } else {\n        // nothing is forbidden\n        ret = '*'\n      }\n    } else if (gtlt && anyX) {\n      // we know patch is an x, because we have any x at all.\n      // replace X with 0\n      if (xm) {\n        m = 0\n      }\n      p = 0\n\n      if (gtlt === '>') {\n        // >1 => >=2.0.0\n        // >1.2 => >=1.3.0\n        gtlt = '>='\n        if (xm) {\n          M = +M + 1\n          m = 0\n          p = 0\n        } else {\n          m = +m + 1\n          p = 0\n        }\n      } else if (gtlt === '<=') {\n        // <=0.7.x is actually <0.8.0, since any 0.7.x should\n        // pass.  Similarly, <=7.x is actually <8.0.0, etc.\n        gtlt = '<'\n        if (xm) {\n          M = +M + 1\n        } else {\n          m = +m + 1\n        }\n      }\n\n      if (gtlt === '<') {\n        pr = '-0'\n      }\n\n      ret = `${gtlt + M}.${m}.${p}${pr}`\n    } else if (xm) {\n      ret = `>=${M}.0.0${pr} <${+M + 1}.0.0-0`\n    } else if (xp) {\n      ret = `>=${M}.${m}.0${pr\n      } <${M}.${+m + 1}.0-0`\n    }\n\n    debug('xRange return', ret)\n\n    return ret\n  })\n}\n\n// Because * is AND-ed with everything else in the comparator,\n// and '' means \"any version\", just remove the *s entirely.\nconst replaceStars = (comp, options) => {\n  debug('replaceStars', comp, options)\n  // Looseness is ignored here.  star is always as loose as it gets!\n  return comp\n    .trim()\n    .replace(re[t.STAR], '')\n}\n\nconst replaceGTE0 = (comp, options) => {\n  debug('replaceGTE0', comp, options)\n  return comp\n    .trim()\n    .replace(re[options.includePrerelease ? t.GTE0PRE : t.GTE0], '')\n}\n\n// This function is passed to string.replace(re[t.HYPHENRANGE])\n// M, m, patch, prerelease, build\n// 1.2 - 3.4.5 => >=1.2.0 <=3.4.5\n// 1.2.3 - 3.4 => >=1.2.0 <3.5.0-0 Any 3.4.x will do\n// 1.2 - 3.4 => >=1.2.0 <3.5.0-0\n// TODO build?\nconst hyphenReplace = incPr => ($0,\n  from, fM, fm, fp, fpr, fb,\n  to, tM, tm, tp, tpr) => {\n  if (isX(fM)) {\n    from = ''\n  } else if (isX(fm)) {\n    from = `>=${fM}.0.0${incPr ? '-0' : ''}`\n  } else if (isX(fp)) {\n    from = `>=${fM}.${fm}.0${incPr ? '-0' : ''}`\n  } else if (fpr) {\n    from = `>=${from}`\n  } else {\n    from = `>=${from}${incPr ? '-0' : ''}`\n  }\n\n  if (isX(tM)) {\n    to = ''\n  } else if (isX(tm)) {\n    to = `<${+tM + 1}.0.0-0`\n  } else if (isX(tp)) {\n    to = `<${tM}.${+tm + 1}.0-0`\n  } else if (tpr) {\n    to = `<=${tM}.${tm}.${tp}-${tpr}`\n  } else if (incPr) {\n    to = `<${tM}.${tm}.${+tp + 1}-0`\n  } else {\n    to = `<=${to}`\n  }\n\n  return `${from} ${to}`.trim()\n}\n\nconst testSet = (set, version, options) => {\n  for (let i = 0; i < set.length; i++) {\n    if (!set[i].test(version)) {\n      return false\n    }\n  }\n\n  if (version.prerelease.length && !options.includePrerelease) {\n    // Find the set of versions that are allowed to have prereleases\n    // For example, ^1.2.3-pr.1 desugars to >=1.2.3-pr.1 <2.0.0\n    // That should allow `1.2.3-pr.2` to pass.\n    // However, `1.2.4-alpha.notready` should NOT be allowed,\n    // even though it's within the range set by the comparators.\n    for (let i = 0; i < set.length; i++) {\n      debug(set[i].semver)\n      if (set[i].semver === Comparator.ANY) {\n        continue\n      }\n\n      if (set[i].semver.prerelease.length > 0) {\n        const allowed = set[i].semver\n        if (allowed.major === version.major &&\n            allowed.minor === version.minor &&\n            allowed.patch === version.patch) {\n          return true\n        }\n      }\n    }\n\n    // Version has a -pre, but it's not one of the ones we like.\n    return false\n  }\n\n  return true\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,mBAAmB;AAEzB,sCAAsC;AACtC,MAAM;IACJ,YAAa,KAAK,EAAE,OAAO,CAAE;QAC3B,UAAU,aAAa;QAEvB,IAAI,iBAAiB,OAAO;YAC1B,IACE,MAAM,KAAK,KAAK,CAAC,CAAC,QAAQ,KAAK,IAC/B,MAAM,iBAAiB,KAAK,CAAC,CAAC,QAAQ,iBAAiB,EACvD;gBACA,OAAO;YACT,OAAO;gBACL,OAAO,IAAI,MAAM,MAAM,GAAG,EAAE;YAC9B;QACF;QAEA,IAAI,iBAAiB,YAAY;YAC/B,oCAAoC;YACpC,IAAI,CAAC,GAAG,GAAG,MAAM,KAAK;YACtB,IAAI,CAAC,GAAG,GAAG;gBAAC;oBAAC;iBAAM;aAAC;YACpB,IAAI,CAAC,SAAS,GAAG;YACjB,OAAO,IAAI;QACb;QAEA,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,QAAQ,KAAK;QAC5B,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,QAAQ,iBAAiB;QAEpD,4EAA4E;QAC5E,yEAAyE;QACzE,iCAAiC;QACjC,IAAI,CAAC,GAAG,GAAG,MAAM,IAAI,GAAG,OAAO,CAAC,kBAAkB;QAElD,qBAAqB;QACrB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAChB,KAAK,CAAC,KACP,6CAA6C;SAC5C,GAAG,CAAC,CAAA,IAAK,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,IAChC,gDAAgD;QAChD,uEAAuE;QACvE,qEAAqE;SACpE,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM;QAEvB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;YACpB,MAAM,IAAI,UAAU,CAAC,sBAAsB,EAAE,IAAI,CAAC,GAAG,EAAE;QACzD;QAEA,iEAAiE;QACjE,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,GAAG;YACvB,oDAAoD;YACpD,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC,EAAE;YACzB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,UAAU,CAAC,CAAC,EAAE;YAC/C,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,GAAG;gBACzB,IAAI,CAAC,GAAG,GAAG;oBAAC;iBAAM;YACpB,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,GAAG;gBAC9B,sDAAsD;gBACtD,KAAK,MAAM,KAAK,IAAI,CAAC,GAAG,CAAE;oBACxB,IAAI,EAAE,MAAM,KAAK,KAAK,MAAM,CAAC,CAAC,EAAE,GAAG;wBACjC,IAAI,CAAC,GAAG,GAAG;4BAAC;yBAAE;wBACd;oBACF;gBACF;YACF;QACF;QAEA,IAAI,CAAC,SAAS,GAAG;IACnB;IAEA,IAAI,QAAS;QACX,IAAI,IAAI,CAAC,SAAS,KAAK,WAAW;YAChC,IAAI,CAAC,SAAS,GAAG;YACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,IAAK;gBACxC,IAAI,IAAI,GAAG;oBACT,IAAI,CAAC,SAAS,IAAI;gBACpB;gBACA,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC,EAAE;gBACzB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACrC,IAAI,IAAI,GAAG;wBACT,IAAI,CAAC,SAAS,IAAI;oBACpB;oBACA,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ,GAAG,IAAI;gBAC5C;YACF;QACF;QACA,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA,SAAU;QACR,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,WAAY;QACV,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,WAAY,KAAK,EAAE;QACjB,yCAAyC;QACzC,oDAAoD;QACpD,MAAM,WACJ,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,uBAAuB,IAC1D,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,UAAU;QACnC,MAAM,UAAU,WAAW,MAAM;QACjC,MAAM,SAAS,MAAM,GAAG,CAAC;QACzB,IAAI,QAAQ;YACV,OAAO;QACT;QAEA,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK;QAChC,uCAAuC;QACvC,MAAM,KAAK,QAAQ,EAAE,CAAC,EAAE,gBAAgB,CAAC,GAAG,EAAE,CAAC,EAAE,WAAW,CAAC;QAC7D,QAAQ,MAAM,OAAO,CAAC,IAAI,cAAc,IAAI,CAAC,OAAO,CAAC,iBAAiB;QACtE,MAAM,kBAAkB;QAExB,uCAAuC;QACvC,QAAQ,MAAM,OAAO,CAAC,EAAE,CAAC,EAAE,cAAc,CAAC,EAAE;QAC5C,MAAM,mBAAmB;QAEzB,wBAAwB;QACxB,QAAQ,MAAM,OAAO,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,EAAE;QACvC,MAAM,cAAc;QAEpB,wBAAwB;QACxB,QAAQ,MAAM,OAAO,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,EAAE;QACvC,MAAM,cAAc;QAEpB,qDAAqD;QACrD,sCAAsC;QAEtC,IAAI,YAAY,MACb,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,gBAAgB,MAAM,IAAI,CAAC,OAAO,GAC9C,IAAI,CAAC,KACL,KAAK,CAAC,MACP,6BAA6B;SAC5B,GAAG,CAAC,CAAA,OAAQ,YAAY,MAAM,IAAI,CAAC,OAAO;QAE7C,IAAI,OAAO;YACT,8DAA8D;YAC9D,YAAY,UAAU,MAAM,CAAC,CAAA;gBAC3B,MAAM,wBAAwB,MAAM,IAAI,CAAC,OAAO;gBAChD,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,eAAe,CAAC;YAC3C;QACF;QACA,MAAM,cAAc;QAEpB,uEAAuE;QACvE,wDAAwD;QACxD,yDAAyD;QACzD,MAAM,WAAW,IAAI;QACrB,MAAM,cAAc,UAAU,GAAG,CAAC,CAAA,OAAQ,IAAI,WAAW,MAAM,IAAI,CAAC,OAAO;QAC3E,KAAK,MAAM,QAAQ,YAAa;YAC9B,IAAI,UAAU,OAAO;gBACnB,OAAO;oBAAC;iBAAK;YACf;YACA,SAAS,GAAG,CAAC,KAAK,KAAK,EAAE;QAC3B;QACA,IAAI,SAAS,IAAI,GAAG,KAAK,SAAS,GAAG,CAAC,KAAK;YACzC,SAAS,MAAM,CAAC;QAClB;QAEA,MAAM,SAAS;eAAI,SAAS,MAAM;SAAG;QACrC,MAAM,GAAG,CAAC,SAAS;QACnB,OAAO;IACT;IAEA,WAAY,KAAK,EAAE,OAAO,EAAE;QAC1B,IAAI,CAAC,CAAC,iBAAiB,KAAK,GAAG;YAC7B,MAAM,IAAI,UAAU;QACtB;QAEA,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACpB,OACE,cAAc,iBAAiB,YAC/B,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC;gBACd,OACE,cAAc,kBAAkB,YAChC,gBAAgB,KAAK,CAAC,CAAC;oBACrB,OAAO,iBAAiB,KAAK,CAAC,CAAC;wBAC7B,OAAO,eAAe,UAAU,CAAC,iBAAiB;oBACpD;gBACF;YAEJ;QAEJ;IACF;IAEA,6DAA6D;IAC7D,KAAM,OAAO,EAAE;QACb,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,IAAI,OAAO,YAAY,UAAU;YAC/B,IAAI;gBACF,UAAU,IAAI,OAAO,SAAS,IAAI,CAAC,OAAO;YAC5C,EAAE,OAAO,IAAI;gBACX,OAAO;YACT;QACF;QAEA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,IAAK;YACxC,IAAI,QAAQ,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC,OAAO,GAAG;gBAC/C,OAAO;YACT;QACF;QACA,OAAO;IACT;AACF;AAEA,OAAO,OAAO,GAAG;AAEjB,MAAM;AACN,MAAM,QAAQ,IAAI;AAElB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,EACJ,QAAQ,EAAE,EACV,CAAC,EACD,qBAAqB,EACrB,gBAAgB,EAChB,gBAAgB,EACjB;AACD,MAAM,EAAE,uBAAuB,EAAE,UAAU,EAAE;AAE7C,MAAM,YAAY,CAAA,IAAK,EAAE,KAAK,KAAK;AACnC,MAAM,QAAQ,CAAA,IAAK,EAAE,KAAK,KAAK;AAE/B,wDAAwD;AACxD,wCAAwC;AACxC,MAAM,gBAAgB,CAAC,aAAa;IAClC,IAAI,SAAS;IACb,MAAM,uBAAuB,YAAY,KAAK;IAC9C,IAAI,iBAAiB,qBAAqB,GAAG;IAE7C,MAAO,UAAU,qBAAqB,MAAM,CAAE;QAC5C,SAAS,qBAAqB,KAAK,CAAC,CAAC;YACnC,OAAO,eAAe,UAAU,CAAC,iBAAiB;QACpD;QAEA,iBAAiB,qBAAqB,GAAG;IAC3C;IAEA,OAAO;AACT;AAEA,iEAAiE;AACjE,qCAAqC;AACrC,uCAAuC;AACvC,MAAM,kBAAkB,CAAC,MAAM;IAC7B,MAAM,QAAQ,MAAM;IACpB,OAAO,cAAc,MAAM;IAC3B,MAAM,SAAS;IACf,OAAO,cAAc,MAAM;IAC3B,MAAM,UAAU;IAChB,OAAO,eAAe,MAAM;IAC5B,MAAM,UAAU;IAChB,OAAO,aAAa,MAAM;IAC1B,MAAM,SAAS;IACf,OAAO;AACT;AAEA,MAAM,MAAM,CAAA,KAAM,CAAC,MAAM,GAAG,WAAW,OAAO,OAAO,OAAO;AAE5D,iCAAiC;AACjC,4DAA4D;AAC5D,oDAAoD;AACpD,oDAAoD;AACpD,uCAAuC;AACvC,uCAAuC;AACvC,8BAA8B;AAC9B,MAAM,gBAAgB,CAAC,MAAM;IAC3B,OAAO,KACJ,IAAI,GACJ,KAAK,CAAC,OACN,GAAG,CAAC,CAAC,IAAM,aAAa,GAAG,UAC3B,IAAI,CAAC;AACV;AAEA,MAAM,eAAe,CAAC,MAAM;IAC1B,MAAM,IAAI,QAAQ,KAAK,GAAG,EAAE,CAAC,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC;IACxD,OAAO,KAAK,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;QAClC,MAAM,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG;QACjC,IAAI;QAEJ,IAAI,IAAI,IAAI;YACV,MAAM;QACR,OAAO,IAAI,IAAI,IAAI;YACjB,MAAM,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;QACrC,OAAO,IAAI,IAAI,IAAI;YACjB,2BAA2B;YAC3B,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;QAC3C,OAAO,IAAI,IAAI;YACb,MAAM,mBAAmB;YACzB,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GACzB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;QACxB,OAAO;YACL,6BAA6B;YAC7B,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EACpB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;QACxB;QAEA,MAAM,gBAAgB;QACtB,OAAO;IACT;AACF;AAEA,6BAA6B;AAC7B,wCAAwC;AACxC,oCAAoC;AACpC,oCAAoC;AACpC,8BAA8B;AAC9B,8BAA8B;AAC9B,8BAA8B;AAC9B,8BAA8B;AAC9B,MAAM,gBAAgB,CAAC,MAAM;IAC3B,OAAO,KACJ,IAAI,GACJ,KAAK,CAAC,OACN,GAAG,CAAC,CAAC,IAAM,aAAa,GAAG,UAC3B,IAAI,CAAC;AACV;AAEA,MAAM,eAAe,CAAC,MAAM;IAC1B,MAAM,SAAS,MAAM;IACrB,MAAM,IAAI,QAAQ,KAAK,GAAG,EAAE,CAAC,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC;IACxD,MAAM,IAAI,QAAQ,iBAAiB,GAAG,OAAO;IAC7C,OAAO,KAAK,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;QAClC,MAAM,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG;QACjC,IAAI;QAEJ,IAAI,IAAI,IAAI;YACV,MAAM;QACR,OAAO,IAAI,IAAI,IAAI;YACjB,MAAM,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;QACzC,OAAO,IAAI,IAAI,IAAI;YACjB,IAAI,MAAM,KAAK;gBACb,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;YAC/C,OAAO;gBACL,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;YAC5C;QACF,OAAO,IAAI,IAAI;YACb,MAAM,mBAAmB;YACzB,IAAI,MAAM,KAAK;gBACb,IAAI,MAAM,KAAK;oBACb,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GACzB,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;gBAC3B,OAAO;oBACL,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GACzB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;gBACxB;YACF,OAAO;gBACL,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GACzB,EAAE,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;YACrB;QACF,OAAO;YACL,MAAM;YACN,IAAI,MAAM,KAAK;gBACb,IAAI,MAAM,KAAK;oBACb,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,IAClB,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;gBAC/B,OAAO;oBACL,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,IAClB,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;gBAC5B;YACF,OAAO;gBACL,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EACpB,EAAE,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;YACrB;QACF;QAEA,MAAM,gBAAgB;QACtB,OAAO;IACT;AACF;AAEA,MAAM,iBAAiB,CAAC,MAAM;IAC5B,MAAM,kBAAkB,MAAM;IAC9B,OAAO,KACJ,KAAK,CAAC,OACN,GAAG,CAAC,CAAC,IAAM,cAAc,GAAG,UAC5B,IAAI,CAAC;AACV;AAEA,MAAM,gBAAgB,CAAC,MAAM;IAC3B,OAAO,KAAK,IAAI;IAChB,MAAM,IAAI,QAAQ,KAAK,GAAG,EAAE,CAAC,EAAE,WAAW,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC;IAC1D,OAAO,KAAK,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,GAAG,GAAG,GAAG;QAC1C,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,GAAG,GAAG;QAC1C,MAAM,KAAK,IAAI;QACf,MAAM,KAAK,MAAM,IAAI;QACrB,MAAM,KAAK,MAAM,IAAI;QACrB,MAAM,OAAO;QAEb,IAAI,SAAS,OAAO,MAAM;YACxB,OAAO;QACT;QAEA,4DAA4D;QAC5D,0DAA0D;QAC1D,KAAK,QAAQ,iBAAiB,GAAG,OAAO;QAExC,IAAI,IAAI;YACN,IAAI,SAAS,OAAO,SAAS,KAAK;gBAChC,qBAAqB;gBACrB,MAAM;YACR,OAAO;gBACL,uBAAuB;gBACvB,MAAM;YACR;QACF,OAAO,IAAI,QAAQ,MAAM;YACvB,uDAAuD;YACvD,mBAAmB;YACnB,IAAI,IAAI;gBACN,IAAI;YACN;YACA,IAAI;YAEJ,IAAI,SAAS,KAAK;gBAChB,gBAAgB;gBAChB,kBAAkB;gBAClB,OAAO;gBACP,IAAI,IAAI;oBACN,IAAI,CAAC,IAAI;oBACT,IAAI;oBACJ,IAAI;gBACN,OAAO;oBACL,IAAI,CAAC,IAAI;oBACT,IAAI;gBACN;YACF,OAAO,IAAI,SAAS,MAAM;gBACxB,qDAAqD;gBACrD,mDAAmD;gBACnD,OAAO;gBACP,IAAI,IAAI;oBACN,IAAI,CAAC,IAAI;gBACX,OAAO;oBACL,IAAI,CAAC,IAAI;gBACX;YACF;YAEA,IAAI,SAAS,KAAK;gBAChB,KAAK;YACP;YAEA,MAAM,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,IAAI;QACpC,OAAO,IAAI,IAAI;YACb,MAAM,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;QAC1C,OAAO,IAAI,IAAI;YACb,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GACrB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;QACxB;QAEA,MAAM,iBAAiB;QAEvB,OAAO;IACT;AACF;AAEA,8DAA8D;AAC9D,2DAA2D;AAC3D,MAAM,eAAe,CAAC,MAAM;IAC1B,MAAM,gBAAgB,MAAM;IAC5B,kEAAkE;IAClE,OAAO,KACJ,IAAI,GACJ,OAAO,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE;AACzB;AAEA,MAAM,cAAc,CAAC,MAAM;IACzB,MAAM,eAAe,MAAM;IAC3B,OAAO,KACJ,IAAI,GACJ,OAAO,CAAC,EAAE,CAAC,QAAQ,iBAAiB,GAAG,EAAE,OAAO,GAAG,EAAE,IAAI,CAAC,EAAE;AACjE;AAEA,+DAA+D;AAC/D,iCAAiC;AACjC,iCAAiC;AACjC,oDAAoD;AACpD,gCAAgC;AAChC,cAAc;AACd,MAAM,gBAAgB,CAAA,QAAS,CAAC,IAC9B,MAAM,IAAI,IAAI,IAAI,KAAK,IACvB,IAAI,IAAI,IAAI,IAAI;QAChB,IAAI,IAAI,KAAK;YACX,OAAO;QACT,OAAO,IAAI,IAAI,KAAK;YAClB,OAAO,CAAC,EAAE,EAAE,GAAG,IAAI,EAAE,QAAQ,OAAO,IAAI;QAC1C,OAAO,IAAI,IAAI,KAAK;YAClB,OAAO,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,QAAQ,OAAO,IAAI;QAC9C,OAAO,IAAI,KAAK;YACd,OAAO,CAAC,EAAE,EAAE,MAAM;QACpB,OAAO;YACL,OAAO,CAAC,EAAE,EAAE,OAAO,QAAQ,OAAO,IAAI;QACxC;QAEA,IAAI,IAAI,KAAK;YACX,KAAK;QACP,OAAO,IAAI,IAAI,KAAK;YAClB,KAAK,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;QAC1B,OAAO,IAAI,IAAI,KAAK;YAClB,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;QAC9B,OAAO,IAAI,KAAK;YACd,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK;QACnC,OAAO,IAAI,OAAO;YAChB,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC;QAClC,OAAO;YACL,KAAK,CAAC,EAAE,EAAE,IAAI;QAChB;QAEA,OAAO,GAAG,KAAK,CAAC,EAAE,IAAI,CAAC,IAAI;IAC7B;AAEA,MAAM,UAAU,CAAC,KAAK,SAAS;IAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACnC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU;YACzB,OAAO;QACT;IACF;IAEA,IAAI,QAAQ,UAAU,CAAC,MAAM,IAAI,CAAC,QAAQ,iBAAiB,EAAE;QAC3D,gEAAgE;QAChE,2DAA2D;QAC3D,0CAA0C;QAC1C,yDAAyD;QACzD,4DAA4D;QAC5D,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACnC,MAAM,GAAG,CAAC,EAAE,CAAC,MAAM;YACnB,IAAI,GAAG,CAAC,EAAE,CAAC,MAAM,KAAK,WAAW,GAAG,EAAE;gBACpC;YACF;YAEA,IAAI,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG;gBACvC,MAAM,UAAU,GAAG,CAAC,EAAE,CAAC,MAAM;gBAC7B,IAAI,QAAQ,KAAK,KAAK,QAAQ,KAAK,IAC/B,QAAQ,KAAK,KAAK,QAAQ,KAAK,IAC/B,QAAQ,KAAK,KAAK,QAAQ,KAAK,EAAE;oBACnC,OAAO;gBACT;YACF;QACF;QAEA,4DAA4D;QAC5D,OAAO;IACT;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3028, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/classes/comparator.js"], "sourcesContent": ["'use strict'\n\nconst ANY = Symbol('SemVer ANY')\n// hoisted class for cyclic dependency\nclass Comparator {\n  static get ANY () {\n    return ANY\n  }\n\n  constructor (comp, options) {\n    options = parseOptions(options)\n\n    if (comp instanceof Comparator) {\n      if (comp.loose === !!options.loose) {\n        return comp\n      } else {\n        comp = comp.value\n      }\n    }\n\n    comp = comp.trim().split(/\\s+/).join(' ')\n    debug('comparator', comp, options)\n    this.options = options\n    this.loose = !!options.loose\n    this.parse(comp)\n\n    if (this.semver === ANY) {\n      this.value = ''\n    } else {\n      this.value = this.operator + this.semver.version\n    }\n\n    debug('comp', this)\n  }\n\n  parse (comp) {\n    const r = this.options.loose ? re[t.COMPARATORLOOSE] : re[t.COMPARATOR]\n    const m = comp.match(r)\n\n    if (!m) {\n      throw new TypeError(`Invalid comparator: ${comp}`)\n    }\n\n    this.operator = m[1] !== undefined ? m[1] : ''\n    if (this.operator === '=') {\n      this.operator = ''\n    }\n\n    // if it literally is just '>' or '' then allow anything.\n    if (!m[2]) {\n      this.semver = ANY\n    } else {\n      this.semver = new SemVer(m[2], this.options.loose)\n    }\n  }\n\n  toString () {\n    return this.value\n  }\n\n  test (version) {\n    debug('Comparator.test', version, this.options.loose)\n\n    if (this.semver === ANY || version === ANY) {\n      return true\n    }\n\n    if (typeof version === 'string') {\n      try {\n        version = new SemVer(version, this.options)\n      } catch (er) {\n        return false\n      }\n    }\n\n    return cmp(version, this.operator, this.semver, this.options)\n  }\n\n  intersects (comp, options) {\n    if (!(comp instanceof Comparator)) {\n      throw new TypeError('a Comparator is required')\n    }\n\n    if (this.operator === '') {\n      if (this.value === '') {\n        return true\n      }\n      return new Range(comp.value, options).test(this.value)\n    } else if (comp.operator === '') {\n      if (comp.value === '') {\n        return true\n      }\n      return new Range(this.value, options).test(comp.semver)\n    }\n\n    options = parseOptions(options)\n\n    // Special cases where nothing can possibly be lower\n    if (options.includePrerelease &&\n      (this.value === '<0.0.0-0' || comp.value === '<0.0.0-0')) {\n      return false\n    }\n    if (!options.includePrerelease &&\n      (this.value.startsWith('<0.0.0') || comp.value.startsWith('<0.0.0'))) {\n      return false\n    }\n\n    // Same direction increasing (> or >=)\n    if (this.operator.startsWith('>') && comp.operator.startsWith('>')) {\n      return true\n    }\n    // Same direction decreasing (< or <=)\n    if (this.operator.startsWith('<') && comp.operator.startsWith('<')) {\n      return true\n    }\n    // same SemVer and both sides are inclusive (<= or >=)\n    if (\n      (this.semver.version === comp.semver.version) &&\n      this.operator.includes('=') && comp.operator.includes('=')) {\n      return true\n    }\n    // opposite directions less than\n    if (cmp(this.semver, '<', comp.semver, options) &&\n      this.operator.startsWith('>') && comp.operator.startsWith('<')) {\n      return true\n    }\n    // opposite directions greater than\n    if (cmp(this.semver, '>', comp.semver, options) &&\n      this.operator.startsWith('<') && comp.operator.startsWith('>')) {\n      return true\n    }\n    return false\n  }\n}\n\nmodule.exports = Comparator\n\nconst parseOptions = require('../internal/parse-options')\nconst { safeRe: re, t } = require('../internal/re')\nconst cmp = require('../functions/cmp')\nconst debug = require('../internal/debug')\nconst SemVer = require('./semver')\nconst Range = require('./range')\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,MAAM,OAAO;AACnB,sCAAsC;AACtC,MAAM;IACJ,WAAW,MAAO;QAChB,OAAO;IACT;IAEA,YAAa,IAAI,EAAE,OAAO,CAAE;QAC1B,UAAU,aAAa;QAEvB,IAAI,gBAAgB,YAAY;YAC9B,IAAI,KAAK,KAAK,KAAK,CAAC,CAAC,QAAQ,KAAK,EAAE;gBAClC,OAAO;YACT,OAAO;gBACL,OAAO,KAAK,KAAK;YACnB;QACF;QAEA,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC;QACrC,MAAM,cAAc,MAAM;QAC1B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,QAAQ,KAAK;QAC5B,IAAI,CAAC,KAAK,CAAC;QAEX,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK;YACvB,IAAI,CAAC,KAAK,GAAG;QACf,OAAO;YACL,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO;QAClD;QAEA,MAAM,QAAQ,IAAI;IACpB;IAEA,MAAO,IAAI,EAAE;QACX,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC,EAAE,eAAe,CAAC,GAAG,EAAE,CAAC,EAAE,UAAU,CAAC;QACvE,MAAM,IAAI,KAAK,KAAK,CAAC;QAErB,IAAI,CAAC,GAAG;YACN,MAAM,IAAI,UAAU,CAAC,oBAAoB,EAAE,MAAM;QACnD;QAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,KAAK,YAAY,CAAC,CAAC,EAAE,GAAG;QAC5C,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK;YACzB,IAAI,CAAC,QAAQ,GAAG;QAClB;QAEA,yDAAyD;QACzD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,IAAI,CAAC,MAAM,GAAG;QAChB,OAAO;YACL,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;QACnD;IACF;IAEA,WAAY;QACV,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,KAAM,OAAO,EAAE;QACb,MAAM,mBAAmB,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK;QAEpD,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,YAAY,KAAK;YAC1C,OAAO;QACT;QAEA,IAAI,OAAO,YAAY,UAAU;YAC/B,IAAI;gBACF,UAAU,IAAI,OAAO,SAAS,IAAI,CAAC,OAAO;YAC5C,EAAE,OAAO,IAAI;gBACX,OAAO;YACT;QACF;QAEA,OAAO,IAAI,SAAS,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO;IAC9D;IAEA,WAAY,IAAI,EAAE,OAAO,EAAE;QACzB,IAAI,CAAC,CAAC,gBAAgB,UAAU,GAAG;YACjC,MAAM,IAAI,UAAU;QACtB;QAEA,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI;YACxB,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI;gBACrB,OAAO;YACT;YACA,OAAO,IAAI,MAAM,KAAK,KAAK,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;QACvD,OAAO,IAAI,KAAK,QAAQ,KAAK,IAAI;YAC/B,IAAI,KAAK,KAAK,KAAK,IAAI;gBACrB,OAAO;YACT;YACA,OAAO,IAAI,MAAM,IAAI,CAAC,KAAK,EAAE,SAAS,IAAI,CAAC,KAAK,MAAM;QACxD;QAEA,UAAU,aAAa;QAEvB,oDAAoD;QACpD,IAAI,QAAQ,iBAAiB,IAC3B,CAAC,IAAI,CAAC,KAAK,KAAK,cAAc,KAAK,KAAK,KAAK,UAAU,GAAG;YAC1D,OAAO;QACT;QACA,IAAI,CAAC,QAAQ,iBAAiB,IAC5B,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,aAAa,KAAK,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG;YACtE,OAAO;QACT;QAEA,sCAAsC;QACtC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,KAAK,QAAQ,CAAC,UAAU,CAAC,MAAM;YAClE,OAAO;QACT;QACA,sCAAsC;QACtC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,KAAK,QAAQ,CAAC,UAAU,CAAC,MAAM;YAClE,OAAO;QACT;QACA,sDAAsD;QACtD,IACE,AAAC,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,KAAK,MAAM,CAAC,OAAO,IAC5C,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ,CAAC,MAAM;YAC5D,OAAO;QACT;QACA,gCAAgC;QAChC,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,YACrC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,KAAK,QAAQ,CAAC,UAAU,CAAC,MAAM;YAChE,OAAO;QACT;QACA,mCAAmC;QACnC,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,YACrC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,KAAK,QAAQ,CAAC,UAAU,CAAC,MAAM;YAChE,OAAO;QACT;QACA,OAAO;IACT;AACF;AAEA,OAAO,OAAO,GAAG;AAEjB,MAAM;AACN,MAAM,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE;AACvB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/functions/satisfies.js"], "sourcesContent": ["'use strict'\n\nconst Range = require('../classes/range')\nconst satisfies = (version, range, options) => {\n  try {\n    range = new Range(range, options)\n  } catch (er) {\n    return false\n  }\n  return range.test(version)\n}\nmodule.exports = satisfies\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,YAAY,CAAC,SAAS,OAAO;IACjC,IAAI;QACF,QAAQ,IAAI,MAAM,OAAO;IAC3B,EAAE,OAAO,IAAI;QACX,OAAO;IACT;IACA,OAAO,MAAM,IAAI,CAAC;AACpB;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/ranges/to-comparators.js"], "sourcesContent": ["'use strict'\n\nconst Range = require('../classes/range')\n\n// Mostly just for testing and legacy API reasons\nconst toComparators = (range, options) =>\n  new Range(range, options).set\n    .map(comp => comp.map(c => c.value).join(' ').trim().split(' '))\n\nmodule.exports = toComparators\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AAEN,iDAAiD;AACjD,MAAM,gBAAgB,CAAC,OAAO,UAC5B,IAAI,MAAM,OAAO,SAAS,GAAG,CAC1B,GAAG,CAAC,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC;AAE/D,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/ranges/max-satisfying.js"], "sourcesContent": ["'use strict'\n\nconst SemVer = require('../classes/semver')\nconst Range = require('../classes/range')\n\nconst maxSatisfying = (versions, range, options) => {\n  let max = null\n  let maxSV = null\n  let rangeObj = null\n  try {\n    rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach((v) => {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!max || maxSV.compare(v) === -1) {\n        // compare(max, v, true)\n        max = v\n        maxSV = new SemVer(max, options)\n      }\n    }\n  })\n  return max\n}\nmodule.exports = maxSatisfying\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM;AAEN,MAAM,gBAAgB,CAAC,UAAU,OAAO;IACtC,IAAI,MAAM;IACV,IAAI,QAAQ;IACZ,IAAI,WAAW;IACf,IAAI;QACF,WAAW,IAAI,MAAM,OAAO;IAC9B,EAAE,OAAO,IAAI;QACX,OAAO;IACT;IACA,SAAS,OAAO,CAAC,CAAC;QAChB,IAAI,SAAS,IAAI,CAAC,IAAI;YACpB,+BAA+B;YAC/B,IAAI,CAAC,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,GAAG;gBACnC,wBAAwB;gBACxB,MAAM;gBACN,QAAQ,IAAI,OAAO,KAAK;YAC1B;QACF;IACF;IACA,OAAO;AACT;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/ranges/min-satisfying.js"], "sourcesContent": ["'use strict'\n\nconst SemVer = require('../classes/semver')\nconst Range = require('../classes/range')\nconst minSatisfying = (versions, range, options) => {\n  let min = null\n  let minSV = null\n  let rangeObj = null\n  try {\n    rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach((v) => {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!min || minSV.compare(v) === 1) {\n        // compare(min, v, true)\n        min = v\n        minSV = new SemVer(min, options)\n      }\n    }\n  })\n  return min\n}\nmodule.exports = minSatisfying\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM;AACN,MAAM,gBAAgB,CAAC,UAAU,OAAO;IACtC,IAAI,MAAM;IACV,IAAI,QAAQ;IACZ,IAAI,WAAW;IACf,IAAI;QACF,WAAW,IAAI,MAAM,OAAO;IAC9B,EAAE,OAAO,IAAI;QACX,OAAO;IACT;IACA,SAAS,OAAO,CAAC,CAAC;QAChB,IAAI,SAAS,IAAI,CAAC,IAAI;YACpB,+BAA+B;YAC/B,IAAI,CAAC,OAAO,MAAM,OAAO,CAAC,OAAO,GAAG;gBAClC,wBAAwB;gBACxB,MAAM;gBACN,QAAQ,IAAI,OAAO,KAAK;YAC1B;QACF;IACF;IACA,OAAO;AACT;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/ranges/min-version.js"], "sourcesContent": ["'use strict'\n\nconst SemVer = require('../classes/semver')\nconst Range = require('../classes/range')\nconst gt = require('../functions/gt')\n\nconst minVersion = (range, loose) => {\n  range = new Range(range, loose)\n\n  let minver = new SemVer('0.0.0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = new SemVer('0.0.0-0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = null\n  for (let i = 0; i < range.set.length; ++i) {\n    const comparators = range.set[i]\n\n    let setMin = null\n    comparators.forEach((comparator) => {\n      // Clone to avoid manipulating the comparator's semver object.\n      const compver = new SemVer(comparator.semver.version)\n      switch (comparator.operator) {\n        case '>':\n          if (compver.prerelease.length === 0) {\n            compver.patch++\n          } else {\n            compver.prerelease.push(0)\n          }\n          compver.raw = compver.format()\n          /* fallthrough */\n        case '':\n        case '>=':\n          if (!setMin || gt(compver, setMin)) {\n            setMin = compver\n          }\n          break\n        case '<':\n        case '<=':\n          /* Ignore maximum versions */\n          break\n        /* istanbul ignore next */\n        default:\n          throw new Error(`Unexpected operation: ${comparator.operator}`)\n      }\n    })\n    if (setMin && (!minver || gt(minver, setMin))) {\n      minver = setMin\n    }\n  }\n\n  if (minver && range.test(minver)) {\n    return minver\n  }\n\n  return null\n}\nmodule.exports = minVersion\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM;AACN,MAAM;AAEN,MAAM,aAAa,CAAC,OAAO;IACzB,QAAQ,IAAI,MAAM,OAAO;IAEzB,IAAI,SAAS,IAAI,OAAO;IACxB,IAAI,MAAM,IAAI,CAAC,SAAS;QACtB,OAAO;IACT;IAEA,SAAS,IAAI,OAAO;IACpB,IAAI,MAAM,IAAI,CAAC,SAAS;QACtB,OAAO;IACT;IAEA,SAAS;IACT,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,GAAG,CAAC,MAAM,EAAE,EAAE,EAAG;QACzC,MAAM,cAAc,MAAM,GAAG,CAAC,EAAE;QAEhC,IAAI,SAAS;QACb,YAAY,OAAO,CAAC,CAAC;YACnB,8DAA8D;YAC9D,MAAM,UAAU,IAAI,OAAO,WAAW,MAAM,CAAC,OAAO;YACpD,OAAQ,WAAW,QAAQ;gBACzB,KAAK;oBACH,IAAI,QAAQ,UAAU,CAAC,MAAM,KAAK,GAAG;wBACnC,QAAQ,KAAK;oBACf,OAAO;wBACL,QAAQ,UAAU,CAAC,IAAI,CAAC;oBAC1B;oBACA,QAAQ,GAAG,GAAG,QAAQ,MAAM;gBAC5B,eAAe,GACjB,KAAK;gBACL,KAAK;oBACH,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;wBAClC,SAAS;oBACX;oBACA;gBACF,KAAK;gBACL,KAAK;oBAEH;gBACF,wBAAwB,GACxB;oBACE,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,WAAW,QAAQ,EAAE;YAClE;QACF;QACA,IAAI,UAAU,CAAC,CAAC,UAAU,GAAG,QAAQ,OAAO,GAAG;YAC7C,SAAS;QACX;IACF;IAEA,IAAI,UAAU,MAAM,IAAI,CAAC,SAAS;QAChC,OAAO;IACT;IAEA,OAAO;AACT;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3291, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/ranges/valid.js"], "sourcesContent": ["'use strict'\n\nconst Range = require('../classes/range')\nconst validRange = (range, options) => {\n  try {\n    // Return '*' instead of '' so that truthiness works.\n    // This will throw if it's invalid anyway\n    return new Range(range, options).range || '*'\n  } catch (er) {\n    return null\n  }\n}\nmodule.exports = validRange\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,aAAa,CAAC,OAAO;IACzB,IAAI;QACF,qDAAqD;QACrD,yCAAyC;QACzC,OAAO,IAAI,MAAM,OAAO,SAAS,KAAK,IAAI;IAC5C,EAAE,OAAO,IAAI;QACX,OAAO;IACT;AACF;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3308, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/ranges/outside.js"], "sourcesContent": ["'use strict'\n\nconst SemVer = require('../classes/semver')\nconst Comparator = require('../classes/comparator')\nconst { ANY } = Comparator\nconst Range = require('../classes/range')\nconst satisfies = require('../functions/satisfies')\nconst gt = require('../functions/gt')\nconst lt = require('../functions/lt')\nconst lte = require('../functions/lte')\nconst gte = require('../functions/gte')\n\nconst outside = (version, range, hilo, options) => {\n  version = new SemVer(version, options)\n  range = new Range(range, options)\n\n  let gtfn, ltefn, ltfn, comp, ecomp\n  switch (hilo) {\n    case '>':\n      gtfn = gt\n      ltefn = lte\n      ltfn = lt\n      comp = '>'\n      ecomp = '>='\n      break\n    case '<':\n      gtfn = lt\n      ltefn = gte\n      ltfn = gt\n      comp = '<'\n      ecomp = '<='\n      break\n    default:\n      throw new TypeError('Must provide a hilo val of \"<\" or \">\"')\n  }\n\n  // If it satisfies the range it is not outside\n  if (satisfies(version, range, options)) {\n    return false\n  }\n\n  // From now on, variable terms are as if we're in \"gtr\" mode.\n  // but note that everything is flipped for the \"ltr\" function.\n\n  for (let i = 0; i < range.set.length; ++i) {\n    const comparators = range.set[i]\n\n    let high = null\n    let low = null\n\n    comparators.forEach((comparator) => {\n      if (comparator.semver === ANY) {\n        comparator = new Comparator('>=0.0.0')\n      }\n      high = high || comparator\n      low = low || comparator\n      if (gtfn(comparator.semver, high.semver, options)) {\n        high = comparator\n      } else if (ltfn(comparator.semver, low.semver, options)) {\n        low = comparator\n      }\n    })\n\n    // If the edge version comparator has a operator then our version\n    // isn't outside it\n    if (high.operator === comp || high.operator === ecomp) {\n      return false\n    }\n\n    // If the lowest version comparator has an operator and our version\n    // is less than it then it isn't higher than the range\n    if ((!low.operator || low.operator === comp) &&\n        ltefn(version, low.semver)) {\n      return false\n    } else if (low.operator === ecomp && ltfn(version, low.semver)) {\n      return false\n    }\n  }\n  return true\n}\n\nmodule.exports = outside\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM;AACN,MAAM,EAAE,GAAG,EAAE,GAAG;AAChB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AAEN,MAAM,UAAU,CAAC,SAAS,OAAO,MAAM;IACrC,UAAU,IAAI,OAAO,SAAS;IAC9B,QAAQ,IAAI,MAAM,OAAO;IAEzB,IAAI,MAAM,OAAO,MAAM,MAAM;IAC7B,OAAQ;QACN,KAAK;YACH,OAAO;YACP,QAAQ;YACR,OAAO;YACP,OAAO;YACP,QAAQ;YACR;QACF,KAAK;YACH,OAAO;YACP,QAAQ;YACR,OAAO;YACP,OAAO;YACP,QAAQ;YACR;QACF;YACE,MAAM,IAAI,UAAU;IACxB;IAEA,8CAA8C;IAC9C,IAAI,UAAU,SAAS,OAAO,UAAU;QACtC,OAAO;IACT;IAEA,6DAA6D;IAC7D,8DAA8D;IAE9D,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,GAAG,CAAC,MAAM,EAAE,EAAE,EAAG;QACzC,MAAM,cAAc,MAAM,GAAG,CAAC,EAAE;QAEhC,IAAI,OAAO;QACX,IAAI,MAAM;QAEV,YAAY,OAAO,CAAC,CAAC;YACnB,IAAI,WAAW,MAAM,KAAK,KAAK;gBAC7B,aAAa,IAAI,WAAW;YAC9B;YACA,OAAO,QAAQ;YACf,MAAM,OAAO;YACb,IAAI,KAAK,WAAW,MAAM,EAAE,KAAK,MAAM,EAAE,UAAU;gBACjD,OAAO;YACT,OAAO,IAAI,KAAK,WAAW,MAAM,EAAE,IAAI,MAAM,EAAE,UAAU;gBACvD,MAAM;YACR;QACF;QAEA,iEAAiE;QACjE,mBAAmB;QACnB,IAAI,KAAK,QAAQ,KAAK,QAAQ,KAAK,QAAQ,KAAK,OAAO;YACrD,OAAO;QACT;QAEA,mEAAmE;QACnE,sDAAsD;QACtD,IAAI,CAAC,CAAC,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK,IAAI,KACvC,MAAM,SAAS,IAAI,MAAM,GAAG;YAC9B,OAAO;QACT,OAAO,IAAI,IAAI,QAAQ,KAAK,SAAS,KAAK,SAAS,IAAI,MAAM,GAAG;YAC9D,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3383, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/ranges/gtr.js"], "sourcesContent": ["'use strict'\n\n// Determine if version is greater than all the versions possible in the range.\nconst outside = require('./outside')\nconst gtr = (version, range, options) => outside(version, range, '>', options)\nmodule.exports = gtr\n"], "names": [], "mappings": "AAAA;AAEA,+EAA+E;AAC/E,MAAM;AACN,MAAM,MAAM,CAAC,SAAS,OAAO,UAAY,QAAQ,SAAS,OAAO,KAAK;AACtE,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/ranges/ltr.js"], "sourcesContent": ["'use strict'\n\nconst outside = require('./outside')\n// Determine if version is less than all the versions possible in the range\nconst ltr = (version, range, options) => outside(version, range, '<', options)\nmodule.exports = ltr\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,2EAA2E;AAC3E,MAAM,MAAM,CAAC,SAAS,OAAO,UAAY,QAAQ,SAAS,OAAO,KAAK;AACtE,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/ranges/intersects.js"], "sourcesContent": ["'use strict'\n\nconst Range = require('../classes/range')\nconst intersects = (r1, r2, options) => {\n  r1 = new Range(r1, options)\n  r2 = new Range(r2, options)\n  return r1.intersects(r2, options)\n}\nmodule.exports = intersects\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,aAAa,CAAC,IAAI,IAAI;IAC1B,KAAK,IAAI,MAAM,IAAI;IACnB,KAAK,IAAI,MAAM,IAAI;IACnB,OAAO,GAAG,UAAU,CAAC,IAAI;AAC3B;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3416, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/ranges/simplify.js"], "sourcesContent": ["'use strict'\n\n// given a set of versions and a range, create a \"simplified\" range\n// that includes the same versions that the original range does\n// If the original range is shorter than the simplified one, return that.\nconst satisfies = require('../functions/satisfies.js')\nconst compare = require('../functions/compare.js')\nmodule.exports = (versions, range, options) => {\n  const set = []\n  let first = null\n  let prev = null\n  const v = versions.sort((a, b) => compare(a, b, options))\n  for (const version of v) {\n    const included = satisfies(version, range, options)\n    if (included) {\n      prev = version\n      if (!first) {\n        first = version\n      }\n    } else {\n      if (prev) {\n        set.push([first, prev])\n      }\n      prev = null\n      first = null\n    }\n  }\n  if (first) {\n    set.push([first, null])\n  }\n\n  const ranges = []\n  for (const [min, max] of set) {\n    if (min === max) {\n      ranges.push(min)\n    } else if (!max && min === v[0]) {\n      ranges.push('*')\n    } else if (!max) {\n      ranges.push(`>=${min}`)\n    } else if (min === v[0]) {\n      ranges.push(`<=${max}`)\n    } else {\n      ranges.push(`${min} - ${max}`)\n    }\n  }\n  const simplified = ranges.join(' || ')\n  const original = typeof range.raw === 'string' ? range.raw : String(range)\n  return simplified.length < original.length ? simplified : range\n}\n"], "names": [], "mappings": "AAAA;AAEA,mEAAmE;AACnE,+DAA+D;AAC/D,yEAAyE;AACzE,MAAM;AACN,MAAM;AACN,OAAO,OAAO,GAAG,CAAC,UAAU,OAAO;IACjC,MAAM,MAAM,EAAE;IACd,IAAI,QAAQ;IACZ,IAAI,OAAO;IACX,MAAM,IAAI,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,QAAQ,GAAG,GAAG;IAChD,KAAK,MAAM,WAAW,EAAG;QACvB,MAAM,WAAW,UAAU,SAAS,OAAO;QAC3C,IAAI,UAAU;YACZ,OAAO;YACP,IAAI,CAAC,OAAO;gBACV,QAAQ;YACV;QACF,OAAO;YACL,IAAI,MAAM;gBACR,IAAI,IAAI,CAAC;oBAAC;oBAAO;iBAAK;YACxB;YACA,OAAO;YACP,QAAQ;QACV;IACF;IACA,IAAI,OAAO;QACT,IAAI,IAAI,CAAC;YAAC;YAAO;SAAK;IACxB;IAEA,MAAM,SAAS,EAAE;IACjB,KAAK,MAAM,CAAC,KAAK,IAAI,IAAI,IAAK;QAC5B,IAAI,QAAQ,KAAK;YACf,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,CAAC,OAAO,QAAQ,CAAC,CAAC,EAAE,EAAE;YAC/B,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,CAAC,KAAK;YACf,OAAO,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK;QACxB,OAAO,IAAI,QAAQ,CAAC,CAAC,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK;QACxB,OAAO;YACL,OAAO,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE,KAAK;QAC/B;IACF;IACA,MAAM,aAAa,OAAO,IAAI,CAAC;IAC/B,MAAM,WAAW,OAAO,MAAM,GAAG,KAAK,WAAW,MAAM,GAAG,GAAG,OAAO;IACpE,OAAO,WAAW,MAAM,GAAG,SAAS,MAAM,GAAG,aAAa;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3474, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/ranges/subset.js"], "sourcesContent": ["'use strict'\n\nconst Range = require('../classes/range.js')\nconst Comparator = require('../classes/comparator.js')\nconst { ANY } = Comparator\nconst satisfies = require('../functions/satisfies.js')\nconst compare = require('../functions/compare.js')\n\n// Complex range `r1 || r2 || ...` is a subset of `R1 || R2 || ...` iff:\n// - Every simple range `r1, r2, ...` is a null set, OR\n// - Every simple range `r1, r2, ...` which is not a null set is a subset of\n//   some `R1, R2, ...`\n//\n// Simple range `c1 c2 ...` is a subset of simple range `C1 C2 ...` iff:\n// - If c is only the ANY comparator\n//   - If C is only the ANY comparator, return true\n//   - Else if in prerelease mode, return false\n//   - else replace c with `[>=0.0.0]`\n// - If C is only the ANY comparator\n//   - if in prerelease mode, return true\n//   - else replace C with `[>=0.0.0]`\n// - Let EQ be the set of = comparators in c\n// - If EQ is more than one, return true (null set)\n// - Let GT be the highest > or >= comparator in c\n// - Let LT be the lowest < or <= comparator in c\n// - If GT and LT, and GT.semver > LT.semver, return true (null set)\n// - If any C is a = range, and GT or LT are set, return false\n// - If EQ\n//   - If GT, and EQ does not satisfy GT, return true (null set)\n//   - If LT, and EQ does not satisfy LT, return true (null set)\n//   - If EQ satisfies every C, return true\n//   - Else return false\n// - If GT\n//   - If GT.semver is lower than any > or >= comp in C, return false\n//   - If GT is >=, and GT.semver does not satisfy every C, return false\n//   - If GT.semver has a prerelease, and not in prerelease mode\n//     - If no C has a prerelease and the GT.semver tuple, return false\n// - If LT\n//   - If LT.semver is greater than any < or <= comp in C, return false\n//   - If LT is <=, and LT.semver does not satisfy every C, return false\n//   - If GT.semver has a prerelease, and not in prerelease mode\n//     - If no C has a prerelease and the LT.semver tuple, return false\n// - Else return true\n\nconst subset = (sub, dom, options = {}) => {\n  if (sub === dom) {\n    return true\n  }\n\n  sub = new Range(sub, options)\n  dom = new Range(dom, options)\n  let sawNonNull = false\n\n  OUTER: for (const simpleSub of sub.set) {\n    for (const simpleDom of dom.set) {\n      const isSub = simpleSubset(simpleSub, simpleDom, options)\n      sawNonNull = sawNonNull || isSub !== null\n      if (isSub) {\n        continue OUTER\n      }\n    }\n    // the null set is a subset of everything, but null simple ranges in\n    // a complex range should be ignored.  so if we saw a non-null range,\n    // then we know this isn't a subset, but if EVERY simple range was null,\n    // then it is a subset.\n    if (sawNonNull) {\n      return false\n    }\n  }\n  return true\n}\n\nconst minimumVersionWithPreRelease = [new Comparator('>=0.0.0-0')]\nconst minimumVersion = [new Comparator('>=0.0.0')]\n\nconst simpleSubset = (sub, dom, options) => {\n  if (sub === dom) {\n    return true\n  }\n\n  if (sub.length === 1 && sub[0].semver === ANY) {\n    if (dom.length === 1 && dom[0].semver === ANY) {\n      return true\n    } else if (options.includePrerelease) {\n      sub = minimumVersionWithPreRelease\n    } else {\n      sub = minimumVersion\n    }\n  }\n\n  if (dom.length === 1 && dom[0].semver === ANY) {\n    if (options.includePrerelease) {\n      return true\n    } else {\n      dom = minimumVersion\n    }\n  }\n\n  const eqSet = new Set()\n  let gt, lt\n  for (const c of sub) {\n    if (c.operator === '>' || c.operator === '>=') {\n      gt = higherGT(gt, c, options)\n    } else if (c.operator === '<' || c.operator === '<=') {\n      lt = lowerLT(lt, c, options)\n    } else {\n      eqSet.add(c.semver)\n    }\n  }\n\n  if (eqSet.size > 1) {\n    return null\n  }\n\n  let gtltComp\n  if (gt && lt) {\n    gtltComp = compare(gt.semver, lt.semver, options)\n    if (gtltComp > 0) {\n      return null\n    } else if (gtltComp === 0 && (gt.operator !== '>=' || lt.operator !== '<=')) {\n      return null\n    }\n  }\n\n  // will iterate one or zero times\n  for (const eq of eqSet) {\n    if (gt && !satisfies(eq, String(gt), options)) {\n      return null\n    }\n\n    if (lt && !satisfies(eq, String(lt), options)) {\n      return null\n    }\n\n    for (const c of dom) {\n      if (!satisfies(eq, String(c), options)) {\n        return false\n      }\n    }\n\n    return true\n  }\n\n  let higher, lower\n  let hasDomLT, hasDomGT\n  // if the subset has a prerelease, we need a comparator in the superset\n  // with the same tuple and a prerelease, or it's not a subset\n  let needDomLTPre = lt &&\n    !options.includePrerelease &&\n    lt.semver.prerelease.length ? lt.semver : false\n  let needDomGTPre = gt &&\n    !options.includePrerelease &&\n    gt.semver.prerelease.length ? gt.semver : false\n  // exception: <1.2.3-0 is the same as <1.2.3\n  if (needDomLTPre && needDomLTPre.prerelease.length === 1 &&\n      lt.operator === '<' && needDomLTPre.prerelease[0] === 0) {\n    needDomLTPre = false\n  }\n\n  for (const c of dom) {\n    hasDomGT = hasDomGT || c.operator === '>' || c.operator === '>='\n    hasDomLT = hasDomLT || c.operator === '<' || c.operator === '<='\n    if (gt) {\n      if (needDomGTPre) {\n        if (c.semver.prerelease && c.semver.prerelease.length &&\n            c.semver.major === needDomGTPre.major &&\n            c.semver.minor === needDomGTPre.minor &&\n            c.semver.patch === needDomGTPre.patch) {\n          needDomGTPre = false\n        }\n      }\n      if (c.operator === '>' || c.operator === '>=') {\n        higher = higherGT(gt, c, options)\n        if (higher === c && higher !== gt) {\n          return false\n        }\n      } else if (gt.operator === '>=' && !satisfies(gt.semver, String(c), options)) {\n        return false\n      }\n    }\n    if (lt) {\n      if (needDomLTPre) {\n        if (c.semver.prerelease && c.semver.prerelease.length &&\n            c.semver.major === needDomLTPre.major &&\n            c.semver.minor === needDomLTPre.minor &&\n            c.semver.patch === needDomLTPre.patch) {\n          needDomLTPre = false\n        }\n      }\n      if (c.operator === '<' || c.operator === '<=') {\n        lower = lowerLT(lt, c, options)\n        if (lower === c && lower !== lt) {\n          return false\n        }\n      } else if (lt.operator === '<=' && !satisfies(lt.semver, String(c), options)) {\n        return false\n      }\n    }\n    if (!c.operator && (lt || gt) && gtltComp !== 0) {\n      return false\n    }\n  }\n\n  // if there was a < or >, and nothing in the dom, then must be false\n  // UNLESS it was limited by another range in the other direction.\n  // Eg, >1.0.0 <1.0.1 is still a subset of <2.0.0\n  if (gt && hasDomLT && !lt && gtltComp !== 0) {\n    return false\n  }\n\n  if (lt && hasDomGT && !gt && gtltComp !== 0) {\n    return false\n  }\n\n  // we needed a prerelease range in a specific tuple, but didn't get one\n  // then this isn't a subset.  eg >=1.2.3-pre is not a subset of >=1.0.0,\n  // because it includes prereleases in the 1.2.3 tuple\n  if (needDomGTPre || needDomLTPre) {\n    return false\n  }\n\n  return true\n}\n\n// >=1.2.3 is lower than >1.2.3\nconst higherGT = (a, b, options) => {\n  if (!a) {\n    return b\n  }\n  const comp = compare(a.semver, b.semver, options)\n  return comp > 0 ? a\n    : comp < 0 ? b\n    : b.operator === '>' && a.operator === '>=' ? b\n    : a\n}\n\n// <=1.2.3 is higher than <1.2.3\nconst lowerLT = (a, b, options) => {\n  if (!a) {\n    return b\n  }\n  const comp = compare(a.semver, b.semver, options)\n  return comp < 0 ? a\n    : comp > 0 ? b\n    : b.operator === '<' && a.operator === '<=' ? b\n    : a\n}\n\nmodule.exports = subset\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM;AACN,MAAM,EAAE,GAAG,EAAE,GAAG;AAChB,MAAM;AACN,MAAM;AAEN,wEAAwE;AACxE,uDAAuD;AACvD,4EAA4E;AAC5E,uBAAuB;AACvB,EAAE;AACF,wEAAwE;AACxE,oCAAoC;AACpC,mDAAmD;AACnD,+CAA+C;AAC/C,sCAAsC;AACtC,oCAAoC;AACpC,yCAAyC;AACzC,sCAAsC;AACtC,4CAA4C;AAC5C,mDAAmD;AACnD,kDAAkD;AAClD,iDAAiD;AACjD,oEAAoE;AACpE,8DAA8D;AAC9D,UAAU;AACV,gEAAgE;AAChE,gEAAgE;AAChE,2CAA2C;AAC3C,wBAAwB;AACxB,UAAU;AACV,qEAAqE;AACrE,wEAAwE;AACxE,gEAAgE;AAChE,uEAAuE;AACvE,UAAU;AACV,uEAAuE;AACvE,wEAAwE;AACxE,gEAAgE;AAChE,uEAAuE;AACvE,qBAAqB;AAErB,MAAM,SAAS,CAAC,KAAK,KAAK,UAAU,CAAC,CAAC;IACpC,IAAI,QAAQ,KAAK;QACf,OAAO;IACT;IAEA,MAAM,IAAI,MAAM,KAAK;IACrB,MAAM,IAAI,MAAM,KAAK;IACrB,IAAI,aAAa;IAEjB,OAAO,KAAK,MAAM,aAAa,IAAI,GAAG,CAAE;QACtC,KAAK,MAAM,aAAa,IAAI,GAAG,CAAE;YAC/B,MAAM,QAAQ,aAAa,WAAW,WAAW;YACjD,aAAa,cAAc,UAAU;YACrC,IAAI,OAAO;gBACT,SAAS;YACX;QACF;QACA,oEAAoE;QACpE,qEAAqE;QACrE,wEAAwE;QACxE,uBAAuB;QACvB,IAAI,YAAY;YACd,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,MAAM,+BAA+B;IAAC,IAAI,WAAW;CAAa;AAClE,MAAM,iBAAiB;IAAC,IAAI,WAAW;CAAW;AAElD,MAAM,eAAe,CAAC,KAAK,KAAK;IAC9B,IAAI,QAAQ,KAAK;QACf,OAAO;IACT;IAEA,IAAI,IAAI,MAAM,KAAK,KAAK,GAAG,CAAC,EAAE,CAAC,MAAM,KAAK,KAAK;QAC7C,IAAI,IAAI,MAAM,KAAK,KAAK,GAAG,CAAC,EAAE,CAAC,MAAM,KAAK,KAAK;YAC7C,OAAO;QACT,OAAO,IAAI,QAAQ,iBAAiB,EAAE;YACpC,MAAM;QACR,OAAO;YACL,MAAM;QACR;IACF;IAEA,IAAI,IAAI,MAAM,KAAK,KAAK,GAAG,CAAC,EAAE,CAAC,MAAM,KAAK,KAAK;QAC7C,IAAI,QAAQ,iBAAiB,EAAE;YAC7B,OAAO;QACT,OAAO;YACL,MAAM;QACR;IACF;IAEA,MAAM,QAAQ,IAAI;IAClB,IAAI,IAAI;IACR,KAAK,MAAM,KAAK,IAAK;QACnB,IAAI,EAAE,QAAQ,KAAK,OAAO,EAAE,QAAQ,KAAK,MAAM;YAC7C,KAAK,SAAS,IAAI,GAAG;QACvB,OAAO,IAAI,EAAE,QAAQ,KAAK,OAAO,EAAE,QAAQ,KAAK,MAAM;YACpD,KAAK,QAAQ,IAAI,GAAG;QACtB,OAAO;YACL,MAAM,GAAG,CAAC,EAAE,MAAM;QACpB;IACF;IAEA,IAAI,MAAM,IAAI,GAAG,GAAG;QAClB,OAAO;IACT;IAEA,IAAI;IACJ,IAAI,MAAM,IAAI;QACZ,WAAW,QAAQ,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE;QACzC,IAAI,WAAW,GAAG;YAChB,OAAO;QACT,OAAO,IAAI,aAAa,KAAK,CAAC,GAAG,QAAQ,KAAK,QAAQ,GAAG,QAAQ,KAAK,IAAI,GAAG;YAC3E,OAAO;QACT;IACF;IAEA,iCAAiC;IACjC,KAAK,MAAM,MAAM,MAAO;QACtB,IAAI,MAAM,CAAC,UAAU,IAAI,OAAO,KAAK,UAAU;YAC7C,OAAO;QACT;QAEA,IAAI,MAAM,CAAC,UAAU,IAAI,OAAO,KAAK,UAAU;YAC7C,OAAO;QACT;QAEA,KAAK,MAAM,KAAK,IAAK;YACnB,IAAI,CAAC,UAAU,IAAI,OAAO,IAAI,UAAU;gBACtC,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,IAAI,QAAQ;IACZ,IAAI,UAAU;IACd,uEAAuE;IACvE,6DAA6D;IAC7D,IAAI,eAAe,MACjB,CAAC,QAAQ,iBAAiB,IAC1B,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,MAAM,GAAG;IAC5C,IAAI,eAAe,MACjB,CAAC,QAAQ,iBAAiB,IAC1B,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,MAAM,GAAG;IAC5C,4CAA4C;IAC5C,IAAI,gBAAgB,aAAa,UAAU,CAAC,MAAM,KAAK,KACnD,GAAG,QAAQ,KAAK,OAAO,aAAa,UAAU,CAAC,EAAE,KAAK,GAAG;QAC3D,eAAe;IACjB;IAEA,KAAK,MAAM,KAAK,IAAK;QACnB,WAAW,YAAY,EAAE,QAAQ,KAAK,OAAO,EAAE,QAAQ,KAAK;QAC5D,WAAW,YAAY,EAAE,QAAQ,KAAK,OAAO,EAAE,QAAQ,KAAK;QAC5D,IAAI,IAAI;YACN,IAAI,cAAc;gBAChB,IAAI,EAAE,MAAM,CAAC,UAAU,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC,MAAM,IACjD,EAAE,MAAM,CAAC,KAAK,KAAK,aAAa,KAAK,IACrC,EAAE,MAAM,CAAC,KAAK,KAAK,aAAa,KAAK,IACrC,EAAE,MAAM,CAAC,KAAK,KAAK,aAAa,KAAK,EAAE;oBACzC,eAAe;gBACjB;YACF;YACA,IAAI,EAAE,QAAQ,KAAK,OAAO,EAAE,QAAQ,KAAK,MAAM;gBAC7C,SAAS,SAAS,IAAI,GAAG;gBACzB,IAAI,WAAW,KAAK,WAAW,IAAI;oBACjC,OAAO;gBACT;YACF,OAAO,IAAI,GAAG,QAAQ,KAAK,QAAQ,CAAC,UAAU,GAAG,MAAM,EAAE,OAAO,IAAI,UAAU;gBAC5E,OAAO;YACT;QACF;QACA,IAAI,IAAI;YACN,IAAI,cAAc;gBAChB,IAAI,EAAE,MAAM,CAAC,UAAU,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC,MAAM,IACjD,EAAE,MAAM,CAAC,KAAK,KAAK,aAAa,KAAK,IACrC,EAAE,MAAM,CAAC,KAAK,KAAK,aAAa,KAAK,IACrC,EAAE,MAAM,CAAC,KAAK,KAAK,aAAa,KAAK,EAAE;oBACzC,eAAe;gBACjB;YACF;YACA,IAAI,EAAE,QAAQ,KAAK,OAAO,EAAE,QAAQ,KAAK,MAAM;gBAC7C,QAAQ,QAAQ,IAAI,GAAG;gBACvB,IAAI,UAAU,KAAK,UAAU,IAAI;oBAC/B,OAAO;gBACT;YACF,OAAO,IAAI,GAAG,QAAQ,KAAK,QAAQ,CAAC,UAAU,GAAG,MAAM,EAAE,OAAO,IAAI,UAAU;gBAC5E,OAAO;YACT;QACF;QACA,IAAI,CAAC,EAAE,QAAQ,IAAI,CAAC,MAAM,EAAE,KAAK,aAAa,GAAG;YAC/C,OAAO;QACT;IACF;IAEA,oEAAoE;IACpE,iEAAiE;IACjE,gDAAgD;IAChD,IAAI,MAAM,YAAY,CAAC,MAAM,aAAa,GAAG;QAC3C,OAAO;IACT;IAEA,IAAI,MAAM,YAAY,CAAC,MAAM,aAAa,GAAG;QAC3C,OAAO;IACT;IAEA,uEAAuE;IACvE,wEAAwE;IACxE,qDAAqD;IACrD,IAAI,gBAAgB,cAAc;QAChC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,+BAA+B;AAC/B,MAAM,WAAW,CAAC,GAAG,GAAG;IACtB,IAAI,CAAC,GAAG;QACN,OAAO;IACT;IACA,MAAM,OAAO,QAAQ,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE;IACzC,OAAO,OAAO,IAAI,IACd,OAAO,IAAI,IACX,EAAE,QAAQ,KAAK,OAAO,EAAE,QAAQ,KAAK,OAAO,IAC5C;AACN;AAEA,gCAAgC;AAChC,MAAM,UAAU,CAAC,GAAG,GAAG;IACrB,IAAI,CAAC,GAAG;QACN,OAAO;IACT;IACA,MAAM,OAAO,QAAQ,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE;IACzC,OAAO,OAAO,IAAI,IACd,OAAO,IAAI,IACX,EAAE,QAAQ,KAAK,OAAO,EAAE,QAAQ,KAAK,OAAO,IAC5C;AACN;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3690, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/semver/index.js"], "sourcesContent": ["'use strict'\n\n// just pre-load all the stuff that index.js lazily exports\nconst internalRe = require('./internal/re')\nconst constants = require('./internal/constants')\nconst SemVer = require('./classes/semver')\nconst identifiers = require('./internal/identifiers')\nconst parse = require('./functions/parse')\nconst valid = require('./functions/valid')\nconst clean = require('./functions/clean')\nconst inc = require('./functions/inc')\nconst diff = require('./functions/diff')\nconst major = require('./functions/major')\nconst minor = require('./functions/minor')\nconst patch = require('./functions/patch')\nconst prerelease = require('./functions/prerelease')\nconst compare = require('./functions/compare')\nconst rcompare = require('./functions/rcompare')\nconst compareLoose = require('./functions/compare-loose')\nconst compareBuild = require('./functions/compare-build')\nconst sort = require('./functions/sort')\nconst rsort = require('./functions/rsort')\nconst gt = require('./functions/gt')\nconst lt = require('./functions/lt')\nconst eq = require('./functions/eq')\nconst neq = require('./functions/neq')\nconst gte = require('./functions/gte')\nconst lte = require('./functions/lte')\nconst cmp = require('./functions/cmp')\nconst coerce = require('./functions/coerce')\nconst Comparator = require('./classes/comparator')\nconst Range = require('./classes/range')\nconst satisfies = require('./functions/satisfies')\nconst toComparators = require('./ranges/to-comparators')\nconst maxSatisfying = require('./ranges/max-satisfying')\nconst minSatisfying = require('./ranges/min-satisfying')\nconst minVersion = require('./ranges/min-version')\nconst validRange = require('./ranges/valid')\nconst outside = require('./ranges/outside')\nconst gtr = require('./ranges/gtr')\nconst ltr = require('./ranges/ltr')\nconst intersects = require('./ranges/intersects')\nconst simplifyRange = require('./ranges/simplify')\nconst subset = require('./ranges/subset')\nmodule.exports = {\n  parse,\n  valid,\n  clean,\n  inc,\n  diff,\n  major,\n  minor,\n  patch,\n  prerelease,\n  compare,\n  rcompare,\n  compareLoose,\n  compareBuild,\n  sort,\n  rsort,\n  gt,\n  lt,\n  eq,\n  neq,\n  gte,\n  lte,\n  cmp,\n  coerce,\n  Comparator,\n  Range,\n  satisfies,\n  toComparators,\n  maxSatisfying,\n  minSatisfying,\n  minVersion,\n  validRange,\n  outside,\n  gtr,\n  ltr,\n  intersects,\n  simplifyRange,\n  subset,\n  SemVer,\n  re: internalRe.re,\n  src: internalRe.src,\n  tokens: internalRe.t,\n  SEMVER_SPEC_VERSION: constants.SEMVER_SPEC_VERSION,\n  RELEASE_TYPES: constants.RELEASE_TYPES,\n  compareIdentifiers: identifiers.compareIdentifiers,\n  rcompareIdentifiers: identifiers.rcompareIdentifiers,\n}\n"], "names": [], "mappings": "AAAA;AAEA,2DAA2D;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,OAAO,OAAO,GAAG;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,EAAE;IACjB,KAAK,WAAW,GAAG;IACnB,QAAQ,WAAW,CAAC;IACpB,qBAAqB,UAAU,mBAAmB;IAClD,eAAe,UAAU,aAAa;IACtC,oBAAoB,YAAY,kBAAkB;IAClD,qBAAqB,YAAY,mBAAmB;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3785, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/lodash.includes/index.js"], "sourcesContent": ["/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0,\n    MAX_SAFE_INTEGER = 9007199254740991,\n    MAX_INTEGER = 1.7976931348623157e+308,\n    NAN = 0 / 0;\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    stringTag = '[object String]',\n    symbolTag = '[object Symbol]';\n\n/** Used to match leading and trailing whitespace. */\nvar reTrim = /^\\s+|\\s+$/g;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Used to detect unsigned integer values. */\nvar reIsUint = /^(?:0|[1-9]\\d*)$/;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/**\n * A specialized version of `_.map` for arrays without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n */\nfunction arrayMap(array, iteratee) {\n  var index = -1,\n      length = array ? array.length : 0,\n      result = Array(length);\n\n  while (++index < length) {\n    result[index] = iteratee(array[index], index, array);\n  }\n  return result;\n}\n\n/**\n * The base implementation of `_.findIndex` and `_.findLastIndex` without\n * support for iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Function} predicate The function invoked per iteration.\n * @param {number} fromIndex The index to search from.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction baseFindIndex(array, predicate, fromIndex, fromRight) {\n  var length = array.length,\n      index = fromIndex + (fromRight ? 1 : -1);\n\n  while ((fromRight ? index-- : ++index < length)) {\n    if (predicate(array[index], index, array)) {\n      return index;\n    }\n  }\n  return -1;\n}\n\n/**\n * The base implementation of `_.indexOf` without `fromIndex` bounds checks.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} fromIndex The index to search from.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction baseIndexOf(array, value, fromIndex) {\n  if (value !== value) {\n    return baseFindIndex(array, baseIsNaN, fromIndex);\n  }\n  var index = fromIndex - 1,\n      length = array.length;\n\n  while (++index < length) {\n    if (array[index] === value) {\n      return index;\n    }\n  }\n  return -1;\n}\n\n/**\n * The base implementation of `_.isNaN` without support for number objects.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is `NaN`, else `false`.\n */\nfunction baseIsNaN(value) {\n  return value !== value;\n}\n\n/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */\nfunction baseTimes(n, iteratee) {\n  var index = -1,\n      result = Array(n);\n\n  while (++index < n) {\n    result[index] = iteratee(index);\n  }\n  return result;\n}\n\n/**\n * The base implementation of `_.values` and `_.valuesIn` which creates an\n * array of `object` property values corresponding to the property names\n * of `props`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} props The property names to get values for.\n * @returns {Object} Returns the array of property values.\n */\nfunction baseValues(object, props) {\n  return arrayMap(props, function(key) {\n    return object[key];\n  });\n}\n\n/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func, transform) {\n  return function(arg) {\n    return func(transform(arg));\n  };\n}\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeKeys = overArg(Object.keys, Object),\n    nativeMax = Math.max;\n\n/**\n * Creates an array of the enumerable property names of the array-like `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @param {boolean} inherited Specify returning inherited property names.\n * @returns {Array} Returns the array of property names.\n */\nfunction arrayLikeKeys(value, inherited) {\n  // Safari 8.1 makes `arguments.callee` enumerable in strict mode.\n  // Safari 9 makes `arguments.length` enumerable in strict mode.\n  var result = (isArray(value) || isArguments(value))\n    ? baseTimes(value.length, String)\n    : [];\n\n  var length = result.length,\n      skipIndexes = !!length;\n\n  for (var key in value) {\n    if ((inherited || hasOwnProperty.call(value, key)) &&\n        !(skipIndexes && (key == 'length' || isIndex(key, length)))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeys(object) {\n  if (!isPrototype(object)) {\n    return nativeKeys(object);\n  }\n  var result = [];\n  for (var key in Object(object)) {\n    if (hasOwnProperty.call(object, key) && key != 'constructor') {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * Checks if `value` is a valid array-like index.\n *\n * @private\n * @param {*} value The value to check.\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\n */\nfunction isIndex(value, length) {\n  length = length == null ? MAX_SAFE_INTEGER : length;\n  return !!length &&\n    (typeof value == 'number' || reIsUint.test(value)) &&\n    (value > -1 && value % 1 == 0 && value < length);\n}\n\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor,\n      proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto;\n\n  return value === proto;\n}\n\n/**\n * Checks if `value` is in `collection`. If `collection` is a string, it's\n * checked for a substring of `value`, otherwise\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * is used for equality comparisons. If `fromIndex` is negative, it's used as\n * the offset from the end of `collection`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object|string} collection The collection to inspect.\n * @param {*} value The value to search for.\n * @param {number} [fromIndex=0] The index to search from.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.reduce`.\n * @returns {boolean} Returns `true` if `value` is found, else `false`.\n * @example\n *\n * _.includes([1, 2, 3], 1);\n * // => true\n *\n * _.includes([1, 2, 3], 1, 2);\n * // => false\n *\n * _.includes({ 'a': 1, 'b': 2 }, 1);\n * // => true\n *\n * _.includes('abcd', 'bc');\n * // => true\n */\nfunction includes(collection, value, fromIndex, guard) {\n  collection = isArrayLike(collection) ? collection : values(collection);\n  fromIndex = (fromIndex && !guard) ? toInteger(fromIndex) : 0;\n\n  var length = collection.length;\n  if (fromIndex < 0) {\n    fromIndex = nativeMax(length + fromIndex, 0);\n  }\n  return isString(collection)\n    ? (fromIndex <= length && collection.indexOf(value, fromIndex) > -1)\n    : (!!length && baseIndexOf(collection, value, fromIndex) > -1);\n}\n\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */\nfunction isArguments(value) {\n  // Safari 8.1 makes `arguments.callee` enumerable in strict mode.\n  return isArrayLikeObject(value) && hasOwnProperty.call(value, 'callee') &&\n    (!propertyIsEnumerable.call(value, 'callee') || objectToString.call(value) == argsTag);\n}\n\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */\nvar isArray = Array.isArray;\n\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */\nfunction isArrayLike(value) {\n  return value != null && isLength(value.length) && !isFunction(value);\n}\n\n/**\n * This method is like `_.isArrayLike` except that it also checks if `value`\n * is an object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array-like object,\n *  else `false`.\n * @example\n *\n * _.isArrayLikeObject([1, 2, 3]);\n * // => true\n *\n * _.isArrayLikeObject(document.body.children);\n * // => true\n *\n * _.isArrayLikeObject('abc');\n * // => false\n *\n * _.isArrayLikeObject(_.noop);\n * // => false\n */\nfunction isArrayLikeObject(value) {\n  return isObjectLike(value) && isArrayLike(value);\n}\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 8-9 which returns 'object' for typed array and other constructors.\n  var tag = isObject(value) ? objectToString.call(value) : '';\n  return tag == funcTag || tag == genTag;\n}\n\n/**\n * Checks if `value` is a valid array-like length.\n *\n * **Note:** This method is loosely based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.\n * @example\n *\n * _.isLength(3);\n * // => true\n *\n * _.isLength(Number.MIN_VALUE);\n * // => false\n *\n * _.isLength(Infinity);\n * // => false\n *\n * _.isLength('3');\n * // => false\n */\nfunction isLength(value) {\n  return typeof value == 'number' &&\n    value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `String` primitive or object.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a string, else `false`.\n * @example\n *\n * _.isString('abc');\n * // => true\n *\n * _.isString(1);\n * // => false\n */\nfunction isString(value) {\n  return typeof value == 'string' ||\n    (!isArray(value) && isObjectLike(value) && objectToString.call(value) == stringTag);\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Converts `value` to a finite number.\n *\n * @static\n * @memberOf _\n * @since 4.12.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted number.\n * @example\n *\n * _.toFinite(3.2);\n * // => 3.2\n *\n * _.toFinite(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toFinite(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toFinite('3.2');\n * // => 3.2\n */\nfunction toFinite(value) {\n  if (!value) {\n    return value === 0 ? value : 0;\n  }\n  value = toNumber(value);\n  if (value === INFINITY || value === -INFINITY) {\n    var sign = (value < 0 ? -1 : 1);\n    return sign * MAX_INTEGER;\n  }\n  return value === value ? value : 0;\n}\n\n/**\n * Converts `value` to an integer.\n *\n * **Note:** This method is loosely based on\n * [`ToInteger`](http://www.ecma-international.org/ecma-262/7.0/#sec-tointeger).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted integer.\n * @example\n *\n * _.toInteger(3.2);\n * // => 3\n *\n * _.toInteger(Number.MIN_VALUE);\n * // => 0\n *\n * _.toInteger(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toInteger('3.2');\n * // => 3\n */\nfunction toInteger(value) {\n  var result = toFinite(value),\n      remainder = result % 1;\n\n  return result === result ? (remainder ? result - remainder : result) : 0;\n}\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = value.replace(reTrim, '');\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\n/**\n * Creates an array of the own enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects. See the\n * [ES spec](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\n * for more details.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keys(new Foo);\n * // => ['a', 'b'] (iteration order is not guaranteed)\n *\n * _.keys('hi');\n * // => ['0', '1']\n */\nfunction keys(object) {\n  return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);\n}\n\n/**\n * Creates an array of the own enumerable string keyed property values of `object`.\n *\n * **Note:** Non-object values are coerced to objects.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property values.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.values(new Foo);\n * // => [1, 2] (iteration order is not guaranteed)\n *\n * _.values('hi');\n * // => ['h', 'i']\n */\nfunction values(object) {\n  return object ? baseValues(object, keys(object)) : [];\n}\n\nmodule.exports = includes;\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED,uDAAuD,GACvD,IAAI,WAAW,IAAI,GACf,mBAAmB,kBACnB,cAAc,yBACd,MAAM,IAAI;AAEd,yCAAyC,GACzC,IAAI,UAAU,sBACV,UAAU,qBACV,SAAS,8BACT,YAAY,mBACZ,YAAY;AAEhB,mDAAmD,GACnD,IAAI,SAAS;AAEb,yDAAyD,GACzD,IAAI,aAAa;AAEjB,yCAAyC,GACzC,IAAI,aAAa;AAEjB,wCAAwC,GACxC,IAAI,YAAY;AAEhB,4CAA4C,GAC5C,IAAI,WAAW;AAEf,+DAA+D,GAC/D,IAAI,eAAe;AAEnB;;;;;;;;CAQC,GACD,SAAS,SAAS,KAAK,EAAE,QAAQ;IAC/B,IAAI,QAAQ,CAAC,GACT,SAAS,QAAQ,MAAM,MAAM,GAAG,GAChC,SAAS,MAAM;IAEnB,MAAO,EAAE,QAAQ,OAAQ;QACvB,MAAM,CAAC,MAAM,GAAG,SAAS,KAAK,CAAC,MAAM,EAAE,OAAO;IAChD;IACA,OAAO;AACT;AAEA;;;;;;;;;;CAUC,GACD,SAAS,cAAc,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;IAC3D,IAAI,SAAS,MAAM,MAAM,EACrB,QAAQ,YAAY,CAAC,YAAY,IAAI,CAAC,CAAC;IAE3C,MAAQ,YAAY,UAAU,EAAE,QAAQ,OAAS;QAC/C,IAAI,UAAU,KAAK,CAAC,MAAM,EAAE,OAAO,QAAQ;YACzC,OAAO;QACT;IACF;IACA,OAAO,CAAC;AACV;AAEA;;;;;;;;CAQC,GACD,SAAS,YAAY,KAAK,EAAE,KAAK,EAAE,SAAS;IAC1C,IAAI,UAAU,OAAO;QACnB,OAAO,cAAc,OAAO,WAAW;IACzC;IACA,IAAI,QAAQ,YAAY,GACpB,SAAS,MAAM,MAAM;IAEzB,MAAO,EAAE,QAAQ,OAAQ;QACvB,IAAI,KAAK,CAAC,MAAM,KAAK,OAAO;YAC1B,OAAO;QACT;IACF;IACA,OAAO,CAAC;AACV;AAEA;;;;;;CAMC,GACD,SAAS,UAAU,KAAK;IACtB,OAAO,UAAU;AACnB;AAEA;;;;;;;;CAQC,GACD,SAAS,UAAU,CAAC,EAAE,QAAQ;IAC5B,IAAI,QAAQ,CAAC,GACT,SAAS,MAAM;IAEnB,MAAO,EAAE,QAAQ,EAAG;QAClB,MAAM,CAAC,MAAM,GAAG,SAAS;IAC3B;IACA,OAAO;AACT;AAEA;;;;;;;;;CASC,GACD,SAAS,WAAW,MAAM,EAAE,KAAK;IAC/B,OAAO,SAAS,OAAO,SAAS,GAAG;QACjC,OAAO,MAAM,CAAC,IAAI;IACpB;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,IAAI,EAAE,SAAS;IAC9B,OAAO,SAAS,GAAG;QACjB,OAAO,KAAK,UAAU;IACxB;AACF;AAEA,yCAAyC,GACzC,IAAI,cAAc,OAAO,SAAS;AAElC,8CAA8C,GAC9C,IAAI,iBAAiB,YAAY,cAAc;AAE/C;;;;CAIC,GACD,IAAI,iBAAiB,YAAY,QAAQ;AAEzC,+BAA+B,GAC/B,IAAI,uBAAuB,YAAY,oBAAoB;AAE3D,sFAAsF,GACtF,IAAI,aAAa,QAAQ,OAAO,IAAI,EAAE,SAClC,YAAY,KAAK,GAAG;AAExB;;;;;;;CAOC,GACD,SAAS,cAAc,KAAK,EAAE,SAAS;IACrC,iEAAiE;IACjE,+DAA+D;IAC/D,IAAI,SAAS,AAAC,QAAQ,UAAU,YAAY,SACxC,UAAU,MAAM,MAAM,EAAE,UACxB,EAAE;IAEN,IAAI,SAAS,OAAO,MAAM,EACtB,cAAc,CAAC,CAAC;IAEpB,IAAK,IAAI,OAAO,MAAO;QACrB,IAAI,CAAC,aAAa,eAAe,IAAI,CAAC,OAAO,IAAI,KAC7C,CAAC,CAAC,eAAe,CAAC,OAAO,YAAY,QAAQ,KAAK,OAAO,CAAC,GAAG;YAC/D,OAAO,IAAI,CAAC;QACd;IACF;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,SAAS,MAAM;IACtB,IAAI,CAAC,YAAY,SAAS;QACxB,OAAO,WAAW;IACpB;IACA,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,OAAO,OAAO,QAAS;QAC9B,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ,OAAO,eAAe;YAC5D,OAAO,IAAI,CAAC;QACd;IACF;IACA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,KAAK,EAAE,MAAM;IAC5B,SAAS,UAAU,OAAO,mBAAmB;IAC7C,OAAO,CAAC,CAAC,UACP,CAAC,OAAO,SAAS,YAAY,SAAS,IAAI,CAAC,MAAM,KAChD,QAAQ,CAAC,KAAK,QAAQ,KAAK,KAAK,QAAQ;AAC7C;AAEA;;;;;;CAMC,GACD,SAAS,YAAY,KAAK;IACxB,IAAI,OAAO,SAAS,MAAM,WAAW,EACjC,QAAQ,AAAC,OAAO,QAAQ,cAAc,KAAK,SAAS,IAAK;IAE7D,OAAO,UAAU;AACnB;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6BC,GACD,SAAS,SAAS,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK;IACnD,aAAa,YAAY,cAAc,aAAa,OAAO;IAC3D,YAAY,AAAC,aAAa,CAAC,QAAS,UAAU,aAAa;IAE3D,IAAI,SAAS,WAAW,MAAM;IAC9B,IAAI,YAAY,GAAG;QACjB,YAAY,UAAU,SAAS,WAAW;IAC5C;IACA,OAAO,SAAS,cACX,aAAa,UAAU,WAAW,OAAO,CAAC,OAAO,aAAa,CAAC,IAC/D,CAAC,CAAC,UAAU,YAAY,YAAY,OAAO,aAAa,CAAC;AAChE;AAEA;;;;;;;;;;;;;;;;;CAiBC,GACD,SAAS,YAAY,KAAK;IACxB,iEAAiE;IACjE,OAAO,kBAAkB,UAAU,eAAe,IAAI,CAAC,OAAO,aAC5D,CAAC,CAAC,qBAAqB,IAAI,CAAC,OAAO,aAAa,eAAe,IAAI,CAAC,UAAU,OAAO;AACzF;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,IAAI,UAAU,MAAM,OAAO;AAE3B;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,SAAS,YAAY,KAAK;IACxB,OAAO,SAAS,QAAQ,SAAS,MAAM,MAAM,KAAK,CAAC,WAAW;AAChE;AAEA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,SAAS,kBAAkB,KAAK;IAC9B,OAAO,aAAa,UAAU,YAAY;AAC5C;AAEA;;;;;;;;;;;;;;;;CAgBC,GACD,SAAS,WAAW,KAAK;IACvB,wEAAwE;IACxE,+EAA+E;IAC/E,IAAI,MAAM,SAAS,SAAS,eAAe,IAAI,CAAC,SAAS;IACzD,OAAO,OAAO,WAAW,OAAO;AAClC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC,GACD,SAAS,SAAS,KAAK;IACrB,OAAO,OAAO,SAAS,YACrB,QAAQ,CAAC,KAAK,QAAQ,KAAK,KAAK,SAAS;AAC7C;AAEA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,OAAO,OAAO;IAClB,OAAO,CAAC,CAAC,SAAS,CAAC,QAAQ,YAAY,QAAQ,UAAU;AAC3D;AAEA;;;;;;;;;;;;;;;;;;;;;;;CAuBC,GACD,SAAS,aAAa,KAAK;IACzB,OAAO,CAAC,CAAC,SAAS,OAAO,SAAS;AACpC;AAEA;;;;;;;;;;;;;;;;CAgBC,GACD,SAAS,SAAS,KAAK;IACrB,OAAO,OAAO,SAAS,YACpB,CAAC,QAAQ,UAAU,aAAa,UAAU,eAAe,IAAI,CAAC,UAAU;AAC7E;AAEA;;;;;;;;;;;;;;;;CAgBC,GACD,SAAS,SAAS,KAAK;IACrB,OAAO,OAAO,SAAS,YACpB,aAAa,UAAU,eAAe,IAAI,CAAC,UAAU;AAC1D;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,CAAC,OAAO;QACV,OAAO,UAAU,IAAI,QAAQ;IAC/B;IACA,QAAQ,SAAS;IACjB,IAAI,UAAU,YAAY,UAAU,CAAC,UAAU;QAC7C,IAAI,OAAQ,QAAQ,IAAI,CAAC,IAAI;QAC7B,OAAO,OAAO;IAChB;IACA,OAAO,UAAU,QAAQ,QAAQ;AACnC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC,GACD,SAAS,UAAU,KAAK;IACtB,IAAI,SAAS,SAAS,QAClB,YAAY,SAAS;IAEzB,OAAO,WAAW,SAAU,YAAY,SAAS,YAAY,SAAU;AACzE;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO;IACT;IACA,IAAI,SAAS,QAAQ;QACnB,OAAO;IACT;IACA,IAAI,SAAS,QAAQ;QACnB,IAAI,QAAQ,OAAO,MAAM,OAAO,IAAI,aAAa,MAAM,OAAO,KAAK;QACnE,QAAQ,SAAS,SAAU,QAAQ,KAAM;IAC3C;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,UAAU,IAAI,QAAQ,CAAC;IAChC;IACA,QAAQ,MAAM,OAAO,CAAC,QAAQ;IAC9B,IAAI,WAAW,WAAW,IAAI,CAAC;IAC/B,OAAO,AAAC,YAAY,UAAU,IAAI,CAAC,SAC/B,aAAa,MAAM,KAAK,CAAC,IAAI,WAAW,IAAI,KAC3C,WAAW,IAAI,CAAC,SAAS,MAAM,CAAC;AACvC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BC,GACD,SAAS,KAAK,MAAM;IAClB,OAAO,YAAY,UAAU,cAAc,UAAU,SAAS;AAChE;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC,GACD,SAAS,OAAO,MAAM;IACpB,OAAO,SAAS,WAAW,QAAQ,KAAK,WAAW,EAAE;AACvD;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/lodash.isboolean/index.js"], "sourcesContent": ["/**\n * lodash 3.0.3 (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright 2012-2016 The Dojo Foundation <http://dojofoundation.org/>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright 2009-2016 <PERSON>, DocumentCloud and Investigative Reporters & Editors\n * Available under MIT license <https://lodash.com/license>\n */\n\n/** `Object#toString` result references. */\nvar boolTag = '[object Boolean]';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the [`toStringTag`](http://ecma-international.org/ecma-262/6.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/**\n * Checks if `value` is classified as a boolean primitive or object.\n *\n * @static\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is correctly classified, else `false`.\n * @example\n *\n * _.isBoolean(false);\n * // => true\n *\n * _.isBoolean(null);\n * // => false\n */\nfunction isBoolean(value) {\n  return value === true || value === false ||\n    (isObjectLike(value) && objectToString.call(value) == boolTag);\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\nmodule.exports = isBoolean;\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED,yCAAyC,GACzC,IAAI,UAAU;AAEd,yCAAyC,GACzC,IAAI,cAAc,OAAO,SAAS;AAElC;;;CAGC,GACD,IAAI,iBAAiB,YAAY,QAAQ;AAEzC;;;;;;;;;;;;;;;CAeC,GACD,SAAS,UAAU,KAAK;IACtB,OAAO,UAAU,QAAQ,UAAU,SAChC,aAAa,UAAU,eAAe,IAAI,CAAC,UAAU;AAC1D;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,aAAa,KAAK;IACzB,OAAO,CAAC,CAAC,SAAS,OAAO,SAAS;AACpC;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/lodash.isinteger/index.js"], "sourcesContent": ["/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0,\n    MAX_INTEGER = 1.7976931348623157e+308,\n    NAN = 0 / 0;\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/** Used to match leading and trailing whitespace. */\nvar reTrim = /^\\s+|\\s+$/g;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/**\n * Checks if `value` is an integer.\n *\n * **Note:** This method is based on\n * [`Number.isInteger`](https://mdn.io/Number/isInteger).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an integer, else `false`.\n * @example\n *\n * _.isInteger(3);\n * // => true\n *\n * _.isInteger(Number.MIN_VALUE);\n * // => false\n *\n * _.isInteger(Infinity);\n * // => false\n *\n * _.isInteger('3');\n * // => false\n */\nfunction isInteger(value) {\n  return typeof value == 'number' && value == toInteger(value);\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Converts `value` to a finite number.\n *\n * @static\n * @memberOf _\n * @since 4.12.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted number.\n * @example\n *\n * _.toFinite(3.2);\n * // => 3.2\n *\n * _.toFinite(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toFinite(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toFinite('3.2');\n * // => 3.2\n */\nfunction toFinite(value) {\n  if (!value) {\n    return value === 0 ? value : 0;\n  }\n  value = toNumber(value);\n  if (value === INFINITY || value === -INFINITY) {\n    var sign = (value < 0 ? -1 : 1);\n    return sign * MAX_INTEGER;\n  }\n  return value === value ? value : 0;\n}\n\n/**\n * Converts `value` to an integer.\n *\n * **Note:** This method is loosely based on\n * [`ToInteger`](http://www.ecma-international.org/ecma-262/7.0/#sec-tointeger).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted integer.\n * @example\n *\n * _.toInteger(3.2);\n * // => 3\n *\n * _.toInteger(Number.MIN_VALUE);\n * // => 0\n *\n * _.toInteger(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toInteger('3.2');\n * // => 3\n */\nfunction toInteger(value) {\n  var result = toFinite(value),\n      remainder = result % 1;\n\n  return result === result ? (remainder ? result - remainder : result) : 0;\n}\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = value.replace(reTrim, '');\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nmodule.exports = isInteger;\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED,uDAAuD,GACvD,IAAI,WAAW,IAAI,GACf,cAAc,yBACd,MAAM,IAAI;AAEd,yCAAyC,GACzC,IAAI,YAAY;AAEhB,mDAAmD,GACnD,IAAI,SAAS;AAEb,yDAAyD,GACzD,IAAI,aAAa;AAEjB,yCAAyC,GACzC,IAAI,aAAa;AAEjB,wCAAwC,GACxC,IAAI,YAAY;AAEhB,+DAA+D,GAC/D,IAAI,eAAe;AAEnB,yCAAyC,GACzC,IAAI,cAAc,OAAO,SAAS;AAElC;;;;CAIC,GACD,IAAI,iBAAiB,YAAY,QAAQ;AAEzC;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC,GACD,SAAS,UAAU,KAAK;IACtB,OAAO,OAAO,SAAS,YAAY,SAAS,UAAU;AACxD;AAEA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,OAAO,OAAO;IAClB,OAAO,CAAC,CAAC,SAAS,CAAC,QAAQ,YAAY,QAAQ,UAAU;AAC3D;AAEA;;;;;;;;;;;;;;;;;;;;;;;CAuBC,GACD,SAAS,aAAa,KAAK;IACzB,OAAO,CAAC,CAAC,SAAS,OAAO,SAAS;AACpC;AAEA;;;;;;;;;;;;;;;;CAgBC,GACD,SAAS,SAAS,KAAK;IACrB,OAAO,OAAO,SAAS,YACpB,aAAa,UAAU,eAAe,IAAI,CAAC,UAAU;AAC1D;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,CAAC,OAAO;QACV,OAAO,UAAU,IAAI,QAAQ;IAC/B;IACA,QAAQ,SAAS;IACjB,IAAI,UAAU,YAAY,UAAU,CAAC,UAAU;QAC7C,IAAI,OAAQ,QAAQ,IAAI,CAAC,IAAI;QAC7B,OAAO,OAAO;IAChB;IACA,OAAO,UAAU,QAAQ,QAAQ;AACnC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC,GACD,SAAS,UAAU,KAAK;IACtB,IAAI,SAAS,SAAS,QAClB,YAAY,SAAS;IAEzB,OAAO,WAAW,SAAU,YAAY,SAAS,YAAY,SAAU;AACzE;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO;IACT;IACA,IAAI,SAAS,QAAQ;QACnB,OAAO;IACT;IACA,IAAI,SAAS,QAAQ;QACnB,IAAI,QAAQ,OAAO,MAAM,OAAO,IAAI,aAAa,MAAM,OAAO,KAAK;QACnE,QAAQ,SAAS,SAAU,QAAQ,KAAM;IAC3C;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,UAAU,IAAI,QAAQ,CAAC;IAChC;IACA,QAAQ,MAAM,OAAO,CAAC,QAAQ;IAC9B,IAAI,WAAW,WAAW,IAAI,CAAC;IAC/B,OAAO,AAAC,YAAY,UAAU,IAAI,CAAC,SAC/B,aAAa,MAAM,KAAK,CAAC,IAAI,WAAW,IAAI,KAC3C,WAAW,IAAI,CAAC,SAAS,MAAM,CAAC;AACvC;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4706, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/lodash.isnumber/index.js"], "sourcesContent": ["/**\n * lodash 3.0.3 (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright 2012-2016 The Dojo Foundation <http://dojofoundation.org/>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright 2009-2016 <PERSON>, DocumentCloud and Investigative Reporters & Editors\n * Available under MIT license <https://lodash.com/license>\n */\n\n/** `Object#toString` result references. */\nvar numberTag = '[object Number]';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the [`toStringTag`](http://ecma-international.org/ecma-262/6.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Number` primitive or object.\n *\n * **Note:** To exclude `Infinity`, `-Infinity`, and `NaN`, which are classified\n * as numbers, use the `_.isFinite` method.\n *\n * @static\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is correctly classified, else `false`.\n * @example\n *\n * _.isNumber(3);\n * // => true\n *\n * _.isNumber(Number.MIN_VALUE);\n * // => true\n *\n * _.isNumber(Infinity);\n * // => true\n *\n * _.isNumber('3');\n * // => false\n */\nfunction isNumber(value) {\n  return typeof value == 'number' ||\n    (isObjectLike(value) && objectToString.call(value) == numberTag);\n}\n\nmodule.exports = isNumber;\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED,yCAAyC,GACzC,IAAI,YAAY;AAEhB,yCAAyC,GACzC,IAAI,cAAc,OAAO,SAAS;AAElC;;;CAGC,GACD,IAAI,iBAAiB,YAAY,QAAQ;AAEzC;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,aAAa,KAAK;IACzB,OAAO,CAAC,CAAC,SAAS,OAAO,SAAS;AACpC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,SAAS,SAAS,KAAK;IACrB,OAAO,OAAO,SAAS,YACpB,aAAa,UAAU,eAAe,IAAI,CAAC,UAAU;AAC1D;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4777, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/lodash.isplainobject/index.js"], "sourcesContent": ["/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** `Object#toString` result references. */\nvar objectTag = '[object Object]';\n\n/**\n * Checks if `value` is a host object in IE < 9.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a host object, else `false`.\n */\nfunction isHostObject(value) {\n  // Many host objects are `Object` objects that can coerce to strings\n  // despite having improperly defined `toString` methods.\n  var result = false;\n  if (value != null && typeof value.toString != 'function') {\n    try {\n      result = !!(value + '');\n    } catch (e) {}\n  }\n  return result;\n}\n\n/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func, transform) {\n  return function(arg) {\n    return func(transform(arg));\n  };\n}\n\n/** Used for built-in method references. */\nvar funcProto = Function.prototype,\n    objectProto = Object.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Used to infer the `Object` constructor. */\nvar objectCtorString = funcToString.call(Object);\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar getPrototype = overArg(Object.getPrototypeOf, Object);\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is a plain object, that is, an object created by the\n * `Object` constructor or one with a `[[Prototype]]` of `null`.\n *\n * @static\n * @memberOf _\n * @since 0.8.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a plain object, else `false`.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n * }\n *\n * _.isPlainObject(new Foo);\n * // => false\n *\n * _.isPlainObject([1, 2, 3]);\n * // => false\n *\n * _.isPlainObject({ 'x': 0, 'y': 0 });\n * // => true\n *\n * _.isPlainObject(Object.create(null));\n * // => true\n */\nfunction isPlainObject(value) {\n  if (!isObjectLike(value) ||\n      objectToString.call(value) != objectTag || isHostObject(value)) {\n    return false;\n  }\n  var proto = getPrototype(value);\n  if (proto === null) {\n    return true;\n  }\n  var Ctor = hasOwnProperty.call(proto, 'constructor') && proto.constructor;\n  return (typeof Ctor == 'function' &&\n    Ctor instanceof Ctor && funcToString.call(Ctor) == objectCtorString);\n}\n\nmodule.exports = isPlainObject;\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED,yCAAyC,GACzC,IAAI,YAAY;AAEhB;;;;;;CAMC,GACD,SAAS,aAAa,KAAK;IACzB,oEAAoE;IACpE,wDAAwD;IACxD,IAAI,SAAS;IACb,IAAI,SAAS,QAAQ,OAAO,MAAM,QAAQ,IAAI,YAAY;QACxD,IAAI;YACF,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE;QACxB,EAAE,OAAO,GAAG,CAAC;IACf;IACA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,IAAI,EAAE,SAAS;IAC9B,OAAO,SAAS,GAAG;QACjB,OAAO,KAAK,UAAU;IACxB;AACF;AAEA,yCAAyC,GACzC,IAAI,YAAY,SAAS,SAAS,EAC9B,cAAc,OAAO,SAAS;AAElC,wDAAwD,GACxD,IAAI,eAAe,UAAU,QAAQ;AAErC,8CAA8C,GAC9C,IAAI,iBAAiB,YAAY,cAAc;AAE/C,4CAA4C,GAC5C,IAAI,mBAAmB,aAAa,IAAI,CAAC;AAEzC;;;;CAIC,GACD,IAAI,iBAAiB,YAAY,QAAQ;AAEzC,+BAA+B,GAC/B,IAAI,eAAe,QAAQ,OAAO,cAAc,EAAE;AAElD;;;;;;;;;;;;;;;;;;;;;;;CAuBC,GACD,SAAS,aAAa,KAAK;IACzB,OAAO,CAAC,CAAC,SAAS,OAAO,SAAS;AACpC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BC,GACD,SAAS,cAAc,KAAK;IAC1B,IAAI,CAAC,aAAa,UACd,eAAe,IAAI,CAAC,UAAU,aAAa,aAAa,QAAQ;QAClE,OAAO;IACT;IACA,IAAI,QAAQ,aAAa;IACzB,IAAI,UAAU,MAAM;QAClB,OAAO;IACT;IACA,IAAI,OAAO,eAAe,IAAI,CAAC,OAAO,kBAAkB,MAAM,WAAW;IACzE,OAAQ,OAAO,QAAQ,cACrB,gBAAgB,QAAQ,aAAa,IAAI,CAAC,SAAS;AACvD;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4894, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/lodash.isstring/index.js"], "sourcesContent": ["/**\n * lodash 4.0.1 (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright 2012-2016 The Dojo Foundation <http://dojofoundation.org/>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright 2009-2016 <PERSON>, DocumentCloud and Investigative Reporters & Editors\n * Available under MIT license <https://lodash.com/license>\n */\n\n/** `Object#toString` result references. */\nvar stringTag = '[object String]';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the [`toStringTag`](http://ecma-international.org/ecma-262/6.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @type Function\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is correctly classified, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */\nvar isArray = Array.isArray;\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `String` primitive or object.\n *\n * @static\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is correctly classified, else `false`.\n * @example\n *\n * _.isString('abc');\n * // => true\n *\n * _.isString(1);\n * // => false\n */\nfunction isString(value) {\n  return typeof value == 'string' ||\n    (!isArray(value) && isObjectLike(value) && objectToString.call(value) == stringTag);\n}\n\nmodule.exports = isString;\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED,yCAAyC,GACzC,IAAI,YAAY;AAEhB,yCAAyC,GACzC,IAAI,cAAc,OAAO,SAAS;AAElC;;;CAGC,GACD,IAAI,iBAAiB,YAAY,QAAQ;AAEzC;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,IAAI,UAAU,MAAM,OAAO;AAE3B;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,aAAa,KAAK;IACzB,OAAO,CAAC,CAAC,SAAS,OAAO,SAAS;AACpC;AAEA;;;;;;;;;;;;;;;CAeC,GACD,SAAS,SAAS,KAAK;IACrB,OAAO,OAAO,SAAS,YACpB,CAAC,QAAQ,UAAU,aAAa,UAAU,eAAe,IAAI,CAAC,UAAU;AAC7E;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4979, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/lodash.once/index.js"], "sourcesContent": ["/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as the `TypeError` message for \"Functions\" methods. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0,\n    MAX_INTEGER = 1.7976931348623157e+308,\n    NAN = 0 / 0;\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/** Used to match leading and trailing whitespace. */\nvar reTrim = /^\\s+|\\s+$/g;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/**\n * Creates a function that invokes `func`, with the `this` binding and arguments\n * of the created function, while it's called less than `n` times. Subsequent\n * calls to the created function return the result of the last `func` invocation.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {number} n The number of calls at which `func` is no longer invoked.\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new restricted function.\n * @example\n *\n * jQuery(element).on('click', _.before(5, addContactToList));\n * // => Allows adding up to 4 contacts to the list.\n */\nfunction before(n, func) {\n  var result;\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  n = toInteger(n);\n  return function() {\n    if (--n > 0) {\n      result = func.apply(this, arguments);\n    }\n    if (n <= 1) {\n      func = undefined;\n    }\n    return result;\n  };\n}\n\n/**\n * Creates a function that is restricted to invoking `func` once. Repeat calls\n * to the function return the value of the first invocation. The `func` is\n * invoked with the `this` binding and arguments of the created function.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new restricted function.\n * @example\n *\n * var initialize = _.once(createApplication);\n * initialize();\n * initialize();\n * // => `createApplication` is invoked once\n */\nfunction once(func) {\n  return before(2, func);\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Converts `value` to a finite number.\n *\n * @static\n * @memberOf _\n * @since 4.12.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted number.\n * @example\n *\n * _.toFinite(3.2);\n * // => 3.2\n *\n * _.toFinite(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toFinite(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toFinite('3.2');\n * // => 3.2\n */\nfunction toFinite(value) {\n  if (!value) {\n    return value === 0 ? value : 0;\n  }\n  value = toNumber(value);\n  if (value === INFINITY || value === -INFINITY) {\n    var sign = (value < 0 ? -1 : 1);\n    return sign * MAX_INTEGER;\n  }\n  return value === value ? value : 0;\n}\n\n/**\n * Converts `value` to an integer.\n *\n * **Note:** This method is loosely based on\n * [`ToInteger`](http://www.ecma-international.org/ecma-262/7.0/#sec-tointeger).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted integer.\n * @example\n *\n * _.toInteger(3.2);\n * // => 3\n *\n * _.toInteger(Number.MIN_VALUE);\n * // => 0\n *\n * _.toInteger(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toInteger('3.2');\n * // => 3\n */\nfunction toInteger(value) {\n  var result = toFinite(value),\n      remainder = result % 1;\n\n  return result === result ? (remainder ? result - remainder : result) : 0;\n}\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = value.replace(reTrim, '');\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nmodule.exports = once;\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED,6DAA6D,GAC7D,IAAI,kBAAkB;AAEtB,uDAAuD,GACvD,IAAI,WAAW,IAAI,GACf,cAAc,yBACd,MAAM,IAAI;AAEd,yCAAyC,GACzC,IAAI,YAAY;AAEhB,mDAAmD,GACnD,IAAI,SAAS;AAEb,yDAAyD,GACzD,IAAI,aAAa;AAEjB,yCAAyC,GACzC,IAAI,aAAa;AAEjB,wCAAwC,GACxC,IAAI,YAAY;AAEhB,+DAA+D,GAC/D,IAAI,eAAe;AAEnB,yCAAyC,GACzC,IAAI,cAAc,OAAO,SAAS;AAElC;;;;CAIC,GACD,IAAI,iBAAiB,YAAY,QAAQ;AAEzC;;;;;;;;;;;;;;;;CAgBC,GACD,SAAS,OAAO,CAAC,EAAE,IAAI;IACrB,IAAI;IACJ,IAAI,OAAO,QAAQ,YAAY;QAC7B,MAAM,IAAI,UAAU;IACtB;IACA,IAAI,UAAU;IACd,OAAO;QACL,IAAI,EAAE,IAAI,GAAG;YACX,SAAS,KAAK,KAAK,CAAC,IAAI,EAAE;QAC5B;QACA,IAAI,KAAK,GAAG;YACV,OAAO;QACT;QACA,OAAO;IACT;AACF;AAEA;;;;;;;;;;;;;;;;;CAiBC,GACD,SAAS,KAAK,IAAI;IAChB,OAAO,OAAO,GAAG;AACnB;AAEA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,OAAO,OAAO;IAClB,OAAO,CAAC,CAAC,SAAS,CAAC,QAAQ,YAAY,QAAQ,UAAU;AAC3D;AAEA;;;;;;;;;;;;;;;;;;;;;;;CAuBC,GACD,SAAS,aAAa,KAAK;IACzB,OAAO,CAAC,CAAC,SAAS,OAAO,SAAS;AACpC;AAEA;;;;;;;;;;;;;;;;CAgBC,GACD,SAAS,SAAS,KAAK;IACrB,OAAO,OAAO,SAAS,YACpB,aAAa,UAAU,eAAe,IAAI,CAAC,UAAU;AAC1D;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,CAAC,OAAO;QACV,OAAO,UAAU,IAAI,QAAQ;IAC/B;IACA,QAAQ,SAAS;IACjB,IAAI,UAAU,YAAY,UAAU,CAAC,UAAU;QAC7C,IAAI,OAAQ,QAAQ,IAAI,CAAC,IAAI;QAC7B,OAAO,OAAO;IAChB;IACA,OAAO,UAAU,QAAQ,QAAQ;AACnC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC,GACD,SAAS,UAAU,KAAK;IACtB,IAAI,SAAS,SAAS,QAClB,YAAY,SAAS;IAEzB,OAAO,WAAW,SAAU,YAAY,SAAS,YAAY,SAAU;AACzE;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO;IACT;IACA,IAAI,SAAS,QAAQ;QACnB,OAAO;IACT;IACA,IAAI,SAAS,QAAQ;QACnB,IAAI,QAAQ,OAAO,MAAM,OAAO,IAAI,aAAa,MAAM,OAAO,KAAK;QACnE,QAAQ,SAAS,SAAU,QAAQ,KAAM;IAC3C;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,UAAU,IAAI,QAAQ,CAAC;IAChC;IACA,QAAQ,MAAM,OAAO,CAAC,QAAQ;IAC9B,IAAI,WAAW,WAAW,IAAI,CAAC;IAC/B,OAAO,AAAC,YAAY,UAAU,IAAI,CAAC,SAC/B,aAAa,MAAM,KAAK,CAAC,IAAI,WAAW,IAAI,KAC3C,WAAW,IAAI,CAAC,SAAS,MAAM,CAAC;AACvC;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/node_modules/bcryptjs/index.js"], "sourcesContent": ["/*\n Copyright (c) 2012 N<PERSON><PERSON> <nevins.bartolo<PERSON><EMAIL>>\n Copyright (c) 2012 <PERSON> <<EMAIL>>\n Copyright (c) 2025 <PERSON>z <<EMAIL>>\n\n Redistribution and use in source and binary forms, with or without\n modification, are permitted provided that the following conditions\n are met:\n 1. Redistributions of source code must retain the above copyright\n notice, this list of conditions and the following disclaimer.\n 2. Redistributions in binary form must reproduce the above copyright\n notice, this list of conditions and the following disclaimer in the\n documentation and/or other materials provided with the distribution.\n 3. The name of the author may not be used to endorse or promote products\n derived from this software without specific prior written permission.\n\n THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\n IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\n OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\n INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n// The Node.js crypto module is used as a fallback for the Web Crypto API. When\n// building for the browser, inclusion of the crypto module should be disabled,\n// which the package hints at in its package.json for bundlers that support it.\nimport nodeCrypto from \"crypto\";\n\n/**\n * The random implementation to use as a fallback.\n * @type {?function(number):!Array.<number>}\n * @inner\n */\nvar randomFallback = null;\n\n/**\n * Generates cryptographically secure random bytes.\n * @function\n * @param {number} len Bytes length\n * @returns {!Array.<number>} Random bytes\n * @throws {Error} If no random implementation is available\n * @inner\n */\nfunction randomBytes(len) {\n  // Web Crypto API. Globally available in the browser and in Node.js >=23.\n  try {\n    return crypto.getRandomValues(new Uint8Array(len));\n  } catch {}\n  // Node.js crypto module for non-browser environments.\n  try {\n    return nodeCrypto.randomBytes(len);\n  } catch {}\n  // Custom fallback specified with `setRandomFallback`.\n  if (!randomFallback) {\n    throw Error(\n      \"Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative\",\n    );\n  }\n  return randomFallback(len);\n}\n\n/**\n * Sets the pseudo random number generator to use as a fallback if neither node's `crypto` module nor the Web Crypto\n *  API is available. Please note: It is highly important that the PRNG used is cryptographically secure and that it\n *  is seeded properly!\n * @param {?function(number):!Array.<number>} random Function taking the number of bytes to generate as its\n *  sole argument, returning the corresponding array of cryptographically secure random byte values.\n * @see http://nodejs.org/api/crypto.html\n * @see http://www.w3.org/TR/WebCryptoAPI/\n */\nexport function setRandomFallback(random) {\n  randomFallback = random;\n}\n\n/**\n * Synchronously generates a salt.\n * @param {number=} rounds Number of rounds to use, defaults to 10 if omitted\n * @param {number=} seed_length Not supported.\n * @returns {string} Resulting salt\n * @throws {Error} If a random fallback is required but not set\n */\nexport function genSaltSync(rounds, seed_length) {\n  rounds = rounds || GENSALT_DEFAULT_LOG2_ROUNDS;\n  if (typeof rounds !== \"number\")\n    throw Error(\n      \"Illegal arguments: \" + typeof rounds + \", \" + typeof seed_length,\n    );\n  if (rounds < 4) rounds = 4;\n  else if (rounds > 31) rounds = 31;\n  var salt = [];\n  salt.push(\"$2b$\");\n  if (rounds < 10) salt.push(\"0\");\n  salt.push(rounds.toString());\n  salt.push(\"$\");\n  salt.push(base64_encode(randomBytes(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN)); // May throw\n  return salt.join(\"\");\n}\n\n/**\n * Asynchronously generates a salt.\n * @param {(number|function(Error, string=))=} rounds Number of rounds to use, defaults to 10 if omitted\n * @param {(number|function(Error, string=))=} seed_length Not supported.\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting salt\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nexport function genSalt(rounds, seed_length, callback) {\n  if (typeof seed_length === \"function\")\n    (callback = seed_length), (seed_length = undefined); // Not supported.\n  if (typeof rounds === \"function\") (callback = rounds), (rounds = undefined);\n  if (typeof rounds === \"undefined\") rounds = GENSALT_DEFAULT_LOG2_ROUNDS;\n  else if (typeof rounds !== \"number\")\n    throw Error(\"illegal arguments: \" + typeof rounds);\n\n  function _async(callback) {\n    nextTick(function () {\n      // Pretty thin, but salting is fast enough\n      try {\n        callback(null, genSaltSync(rounds));\n      } catch (err) {\n        callback(err);\n      }\n    });\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Synchronously generates a hash for the given password.\n * @param {string} password Password to hash\n * @param {(number|string)=} salt Salt length to generate or salt to use, default to 10\n * @returns {string} Resulting hash\n */\nexport function hashSync(password, salt) {\n  if (typeof salt === \"undefined\") salt = GENSALT_DEFAULT_LOG2_ROUNDS;\n  if (typeof salt === \"number\") salt = genSaltSync(salt);\n  if (typeof password !== \"string\" || typeof salt !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password + \", \" + typeof salt);\n  return _hash(password, salt);\n}\n\n/**\n * Asynchronously generates a hash for the given password.\n * @param {string} password Password to hash\n * @param {number|string} salt Salt length to generate or salt to use\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash\n * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\n *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nexport function hash(password, salt, callback, progressCallback) {\n  function _async(callback) {\n    if (typeof password === \"string\" && typeof salt === \"number\")\n      genSalt(salt, function (err, salt) {\n        _hash(password, salt, callback, progressCallback);\n      });\n    else if (typeof password === \"string\" && typeof salt === \"string\")\n      _hash(password, salt, callback, progressCallback);\n    else\n      nextTick(\n        callback.bind(\n          this,\n          Error(\"Illegal arguments: \" + typeof password + \", \" + typeof salt),\n        ),\n      );\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Compares two strings of the same length in constant time.\n * @param {string} known Must be of the correct length\n * @param {string} unknown Must be the same length as `known`\n * @returns {boolean}\n * @inner\n */\nfunction safeStringCompare(known, unknown) {\n  var diff = known.length ^ unknown.length;\n  for (var i = 0; i < known.length; ++i) {\n    diff |= known.charCodeAt(i) ^ unknown.charCodeAt(i);\n  }\n  return diff === 0;\n}\n\n/**\n * Synchronously tests a password against a hash.\n * @param {string} password Password to compare\n * @param {string} hash Hash to test against\n * @returns {boolean} true if matching, otherwise false\n * @throws {Error} If an argument is illegal\n */\nexport function compareSync(password, hash) {\n  if (typeof password !== \"string\" || typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password + \", \" + typeof hash);\n  if (hash.length !== 60) return false;\n  return safeStringCompare(\n    hashSync(password, hash.substring(0, hash.length - 31)),\n    hash,\n  );\n}\n\n/**\n * Asynchronously tests a password against a hash.\n * @param {string} password Password to compare\n * @param {string} hashValue Hash to test against\n * @param {function(Error, boolean)=} callback Callback receiving the error, if any, otherwise the result\n * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\n *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nexport function compare(password, hashValue, callback, progressCallback) {\n  function _async(callback) {\n    if (typeof password !== \"string\" || typeof hashValue !== \"string\") {\n      nextTick(\n        callback.bind(\n          this,\n          Error(\n            \"Illegal arguments: \" + typeof password + \", \" + typeof hashValue,\n          ),\n        ),\n      );\n      return;\n    }\n    if (hashValue.length !== 60) {\n      nextTick(callback.bind(this, null, false));\n      return;\n    }\n    hash(\n      password,\n      hashValue.substring(0, 29),\n      function (err, comp) {\n        if (err) callback(err);\n        else callback(null, safeStringCompare(comp, hashValue));\n      },\n      progressCallback,\n    );\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Gets the number of rounds used to encrypt the specified hash.\n * @param {string} hash Hash to extract the used number of rounds from\n * @returns {number} Number of rounds used\n * @throws {Error} If `hash` is not a string\n */\nexport function getRounds(hash) {\n  if (typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof hash);\n  return parseInt(hash.split(\"$\")[2], 10);\n}\n\n/**\n * Gets the salt portion from a hash. Does not validate the hash.\n * @param {string} hash Hash to extract the salt from\n * @returns {string} Extracted salt part\n * @throws {Error} If `hash` is not a string or otherwise invalid\n */\nexport function getSalt(hash) {\n  if (typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof hash);\n  if (hash.length !== 60)\n    throw Error(\"Illegal hash length: \" + hash.length + \" != 60\");\n  return hash.substring(0, 29);\n}\n\n/**\n * Tests if a password will be truncated when hashed, that is its length is\n * greater than 72 bytes when converted to UTF-8.\n * @param {string} password The password to test\n * @returns {boolean} `true` if truncated, otherwise `false`\n */\nexport function truncates(password) {\n  if (typeof password !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password);\n  return utf8Length(password) > 72;\n}\n\n/**\n * Continues with the callback on the next tick.\n * @function\n * @param {function(...[*])} callback Callback to execute\n * @inner\n */\nvar nextTick =\n  typeof process !== \"undefined\" &&\n  process &&\n  typeof process.nextTick === \"function\"\n    ? typeof setImmediate === \"function\"\n      ? setImmediate\n      : process.nextTick\n    : setTimeout;\n\n/** Calculates the byte length of a string encoded as UTF8. */\nfunction utf8Length(string) {\n  var len = 0,\n    c = 0;\n  for (var i = 0; i < string.length; ++i) {\n    c = string.charCodeAt(i);\n    if (c < 128) len += 1;\n    else if (c < 2048) len += 2;\n    else if (\n      (c & 0xfc00) === 0xd800 &&\n      (string.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      ++i;\n      len += 4;\n    } else len += 3;\n  }\n  return len;\n}\n\n/** Converts a string to an array of UTF8 bytes. */\nfunction utf8Array(string) {\n  var offset = 0,\n    c1,\n    c2;\n  var buffer = new Array(utf8Length(string));\n  for (var i = 0, k = string.length; i < k; ++i) {\n    c1 = string.charCodeAt(i);\n    if (c1 < 128) {\n      buffer[offset++] = c1;\n    } else if (c1 < 2048) {\n      buffer[offset++] = (c1 >> 6) | 192;\n      buffer[offset++] = (c1 & 63) | 128;\n    } else if (\n      (c1 & 0xfc00) === 0xd800 &&\n      ((c2 = string.charCodeAt(i + 1)) & 0xfc00) === 0xdc00\n    ) {\n      c1 = 0x10000 + ((c1 & 0x03ff) << 10) + (c2 & 0x03ff);\n      ++i;\n      buffer[offset++] = (c1 >> 18) | 240;\n      buffer[offset++] = ((c1 >> 12) & 63) | 128;\n      buffer[offset++] = ((c1 >> 6) & 63) | 128;\n      buffer[offset++] = (c1 & 63) | 128;\n    } else {\n      buffer[offset++] = (c1 >> 12) | 224;\n      buffer[offset++] = ((c1 >> 6) & 63) | 128;\n      buffer[offset++] = (c1 & 63) | 128;\n    }\n  }\n  return buffer;\n}\n\n// A base64 implementation for the bcrypt algorithm. This is partly non-standard.\n\n/**\n * bcrypt's own non-standard base64 dictionary.\n * @type {!Array.<string>}\n * @const\n * @inner\n **/\nvar BASE64_CODE =\n  \"./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split(\"\");\n\n/**\n * @type {!Array.<number>}\n * @const\n * @inner\n **/\nvar BASE64_INDEX = [\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63,\n  -1, -1, -1, -1, -1, -1, -1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,\n  16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, -1, -1, -1, -1, -1, -1, 28,\n  29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47,\n  48, 49, 50, 51, 52, 53, -1, -1, -1, -1, -1,\n];\n\n/**\n * Encodes a byte array to base64 with up to len bytes of input.\n * @param {!Array.<number>} b Byte array\n * @param {number} len Maximum input length\n * @returns {string}\n * @inner\n */\nfunction base64_encode(b, len) {\n  var off = 0,\n    rs = [],\n    c1,\n    c2;\n  if (len <= 0 || len > b.length) throw Error(\"Illegal len: \" + len);\n  while (off < len) {\n    c1 = b[off++] & 0xff;\n    rs.push(BASE64_CODE[(c1 >> 2) & 0x3f]);\n    c1 = (c1 & 0x03) << 4;\n    if (off >= len) {\n      rs.push(BASE64_CODE[c1 & 0x3f]);\n      break;\n    }\n    c2 = b[off++] & 0xff;\n    c1 |= (c2 >> 4) & 0x0f;\n    rs.push(BASE64_CODE[c1 & 0x3f]);\n    c1 = (c2 & 0x0f) << 2;\n    if (off >= len) {\n      rs.push(BASE64_CODE[c1 & 0x3f]);\n      break;\n    }\n    c2 = b[off++] & 0xff;\n    c1 |= (c2 >> 6) & 0x03;\n    rs.push(BASE64_CODE[c1 & 0x3f]);\n    rs.push(BASE64_CODE[c2 & 0x3f]);\n  }\n  return rs.join(\"\");\n}\n\n/**\n * Decodes a base64 encoded string to up to len bytes of output.\n * @param {string} s String to decode\n * @param {number} len Maximum output length\n * @returns {!Array.<number>}\n * @inner\n */\nfunction base64_decode(s, len) {\n  var off = 0,\n    slen = s.length,\n    olen = 0,\n    rs = [],\n    c1,\n    c2,\n    c3,\n    c4,\n    o,\n    code;\n  if (len <= 0) throw Error(\"Illegal len: \" + len);\n  while (off < slen - 1 && olen < len) {\n    code = s.charCodeAt(off++);\n    c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    code = s.charCodeAt(off++);\n    c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    if (c1 == -1 || c2 == -1) break;\n    o = (c1 << 2) >>> 0;\n    o |= (c2 & 0x30) >> 4;\n    rs.push(String.fromCharCode(o));\n    if (++olen >= len || off >= slen) break;\n    code = s.charCodeAt(off++);\n    c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    if (c3 == -1) break;\n    o = ((c2 & 0x0f) << 4) >>> 0;\n    o |= (c3 & 0x3c) >> 2;\n    rs.push(String.fromCharCode(o));\n    if (++olen >= len || off >= slen) break;\n    code = s.charCodeAt(off++);\n    c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    o = ((c3 & 0x03) << 6) >>> 0;\n    o |= c4;\n    rs.push(String.fromCharCode(o));\n    ++olen;\n  }\n  var res = [];\n  for (off = 0; off < olen; off++) res.push(rs[off].charCodeAt(0));\n  return res;\n}\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar BCRYPT_SALT_LEN = 16;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar GENSALT_DEFAULT_LOG2_ROUNDS = 10;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar BLOWFISH_NUM_ROUNDS = 16;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar MAX_EXECUTION_TIME = 100;\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar P_ORIG = [\n  0x243f6a88, 0x85a308d3, 0x13198a2e, 0x03707344, 0xa4093822, 0x299f31d0,\n  0x082efa98, 0xec4e6c89, 0x452821e6, 0x38d01377, 0xbe5466cf, 0x34e90c6c,\n  0xc0ac29b7, 0xc97c50dd, 0x3f84d5b5, 0xb5470917, 0x9216d5d9, 0x8979fb1b,\n];\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar S_ORIG = [\n  0xd1310ba6, 0x98dfb5ac, 0x2ffd72db, 0xd01adfb7, 0xb8e1afed, 0x6a267e96,\n  0xba7c9045, 0xf12c7f99, 0x24a19947, 0xb3916cf7, 0x0801f2e2, 0x858efc16,\n  0x636920d8, 0x71574e69, 0xa458fea3, 0xf4933d7e, 0x0d95748f, 0x728eb658,\n  0x718bcd58, 0x82154aee, 0x7b54a41d, 0xc25a59b5, 0x9c30d539, 0x2af26013,\n  0xc5d1b023, 0x286085f0, 0xca417918, 0xb8db38ef, 0x8e79dcb0, 0x603a180e,\n  0x6c9e0e8b, 0xb01e8a3e, 0xd71577c1, 0xbd314b27, 0x78af2fda, 0x55605c60,\n  0xe65525f3, 0xaa55ab94, 0x57489862, 0x63e81440, 0x55ca396a, 0x2aab10b6,\n  0xb4cc5c34, 0x1141e8ce, 0xa15486af, 0x7c72e993, 0xb3ee1411, 0x636fbc2a,\n  0x2ba9c55d, 0x741831f6, 0xce5c3e16, 0x9b87931e, 0xafd6ba33, 0x6c24cf5c,\n  0x7a325381, 0x28958677, 0x3b8f4898, 0x6b4bb9af, 0xc4bfe81b, 0x66282193,\n  0x61d809cc, 0xfb21a991, 0x487cac60, 0x5dec8032, 0xef845d5d, 0xe98575b1,\n  0xdc262302, 0xeb651b88, 0x23893e81, 0xd396acc5, 0x0f6d6ff3, 0x83f44239,\n  0x2e0b4482, 0xa4842004, 0x69c8f04a, 0x9e1f9b5e, 0x21c66842, 0xf6e96c9a,\n  0x670c9c61, 0xabd388f0, 0x6a51a0d2, 0xd8542f68, 0x960fa728, 0xab5133a3,\n  0x6eef0b6c, 0x137a3be4, 0xba3bf050, 0x7efb2a98, 0xa1f1651d, 0x39af0176,\n  0x66ca593e, 0x82430e88, 0x8cee8619, 0x456f9fb4, 0x7d84a5c3, 0x3b8b5ebe,\n  0xe06f75d8, 0x85c12073, 0x401a449f, 0x56c16aa6, 0x4ed3aa62, 0x363f7706,\n  0x1bfedf72, 0x429b023d, 0x37d0d724, 0xd00a1248, 0xdb0fead3, 0x49f1c09b,\n  0x075372c9, 0x80991b7b, 0x25d479d8, 0xf6e8def7, 0xe3fe501a, 0xb6794c3b,\n  0x976ce0bd, 0x04c006ba, 0xc1a94fb6, 0x409f60c4, 0x5e5c9ec2, 0x196a2463,\n  0x68fb6faf, 0x3e6c53b5, 0x1339b2eb, 0x3b52ec6f, 0x6dfc511f, 0x9b30952c,\n  0xcc814544, 0xaf5ebd09, 0xbee3d004, 0xde334afd, 0x660f2807, 0x192e4bb3,\n  0xc0cba857, 0x45c8740f, 0xd20b5f39, 0xb9d3fbdb, 0x5579c0bd, 0x1a60320a,\n  0xd6a100c6, 0x402c7279, 0x679f25fe, 0xfb1fa3cc, 0x8ea5e9f8, 0xdb3222f8,\n  0x3c7516df, 0xfd616b15, 0x2f501ec8, 0xad0552ab, 0x323db5fa, 0xfd238760,\n  0x53317b48, 0x3e00df82, 0x9e5c57bb, 0xca6f8ca0, 0x1a87562e, 0xdf1769db,\n  0xd542a8f6, 0x287effc3, 0xac6732c6, 0x8c4f5573, 0x695b27b0, 0xbbca58c8,\n  0xe1ffa35d, 0xb8f011a0, 0x10fa3d98, 0xfd2183b8, 0x4afcb56c, 0x2dd1d35b,\n  0x9a53e479, 0xb6f84565, 0xd28e49bc, 0x4bfb9790, 0xe1ddf2da, 0xa4cb7e33,\n  0x62fb1341, 0xcee4c6e8, 0xef20cada, 0x36774c01, 0xd07e9efe, 0x2bf11fb4,\n  0x95dbda4d, 0xae909198, 0xeaad8e71, 0x6b93d5a0, 0xd08ed1d0, 0xafc725e0,\n  0x8e3c5b2f, 0x8e7594b7, 0x8ff6e2fb, 0xf2122b64, 0x8888b812, 0x900df01c,\n  0x4fad5ea0, 0x688fc31c, 0xd1cff191, 0xb3a8c1ad, 0x2f2f2218, 0xbe0e1777,\n  0xea752dfe, 0x8b021fa1, 0xe5a0cc0f, 0xb56f74e8, 0x18acf3d6, 0xce89e299,\n  0xb4a84fe0, 0xfd13e0b7, 0x7cc43b81, 0xd2ada8d9, 0x165fa266, 0x80957705,\n  0x93cc7314, 0x211a1477, 0xe6ad2065, 0x77b5fa86, 0xc75442f5, 0xfb9d35cf,\n  0xebcdaf0c, 0x7b3e89a0, 0xd6411bd3, 0xae1e7e49, 0x00250e2d, 0x2071b35e,\n  0x226800bb, 0x57b8e0af, 0x2464369b, 0xf009b91e, 0x5563911d, 0x59dfa6aa,\n  0x78c14389, 0xd95a537f, 0x207d5ba2, 0x02e5b9c5, 0x83260376, 0x6295cfa9,\n  0x11c81968, 0x4e734a41, 0xb3472dca, 0x7b14a94a, 0x1b510052, 0x9a532915,\n  0xd60f573f, 0xbc9bc6e4, 0x2b60a476, 0x81e67400, 0x08ba6fb5, 0x571be91f,\n  0xf296ec6b, 0x2a0dd915, 0xb6636521, 0xe7b9f9b6, 0xff34052e, 0xc5855664,\n  0x53b02d5d, 0xa99f8fa1, 0x08ba4799, 0x6e85076a, 0x4b7a70e9, 0xb5b32944,\n  0xdb75092e, 0xc4192623, 0xad6ea6b0, 0x49a7df7d, 0x9cee60b8, 0x8fedb266,\n  0xecaa8c71, 0x699a17ff, 0x5664526c, 0xc2b19ee1, 0x193602a5, 0x75094c29,\n  0xa0591340, 0xe4183a3e, 0x3f54989a, 0x5b429d65, 0x6b8fe4d6, 0x99f73fd6,\n  0xa1d29c07, 0xefe830f5, 0x4d2d38e6, 0xf0255dc1, 0x4cdd2086, 0x8470eb26,\n  0x6382e9c6, 0x021ecc5e, 0x09686b3f, 0x3ebaefc9, 0x3c971814, 0x6b6a70a1,\n  0x687f3584, 0x52a0e286, 0xb79c5305, 0xaa500737, 0x3e07841c, 0x7fdeae5c,\n  0x8e7d44ec, 0x5716f2b8, 0xb03ada37, 0xf0500c0d, 0xf01c1f04, 0x0200b3ff,\n  0xae0cf51a, 0x3cb574b2, 0x25837a58, 0xdc0921bd, 0xd19113f9, 0x7ca92ff6,\n  0x94324773, 0x22f54701, 0x3ae5e581, 0x37c2dadc, 0xc8b57634, 0x9af3dda7,\n  0xa9446146, 0x0fd0030e, 0xecc8c73e, 0xa4751e41, 0xe238cd99, 0x3bea0e2f,\n  0x3280bba1, 0x183eb331, 0x4e548b38, 0x4f6db908, 0x6f420d03, 0xf60a04bf,\n  0x2cb81290, 0x24977c79, 0x5679b072, 0xbcaf89af, 0xde9a771f, 0xd9930810,\n  0xb38bae12, 0xdccf3f2e, 0x5512721f, 0x2e6b7124, 0x501adde6, 0x9f84cd87,\n  0x7a584718, 0x7408da17, 0xbc9f9abc, 0xe94b7d8c, 0xec7aec3a, 0xdb851dfa,\n  0x63094366, 0xc464c3d2, 0xef1c1847, 0x3215d908, 0xdd433b37, 0x24c2ba16,\n  0x12a14d43, 0x2a65c451, 0x50940002, 0x133ae4dd, 0x71dff89e, 0x10314e55,\n  0x81ac77d6, 0x5f11199b, 0x043556f1, 0xd7a3c76b, 0x3c11183b, 0x5924a509,\n  0xf28fe6ed, 0x97f1fbfa, 0x9ebabf2c, 0x1e153c6e, 0x86e34570, 0xeae96fb1,\n  0x860e5e0a, 0x5a3e2ab3, 0x771fe71c, 0x4e3d06fa, 0x2965dcb9, 0x99e71d0f,\n  0x803e89d6, 0x5266c825, 0x2e4cc978, 0x9c10b36a, 0xc6150eba, 0x94e2ea78,\n  0xa5fc3c53, 0x1e0a2df4, 0xf2f74ea7, 0x361d2b3d, 0x1939260f, 0x19c27960,\n  0x5223a708, 0xf71312b6, 0xebadfe6e, 0xeac31f66, 0xe3bc4595, 0xa67bc883,\n  0xb17f37d1, 0x018cff28, 0xc332ddef, 0xbe6c5aa5, 0x65582185, 0x68ab9802,\n  0xeecea50f, 0xdb2f953b, 0x2aef7dad, 0x5b6e2f84, 0x1521b628, 0x29076170,\n  0xecdd4775, 0x619f1510, 0x13cca830, 0xeb61bd96, 0x0334fe1e, 0xaa0363cf,\n  0xb5735c90, 0x4c70a239, 0xd59e9e0b, 0xcbaade14, 0xeecc86bc, 0x60622ca7,\n  0x9cab5cab, 0xb2f3846e, 0x648b1eaf, 0x19bdf0ca, 0xa02369b9, 0x655abb50,\n  0x40685a32, 0x3c2ab4b3, 0x319ee9d5, 0xc021b8f7, 0x9b540b19, 0x875fa099,\n  0x95f7997e, 0x623d7da8, 0xf837889a, 0x97e32d77, 0x11ed935f, 0x16681281,\n  0x0e358829, 0xc7e61fd6, 0x96dedfa1, 0x7858ba99, 0x57f584a5, 0x1b227263,\n  0x9b83c3ff, 0x1ac24696, 0xcdb30aeb, 0x532e3054, 0x8fd948e4, 0x6dbc3128,\n  0x58ebf2ef, 0x34c6ffea, 0xfe28ed61, 0xee7c3c73, 0x5d4a14d9, 0xe864b7e3,\n  0x42105d14, 0x203e13e0, 0x45eee2b6, 0xa3aaabea, 0xdb6c4f15, 0xfacb4fd0,\n  0xc742f442, 0xef6abbb5, 0x654f3b1d, 0x41cd2105, 0xd81e799e, 0x86854dc7,\n  0xe44b476a, 0x3d816250, 0xcf62a1f2, 0x5b8d2646, 0xfc8883a0, 0xc1c7b6a3,\n  0x7f1524c3, 0x69cb7492, 0x47848a0b, 0x5692b285, 0x095bbf00, 0xad19489d,\n  0x1462b174, 0x23820e00, 0x58428d2a, 0x0c55f5ea, 0x1dadf43e, 0x233f7061,\n  0x3372f092, 0x8d937e41, 0xd65fecf1, 0x6c223bdb, 0x7cde3759, 0xcbee7460,\n  0x4085f2a7, 0xce77326e, 0xa6078084, 0x19f8509e, 0xe8efd855, 0x61d99735,\n  0xa969a7aa, 0xc50c06c2, 0x5a04abfc, 0x800bcadc, 0x9e447a2e, 0xc3453484,\n  0xfdd56705, 0x0e1e9ec9, 0xdb73dbd3, 0x105588cd, 0x675fda79, 0xe3674340,\n  0xc5c43465, 0x713e38d8, 0x3d28f89e, 0xf16dff20, 0x153e21e7, 0x8fb03d4a,\n  0xe6e39f2b, 0xdb83adf7, 0xe93d5a68, 0x948140f7, 0xf64c261c, 0x94692934,\n  0x411520f7, 0x7602d4f7, 0xbcf46b2e, 0xd4a20068, 0xd4082471, 0x3320f46a,\n  0x43b7d4b7, 0x500061af, 0x1e39f62e, 0x97244546, 0x14214f74, 0xbf8b8840,\n  0x4d95fc1d, 0x96b591af, 0x70f4ddd3, 0x66a02f45, 0xbfbc09ec, 0x03bd9785,\n  0x7fac6dd0, 0x31cb8504, 0x96eb27b3, 0x55fd3941, 0xda2547e6, 0xabca0a9a,\n  0x28507825, 0x530429f4, 0x0a2c86da, 0xe9b66dfb, 0x68dc1462, 0xd7486900,\n  0x680ec0a4, 0x27a18dee, 0x4f3ffea2, 0xe887ad8c, 0xb58ce006, 0x7af4d6b6,\n  0xaace1e7c, 0xd3375fec, 0xce78a399, 0x406b2a42, 0x20fe9e35, 0xd9f385b9,\n  0xee39d7ab, 0x3b124e8b, 0x1dc9faf7, 0x4b6d1856, 0x26a36631, 0xeae397b2,\n  0x3a6efa74, 0xdd5b4332, 0x6841e7f7, 0xca7820fb, 0xfb0af54e, 0xd8feb397,\n  0x454056ac, 0xba489527, 0x55533a3a, 0x20838d87, 0xfe6ba9b7, 0xd096954b,\n  0x55a867bc, 0xa1159a58, 0xcca92963, 0x99e1db33, 0xa62a4a56, 0x3f3125f9,\n  0x5ef47e1c, 0x9029317c, 0xfdf8e802, 0x04272f70, 0x80bb155c, 0x05282ce3,\n  0x95c11548, 0xe4c66d22, 0x48c1133f, 0xc70f86dc, 0x07f9c9ee, 0x41041f0f,\n  0x404779a4, 0x5d886e17, 0x325f51eb, 0xd59bc0d1, 0xf2bcc18f, 0x41113564,\n  0x257b7834, 0x602a9c60, 0xdff8e8a3, 0x1f636c1b, 0x0e12b4c2, 0x02e1329e,\n  0xaf664fd1, 0xcad18115, 0x6b2395e0, 0x333e92e1, 0x3b240b62, 0xeebeb922,\n  0x85b2a20e, 0xe6ba0d99, 0xde720c8c, 0x2da2f728, 0xd0127845, 0x95b794fd,\n  0x647d0862, 0xe7ccf5f0, 0x5449a36f, 0x877d48fa, 0xc39dfd27, 0xf33e8d1e,\n  0x0a476341, 0x992eff74, 0x3a6f6eab, 0xf4f8fd37, 0xa812dc60, 0xa1ebddf8,\n  0x991be14c, 0xdb6e6b0d, 0xc67b5510, 0x6d672c37, 0x2765d43b, 0xdcd0e804,\n  0xf1290dc7, 0xcc00ffa3, 0xb5390f92, 0x690fed0b, 0x667b9ffb, 0xcedb7d9c,\n  0xa091cf0b, 0xd9155ea3, 0xbb132f88, 0x515bad24, 0x7b9479bf, 0x763bd6eb,\n  0x37392eb3, 0xcc115979, 0x8026e297, 0xf42e312d, 0x6842ada7, 0xc66a2b3b,\n  0x12754ccc, 0x782ef11c, 0x6a124237, 0xb79251e7, 0x06a1bbe6, 0x4bfb6350,\n  0x1a6b1018, 0x11caedfa, 0x3d25bdd8, 0xe2e1c3c9, 0x44421659, 0x0a121386,\n  0xd90cec6e, 0xd5abea2a, 0x64af674e, 0xda86a85f, 0xbebfe988, 0x64e4c3fe,\n  0x9dbc8057, 0xf0f7c086, 0x60787bf8, 0x6003604d, 0xd1fd8346, 0xf6381fb0,\n  0x7745ae04, 0xd736fccc, 0x83426b33, 0xf01eab71, 0xb0804187, 0x3c005e5f,\n  0x77a057be, 0xbde8ae24, 0x55464299, 0xbf582e61, 0x4e58f48f, 0xf2ddfda2,\n  0xf474ef38, 0x8789bdc2, 0x5366f9c3, 0xc8b38e74, 0xb475f255, 0x46fcd9b9,\n  0x7aeb2661, 0x8b1ddf84, 0x846a0e79, 0x915f95e2, 0x466e598e, 0x20b45770,\n  0x8cd55591, 0xc902de4c, 0xb90bace1, 0xbb8205d0, 0x11a86248, 0x7574a99e,\n  0xb77f19b6, 0xe0a9dc09, 0x662d09a1, 0xc4324633, 0xe85a1f02, 0x09f0be8c,\n  0x4a99a025, 0x1d6efe10, 0x1ab93d1d, 0x0ba5a4df, 0xa186f20f, 0x2868f169,\n  0xdcb7da83, 0x573906fe, 0xa1e2ce9b, 0x4fcd7f52, 0x50115e01, 0xa70683fa,\n  0xa002b5c4, 0x0de6d027, 0x9af88c27, 0x773f8641, 0xc3604c06, 0x61a806b5,\n  0xf0177a28, 0xc0f586e0, 0x006058aa, 0x30dc7d62, 0x11e69ed7, 0x2338ea63,\n  0x53c2dd94, 0xc2c21634, 0xbbcbee56, 0x90bcb6de, 0xebfc7da1, 0xce591d76,\n  0x6f05e409, 0x4b7c0188, 0x39720a3d, 0x7c927c24, 0x86e3725f, 0x724d9db9,\n  0x1ac15bb4, 0xd39eb8fc, 0xed545578, 0x08fca5b5, 0xd83d7cd3, 0x4dad0fc4,\n  0x1e50ef5e, 0xb161e6f8, 0xa28514d9, 0x6c51133c, 0x6fd5c7e7, 0x56e14ec4,\n  0x362abfce, 0xddc6c837, 0xd79a3234, 0x92638212, 0x670efa8e, 0x406000e0,\n  0x3a39ce37, 0xd3faf5cf, 0xabc27737, 0x5ac52d1b, 0x5cb0679e, 0x4fa33742,\n  0xd3822740, 0x99bc9bbe, 0xd5118e9d, 0xbf0f7315, 0xd62d1c7e, 0xc700c47b,\n  0xb78c1b6b, 0x21a19045, 0xb26eb1be, 0x6a366eb4, 0x5748ab2f, 0xbc946e79,\n  0xc6a376d2, 0x6549c2c8, 0x530ff8ee, 0x468dde7d, 0xd5730a1d, 0x4cd04dc6,\n  0x2939bbdb, 0xa9ba4650, 0xac9526e8, 0xbe5ee304, 0xa1fad5f0, 0x6a2d519a,\n  0x63ef8ce2, 0x9a86ee22, 0xc089c2b8, 0x43242ef6, 0xa51e03aa, 0x9cf2d0a4,\n  0x83c061ba, 0x9be96a4d, 0x8fe51550, 0xba645bd6, 0x2826a2f9, 0xa73a3ae1,\n  0x4ba99586, 0xef5562e9, 0xc72fefd3, 0xf752f7da, 0x3f046f69, 0x77fa0a59,\n  0x80e4a915, 0x87b08601, 0x9b09e6ad, 0x3b3ee593, 0xe990fd5a, 0x9e34d797,\n  0x2cf0b7d9, 0x022b8b51, 0x96d5ac3a, 0x017da67d, 0xd1cf3ed6, 0x7c7d2d28,\n  0x1f9f25cf, 0xadf2b89b, 0x5ad6b472, 0x5a88f54c, 0xe029ac71, 0xe019a5e6,\n  0x47b0acfd, 0xed93fa9b, 0xe8d3c48d, 0x283b57cc, 0xf8d56629, 0x79132e28,\n  0x785f0191, 0xed756055, 0xf7960e44, 0xe3d35e8c, 0x15056dd4, 0x88f46dba,\n  0x03a16125, 0x0564f0bd, 0xc3eb9e15, 0x3c9057a2, 0x97271aec, 0xa93a072a,\n  0x1b3f6d9b, 0x1e6321f5, 0xf59c66fb, 0x26dcf319, 0x7533d928, 0xb155fdf5,\n  0x03563482, 0x8aba3cbb, 0x28517711, 0xc20ad9f8, 0xabcc5167, 0xccad925f,\n  0x4de81751, 0x3830dc8e, 0x379d5862, 0x9320f991, 0xea7a90c2, 0xfb3e7bce,\n  0x5121ce64, 0x774fbe32, 0xa8b6e37e, 0xc3293d46, 0x48de5369, 0x6413e680,\n  0xa2ae0810, 0xdd6db224, 0x69852dfd, 0x09072166, 0xb39a460a, 0x6445c0dd,\n  0x586cdecf, 0x1c20c8ae, 0x5bbef7dd, 0x1b588d40, 0xccd2017f, 0x6bb4e3bb,\n  0xdda26a7e, 0x3a59ff45, 0x3e350a44, 0xbcb4cdd5, 0x72eacea8, 0xfa6484bb,\n  0x8d6612ae, 0xbf3c6f47, 0xd29be463, 0x542f5d9e, 0xaec2771b, 0xf64e6370,\n  0x740e0d8d, 0xe75b1357, 0xf8721671, 0xaf537d5d, 0x4040cb08, 0x4eb4e2cc,\n  0x34d2466a, 0x0115af84, 0xe1b00428, 0x95983a1d, 0x06b89fb4, 0xce6ea048,\n  0x6f3f3b82, 0x3520ab82, 0x011a1d4b, 0x277227f8, 0x611560b1, 0xe7933fdc,\n  0xbb3a792b, 0x344525bd, 0xa08839e1, 0x51ce794b, 0x2f32c9b7, 0xa01fbac9,\n  0xe01cc87e, 0xbcc7d1f6, 0xcf0111c3, 0xa1e8aac7, 0x1a908749, 0xd44fbd9a,\n  0xd0dadecb, 0xd50ada38, 0x0339c32a, 0xc6913667, 0x8df9317c, 0xe0b12b4f,\n  0xf79e59b7, 0x43f5bb3a, 0xf2d519ff, 0x27d9459c, 0xbf97222c, 0x15e6fc2a,\n  0x0f91fc71, 0x9b941525, 0xfae59361, 0xceb69ceb, 0xc2a86459, 0x12baa8d1,\n  0xb6c1075e, 0xe3056a0c, 0x10d25065, 0xcb03a442, 0xe0ec6e0e, 0x1698db3b,\n  0x4c98a0be, 0x3278e964, 0x9f1f9532, 0xe0d392df, 0xd3a0342b, 0x8971f21e,\n  0x1b0a7441, 0x4ba3348c, 0xc5be7120, 0xc37632d8, 0xdf359f8d, 0x9b992f2e,\n  0xe60b6f47, 0x0fe3f11d, 0xe54cda54, 0x1edad891, 0xce6279cf, 0xcd3e7e6f,\n  0x1618b166, 0xfd2c1d05, 0x848fd2c5, 0xf6fb2299, 0xf523f357, 0xa6327623,\n  0x93a83531, 0x56cccd02, 0xacf08162, 0x5a75ebb5, 0x6e163697, 0x88d273cc,\n  0xde966292, 0x81b949d0, 0x4c50901b, 0x71c65614, 0xe6c6c7bd, 0x327a140a,\n  0x45e1d006, 0xc3f27b9a, 0xc9aa53fd, 0x62a80f00, 0xbb25bfe2, 0x35bdd2f6,\n  0x71126905, 0xb2040222, 0xb6cbcf7c, 0xcd769c2b, 0x53113ec0, 0x1640e3d3,\n  0x38abbd60, 0x2547adf0, 0xba38209c, 0xf746ce76, 0x77afa1c5, 0x20756060,\n  0x85cbfe4e, 0x8ae88dd8, 0x7aaaf9b0, 0x4cf9aa7e, 0x1948c25c, 0x02fb8a8c,\n  0x01c36ae4, 0xd6ebe1f9, 0x90d4f869, 0xa65cdea0, 0x3f09252d, 0xc208e69f,\n  0xb74e6132, 0xce77e25b, 0x578fdfe3, 0x3ac372e6,\n];\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar C_ORIG = [\n  0x4f727068, 0x65616e42, 0x65686f6c, 0x64657253, 0x63727944, 0x6f756274,\n];\n\n/**\n * @param {Array.<number>} lr\n * @param {number} off\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @returns {Array.<number>}\n * @inner\n */\nfunction _encipher(lr, off, P, S) {\n  // This is our bottleneck: 1714/1905 ticks / 90% - see profile.txt\n  var n,\n    l = lr[off],\n    r = lr[off + 1];\n\n  l ^= P[0];\n\n  /*\n    for (var i=0, k=BLOWFISH_NUM_ROUNDS-2; i<=k;)\n        // Feistel substitution on left word\n        n  = S[l >>> 24],\n        n += S[0x100 | ((l >> 16) & 0xff)],\n        n ^= S[0x200 | ((l >> 8) & 0xff)],\n        n += S[0x300 | (l & 0xff)],\n        r ^= n ^ P[++i],\n        // Feistel substitution on right word\n        n  = S[r >>> 24],\n        n += S[0x100 | ((r >> 16) & 0xff)],\n        n ^= S[0x200 | ((r >> 8) & 0xff)],\n        n += S[0x300 | (r & 0xff)],\n        l ^= n ^ P[++i];\n    */\n\n  //The following is an unrolled version of the above loop.\n  //Iteration 0\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[1];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[2];\n  //Iteration 1\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[3];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[4];\n  //Iteration 2\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[5];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[6];\n  //Iteration 3\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[7];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[8];\n  //Iteration 4\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[9];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[10];\n  //Iteration 5\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[11];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[12];\n  //Iteration 6\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[13];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[14];\n  //Iteration 7\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[15];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[16];\n\n  lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];\n  lr[off + 1] = l;\n  return lr;\n}\n\n/**\n * @param {Array.<number>} data\n * @param {number} offp\n * @returns {{key: number, offp: number}}\n * @inner\n */\nfunction _streamtoword(data, offp) {\n  for (var i = 0, word = 0; i < 4; ++i)\n    (word = (word << 8) | (data[offp] & 0xff)),\n      (offp = (offp + 1) % data.length);\n  return { key: word, offp: offp };\n}\n\n/**\n * @param {Array.<number>} key\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @inner\n */\nfunction _key(key, P, S) {\n  var offset = 0,\n    lr = [0, 0],\n    plen = P.length,\n    slen = S.length,\n    sw;\n  for (var i = 0; i < plen; i++)\n    (sw = _streamtoword(key, offset)),\n      (offset = sw.offp),\n      (P[i] = P[i] ^ sw.key);\n  for (i = 0; i < plen; i += 2)\n    (lr = _encipher(lr, 0, P, S)), (P[i] = lr[0]), (P[i + 1] = lr[1]);\n  for (i = 0; i < slen; i += 2)\n    (lr = _encipher(lr, 0, P, S)), (S[i] = lr[0]), (S[i + 1] = lr[1]);\n}\n\n/**\n * Expensive key schedule Blowfish.\n * @param {Array.<number>} data\n * @param {Array.<number>} key\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @inner\n */\nfunction _ekskey(data, key, P, S) {\n  var offp = 0,\n    lr = [0, 0],\n    plen = P.length,\n    slen = S.length,\n    sw;\n  for (var i = 0; i < plen; i++)\n    (sw = _streamtoword(key, offp)), (offp = sw.offp), (P[i] = P[i] ^ sw.key);\n  offp = 0;\n  for (i = 0; i < plen; i += 2)\n    (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[0] ^= sw.key),\n      (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[1] ^= sw.key),\n      (lr = _encipher(lr, 0, P, S)),\n      (P[i] = lr[0]),\n      (P[i + 1] = lr[1]);\n  for (i = 0; i < slen; i += 2)\n    (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[0] ^= sw.key),\n      (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[1] ^= sw.key),\n      (lr = _encipher(lr, 0, P, S)),\n      (S[i] = lr[0]),\n      (S[i + 1] = lr[1]);\n}\n\n/**\n * Internaly crypts a string.\n * @param {Array.<number>} b Bytes to crypt\n * @param {Array.<number>} salt Salt bytes to use\n * @param {number} rounds Number of rounds\n * @param {function(Error, Array.<number>=)=} callback Callback receiving the error, if any, and the resulting bytes. If\n *  omitted, the operation will be performed synchronously.\n *  @param {function(number)=} progressCallback Callback called with the current progress\n * @returns {!Array.<number>|undefined} Resulting bytes if callback has been omitted, otherwise `undefined`\n * @inner\n */\nfunction _crypt(b, salt, rounds, callback, progressCallback) {\n  var cdata = C_ORIG.slice(),\n    clen = cdata.length,\n    err;\n\n  // Validate\n  if (rounds < 4 || rounds > 31) {\n    err = Error(\"Illegal number of rounds (4-31): \" + rounds);\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  if (salt.length !== BCRYPT_SALT_LEN) {\n    err = Error(\n      \"Illegal salt length: \" + salt.length + \" != \" + BCRYPT_SALT_LEN,\n    );\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  rounds = (1 << rounds) >>> 0;\n\n  var P,\n    S,\n    i = 0,\n    j;\n\n  //Use typed arrays when available - huge speedup!\n  if (typeof Int32Array === \"function\") {\n    P = new Int32Array(P_ORIG);\n    S = new Int32Array(S_ORIG);\n  } else {\n    P = P_ORIG.slice();\n    S = S_ORIG.slice();\n  }\n\n  _ekskey(salt, b, P, S);\n\n  /**\n   * Calcualtes the next round.\n   * @returns {Array.<number>|undefined} Resulting array if callback has been omitted, otherwise `undefined`\n   * @inner\n   */\n  function next() {\n    if (progressCallback) progressCallback(i / rounds);\n    if (i < rounds) {\n      var start = Date.now();\n      for (; i < rounds; ) {\n        i = i + 1;\n        _key(b, P, S);\n        _key(salt, P, S);\n        if (Date.now() - start > MAX_EXECUTION_TIME) break;\n      }\n    } else {\n      for (i = 0; i < 64; i++)\n        for (j = 0; j < clen >> 1; j++) _encipher(cdata, j << 1, P, S);\n      var ret = [];\n      for (i = 0; i < clen; i++)\n        ret.push(((cdata[i] >> 24) & 0xff) >>> 0),\n          ret.push(((cdata[i] >> 16) & 0xff) >>> 0),\n          ret.push(((cdata[i] >> 8) & 0xff) >>> 0),\n          ret.push((cdata[i] & 0xff) >>> 0);\n      if (callback) {\n        callback(null, ret);\n        return;\n      } else return ret;\n    }\n    if (callback) nextTick(next);\n  }\n\n  // Async\n  if (typeof callback !== \"undefined\") {\n    next();\n\n    // Sync\n  } else {\n    var res;\n    while (true) if (typeof (res = next()) !== \"undefined\") return res || [];\n  }\n}\n\n/**\n * Internally hashes a password.\n * @param {string} password Password to hash\n * @param {?string} salt Salt to use, actually never null\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash. If omitted,\n *  hashing is performed synchronously.\n *  @param {function(number)=} progressCallback Callback called with the current progress\n * @returns {string|undefined} Resulting hash if callback has been omitted, otherwise `undefined`\n * @inner\n */\nfunction _hash(password, salt, callback, progressCallback) {\n  var err;\n  if (typeof password !== \"string\" || typeof salt !== \"string\") {\n    err = Error(\"Invalid string / salt: Not a string\");\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n\n  // Validate the salt\n  var minor, offset;\n  if (salt.charAt(0) !== \"$\" || salt.charAt(1) !== \"2\") {\n    err = Error(\"Invalid salt version: \" + salt.substring(0, 2));\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  if (salt.charAt(2) === \"$\") (minor = String.fromCharCode(0)), (offset = 3);\n  else {\n    minor = salt.charAt(2);\n    if (\n      (minor !== \"a\" && minor !== \"b\" && minor !== \"y\") ||\n      salt.charAt(3) !== \"$\"\n    ) {\n      err = Error(\"Invalid salt revision: \" + salt.substring(2, 4));\n      if (callback) {\n        nextTick(callback.bind(this, err));\n        return;\n      } else throw err;\n    }\n    offset = 4;\n  }\n\n  // Extract number of rounds\n  if (salt.charAt(offset + 2) > \"$\") {\n    err = Error(\"Missing salt rounds\");\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  var r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10,\n    r2 = parseInt(salt.substring(offset + 1, offset + 2), 10),\n    rounds = r1 + r2,\n    real_salt = salt.substring(offset + 3, offset + 25);\n  password += minor >= \"a\" ? \"\\x00\" : \"\";\n\n  var passwordb = utf8Array(password),\n    saltb = base64_decode(real_salt, BCRYPT_SALT_LEN);\n\n  /**\n   * Finishes hashing.\n   * @param {Array.<number>} bytes Byte array\n   * @returns {string}\n   * @inner\n   */\n  function finish(bytes) {\n    var res = [];\n    res.push(\"$2\");\n    if (minor >= \"a\") res.push(minor);\n    res.push(\"$\");\n    if (rounds < 10) res.push(\"0\");\n    res.push(rounds.toString());\n    res.push(\"$\");\n    res.push(base64_encode(saltb, saltb.length));\n    res.push(base64_encode(bytes, C_ORIG.length * 4 - 1));\n    return res.join(\"\");\n  }\n\n  // Sync\n  if (typeof callback == \"undefined\")\n    return finish(_crypt(passwordb, saltb, rounds));\n  // Async\n  else {\n    _crypt(\n      passwordb,\n      saltb,\n      rounds,\n      function (err, bytes) {\n        if (err) callback(err, null);\n        else callback(null, finish(bytes));\n      },\n      progressCallback,\n    );\n  }\n}\n\n/**\n * Encodes a byte array to base64 with up to len bytes of input, using the custom bcrypt alphabet.\n * @function\n * @param {!Array.<number>} bytes Byte array\n * @param {number} length Maximum input length\n * @returns {string}\n */\nexport function encodeBase64(bytes, length) {\n  return base64_encode(bytes, length);\n}\n\n/**\n * Decodes a base64 encoded string to up to len bytes of output, using the custom bcrypt alphabet.\n * @function\n * @param {string} string String to decode\n * @param {number} length Maximum output length\n * @returns {!Array.<number>}\n */\nexport function decodeBase64(string, length) {\n  return base64_decode(string, length);\n}\n\nexport default {\n  setRandomFallback,\n  genSaltSync,\n  genSalt,\n  hashSync,\n  hash,\n  compareSync,\n  compare,\n  getRounds,\n  getSalt,\n  truncates,\n  encodeBase64,\n  decodeBase64,\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BC,GAED,+EAA+E;AAC/E,+EAA+E;AAC/E,+EAA+E;;;;;;;;;;;;;;;;AAC/E;;AAEA;;;;CAIC,GACD,IAAI,iBAAiB;AAErB;;;;;;;CAOC,GACD,SAAS,YAAY,GAAG;IACtB,yEAAyE;IACzE,IAAI;QACF,OAAO,OAAO,eAAe,CAAC,IAAI,WAAW;IAC/C,EAAE,OAAM,CAAC;IACT,sDAAsD;IACtD,IAAI;QACF,OAAO,qGAAA,CAAA,UAAU,CAAC,WAAW,CAAC;IAChC,EAAE,OAAM,CAAC;IACT,sDAAsD;IACtD,IAAI,CAAC,gBAAgB;QACnB,MAAM,MACJ;IAEJ;IACA,OAAO,eAAe;AACxB;AAWO,SAAS,kBAAkB,MAAM;IACtC,iBAAiB;AACnB;AASO,SAAS,YAAY,MAAM,EAAE,WAAW;IAC7C,SAAS,UAAU;IACnB,IAAI,OAAO,WAAW,UACpB,MAAM,MACJ,wBAAwB,OAAO,SAAS,OAAO,OAAO;IAE1D,IAAI,SAAS,GAAG,SAAS;SACpB,IAAI,SAAS,IAAI,SAAS;IAC/B,IAAI,OAAO,EAAE;IACb,KAAK,IAAI,CAAC;IACV,IAAI,SAAS,IAAI,KAAK,IAAI,CAAC;IAC3B,KAAK,IAAI,CAAC,OAAO,QAAQ;IACzB,KAAK,IAAI,CAAC;IACV,KAAK,IAAI,CAAC,cAAc,YAAY,kBAAkB,mBAAmB,YAAY;IACrF,OAAO,KAAK,IAAI,CAAC;AACnB;AAUO,SAAS,QAAQ,MAAM,EAAE,WAAW,EAAE,QAAQ;IACnD,IAAI,OAAO,gBAAgB,YACzB,AAAC,WAAW,aAAe,cAAc,WAAY,iBAAiB;IACxE,IAAI,OAAO,WAAW,YAAY,AAAC,WAAW,QAAU,SAAS;IACjE,IAAI,OAAO,WAAW,aAAa,SAAS;SACvC,IAAI,OAAO,WAAW,UACzB,MAAM,MAAM,wBAAwB,OAAO;IAE7C,SAAS,OAAO,QAAQ;QACtB,SAAS;YACP,0CAA0C;YAC1C,IAAI;gBACF,SAAS,MAAM,YAAY;YAC7B,EAAE,OAAO,KAAK;gBACZ,SAAS;YACX;QACF;IACF;IAEA,IAAI,UAAU;QACZ,IAAI,OAAO,aAAa,YACtB,MAAM,MAAM,uBAAuB,OAAO;QAC5C,OAAO;IACT,OACE,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;QAC1C,OAAO,SAAU,GAAG,EAAE,GAAG;YACvB,IAAI,KAAK;gBACP,OAAO;gBACP;YACF;YACA,QAAQ;QACV;IACF;AACJ;AAQO,SAAS,SAAS,QAAQ,EAAE,IAAI;IACrC,IAAI,OAAO,SAAS,aAAa,OAAO;IACxC,IAAI,OAAO,SAAS,UAAU,OAAO,YAAY;IACjD,IAAI,OAAO,aAAa,YAAY,OAAO,SAAS,UAClD,MAAM,MAAM,wBAAwB,OAAO,WAAW,OAAO,OAAO;IACtE,OAAO,MAAM,UAAU;AACzB;AAYO,SAAS,KAAK,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,gBAAgB;IAC7D,SAAS,OAAO,QAAQ;QACtB,IAAI,OAAO,aAAa,YAAY,OAAO,SAAS,UAClD,QAAQ,MAAM,SAAU,GAAG,EAAE,IAAI;YAC/B,MAAM,UAAU,MAAM,UAAU;QAClC;aACG,IAAI,OAAO,aAAa,YAAY,OAAO,SAAS,UACvD,MAAM,UAAU,MAAM,UAAU;aAEhC,SACE,SAAS,IAAI,CACX,IAAI,EACJ,MAAM,wBAAwB,OAAO,WAAW,OAAO,OAAO;IAGtE;IAEA,IAAI,UAAU;QACZ,IAAI,OAAO,aAAa,YACtB,MAAM,MAAM,uBAAuB,OAAO;QAC5C,OAAO;IACT,OACE,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;QAC1C,OAAO,SAAU,GAAG,EAAE,GAAG;YACvB,IAAI,KAAK;gBACP,OAAO;gBACP;YACF;YACA,QAAQ;QACV;IACF;AACJ;AAEA;;;;;;CAMC,GACD,SAAS,kBAAkB,KAAK,EAAE,OAAO;IACvC,IAAI,OAAO,MAAM,MAAM,GAAG,QAAQ,MAAM;IACxC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;QACrC,QAAQ,MAAM,UAAU,CAAC,KAAK,QAAQ,UAAU,CAAC;IACnD;IACA,OAAO,SAAS;AAClB;AASO,SAAS,YAAY,QAAQ,EAAE,IAAI;IACxC,IAAI,OAAO,aAAa,YAAY,OAAO,SAAS,UAClD,MAAM,MAAM,wBAAwB,OAAO,WAAW,OAAO,OAAO;IACtE,IAAI,KAAK,MAAM,KAAK,IAAI,OAAO;IAC/B,OAAO,kBACL,SAAS,UAAU,KAAK,SAAS,CAAC,GAAG,KAAK,MAAM,GAAG,MACnD;AAEJ;AAYO,SAAS,QAAQ,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,gBAAgB;IACrE,SAAS,OAAO,QAAQ;QACtB,IAAI,OAAO,aAAa,YAAY,OAAO,cAAc,UAAU;YACjE,SACE,SAAS,IAAI,CACX,IAAI,EACJ,MACE,wBAAwB,OAAO,WAAW,OAAO,OAAO;YAI9D;QACF;QACA,IAAI,UAAU,MAAM,KAAK,IAAI;YAC3B,SAAS,SAAS,IAAI,CAAC,IAAI,EAAE,MAAM;YACnC;QACF;QACA,KACE,UACA,UAAU,SAAS,CAAC,GAAG,KACvB,SAAU,GAAG,EAAE,IAAI;YACjB,IAAI,KAAK,SAAS;iBACb,SAAS,MAAM,kBAAkB,MAAM;QAC9C,GACA;IAEJ;IAEA,IAAI,UAAU;QACZ,IAAI,OAAO,aAAa,YACtB,MAAM,MAAM,uBAAuB,OAAO;QAC5C,OAAO;IACT,OACE,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;QAC1C,OAAO,SAAU,GAAG,EAAE,GAAG;YACvB,IAAI,KAAK;gBACP,OAAO;gBACP;YACF;YACA,QAAQ;QACV;IACF;AACJ;AAQO,SAAS,UAAU,IAAI;IAC5B,IAAI,OAAO,SAAS,UAClB,MAAM,MAAM,wBAAwB,OAAO;IAC7C,OAAO,SAAS,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;AACtC;AAQO,SAAS,QAAQ,IAAI;IAC1B,IAAI,OAAO,SAAS,UAClB,MAAM,MAAM,wBAAwB,OAAO;IAC7C,IAAI,KAAK,MAAM,KAAK,IAClB,MAAM,MAAM,0BAA0B,KAAK,MAAM,GAAG;IACtD,OAAO,KAAK,SAAS,CAAC,GAAG;AAC3B;AAQO,SAAS,UAAU,QAAQ;IAChC,IAAI,OAAO,aAAa,UACtB,MAAM,MAAM,wBAAwB,OAAO;IAC7C,OAAO,WAAW,YAAY;AAChC;AAEA;;;;;CAKC,GACD,IAAI,WACF,OAAO,YAAY,eACnB,WACA,OAAO,QAAQ,QAAQ,KAAK,aACxB,OAAO,iBAAiB,aACtB,eACA,QAAQ,QAAQ,GAClB;AAEN,4DAA4D,GAC5D,SAAS,WAAW,MAAM;IACxB,IAAI,MAAM,GACR,IAAI;IACN,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;QACtC,IAAI,OAAO,UAAU,CAAC;QACtB,IAAI,IAAI,KAAK,OAAO;aACf,IAAI,IAAI,MAAM,OAAO;aACrB,IACH,CAAC,IAAI,MAAM,MAAM,UACjB,CAAC,OAAO,UAAU,CAAC,IAAI,KAAK,MAAM,MAAM,QACxC;YACA,EAAE;YACF,OAAO;QACT,OAAO,OAAO;IAChB;IACA,OAAO;AACT;AAEA,iDAAiD,GACjD,SAAS,UAAU,MAAM;IACvB,IAAI,SAAS,GACX,IACA;IACF,IAAI,SAAS,IAAI,MAAM,WAAW;IAClC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;QAC7C,KAAK,OAAO,UAAU,CAAC;QACvB,IAAI,KAAK,KAAK;YACZ,MAAM,CAAC,SAAS,GAAG;QACrB,OAAO,IAAI,KAAK,MAAM;YACpB,MAAM,CAAC,SAAS,GAAG,AAAC,MAAM,IAAK;YAC/B,MAAM,CAAC,SAAS,GAAG,AAAC,KAAK,KAAM;QACjC,OAAO,IACL,CAAC,KAAK,MAAM,MAAM,UAClB,CAAC,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,EAAE,IAAI,MAAM,MAAM,QAC/C;YACA,KAAK,UAAU,CAAC,CAAC,KAAK,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK,MAAM;YACnD,EAAE;YACF,MAAM,CAAC,SAAS,GAAG,AAAC,MAAM,KAAM;YAChC,MAAM,CAAC,SAAS,GAAG,AAAE,MAAM,KAAM,KAAM;YACvC,MAAM,CAAC,SAAS,GAAG,AAAE,MAAM,IAAK,KAAM;YACtC,MAAM,CAAC,SAAS,GAAG,AAAC,KAAK,KAAM;QACjC,OAAO;YACL,MAAM,CAAC,SAAS,GAAG,AAAC,MAAM,KAAM;YAChC,MAAM,CAAC,SAAS,GAAG,AAAE,MAAM,IAAK,KAAM;YACtC,MAAM,CAAC,SAAS,GAAG,AAAC,KAAK,KAAM;QACjC;IACF;IACA,OAAO;AACT;AAEA,iFAAiF;AAEjF;;;;;EAKE,GACF,IAAI,cACF,mEAAmE,KAAK,CAAC;AAE3E;;;;EAIE,GACF,IAAI,eAAe;IACjB,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IACzE,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IACzE,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG;IAAG;IAAG;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAC1E,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAI;IAAI;IAAI;IAAI;IACxE;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG;IACxE;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IACxE;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;IAAG,CAAC;CAC1C;AAED;;;;;;CAMC,GACD,SAAS,cAAc,CAAC,EAAE,GAAG;IAC3B,IAAI,MAAM,GACR,KAAK,EAAE,EACP,IACA;IACF,IAAI,OAAO,KAAK,MAAM,EAAE,MAAM,EAAE,MAAM,MAAM,kBAAkB;IAC9D,MAAO,MAAM,IAAK;QAChB,KAAK,CAAC,CAAC,MAAM,GAAG;QAChB,GAAG,IAAI,CAAC,WAAW,CAAC,AAAC,MAAM,IAAK,KAAK;QACrC,KAAK,CAAC,KAAK,IAAI,KAAK;QACpB,IAAI,OAAO,KAAK;YACd,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;YAC9B;QACF;QACA,KAAK,CAAC,CAAC,MAAM,GAAG;QAChB,MAAM,AAAC,MAAM,IAAK;QAClB,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;QAC9B,KAAK,CAAC,KAAK,IAAI,KAAK;QACpB,IAAI,OAAO,KAAK;YACd,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;YAC9B;QACF;QACA,KAAK,CAAC,CAAC,MAAM,GAAG;QAChB,MAAM,AAAC,MAAM,IAAK;QAClB,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;QAC9B,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;IAChC;IACA,OAAO,GAAG,IAAI,CAAC;AACjB;AAEA;;;;;;CAMC,GACD,SAAS,cAAc,CAAC,EAAE,GAAG;IAC3B,IAAI,MAAM,GACR,OAAO,EAAE,MAAM,EACf,OAAO,GACP,KAAK,EAAE,EACP,IACA,IACA,IACA,IACA,GACA;IACF,IAAI,OAAO,GAAG,MAAM,MAAM,kBAAkB;IAC5C,MAAO,MAAM,OAAO,KAAK,OAAO,IAAK;QACnC,OAAO,EAAE,UAAU,CAAC;QACpB,KAAK,OAAO,aAAa,MAAM,GAAG,YAAY,CAAC,KAAK,GAAG,CAAC;QACxD,OAAO,EAAE,UAAU,CAAC;QACpB,KAAK,OAAO,aAAa,MAAM,GAAG,YAAY,CAAC,KAAK,GAAG,CAAC;QACxD,IAAI,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;QAC1B,IAAI,AAAC,MAAM,MAAO;QAClB,KAAK,CAAC,KAAK,IAAI,KAAK;QACpB,GAAG,IAAI,CAAC,OAAO,YAAY,CAAC;QAC5B,IAAI,EAAE,QAAQ,OAAO,OAAO,MAAM;QAClC,OAAO,EAAE,UAAU,CAAC;QACpB,KAAK,OAAO,aAAa,MAAM,GAAG,YAAY,CAAC,KAAK,GAAG,CAAC;QACxD,IAAI,MAAM,CAAC,GAAG;QACd,IAAI,AAAC,CAAC,KAAK,IAAI,KAAK,MAAO;QAC3B,KAAK,CAAC,KAAK,IAAI,KAAK;QACpB,GAAG,IAAI,CAAC,OAAO,YAAY,CAAC;QAC5B,IAAI,EAAE,QAAQ,OAAO,OAAO,MAAM;QAClC,OAAO,EAAE,UAAU,CAAC;QACpB,KAAK,OAAO,aAAa,MAAM,GAAG,YAAY,CAAC,KAAK,GAAG,CAAC;QACxD,IAAI,AAAC,CAAC,KAAK,IAAI,KAAK,MAAO;QAC3B,KAAK;QACL,GAAG,IAAI,CAAC,OAAO,YAAY,CAAC;QAC5B,EAAE;IACJ;IACA,IAAI,MAAM,EAAE;IACZ,IAAK,MAAM,GAAG,MAAM,MAAM,MAAO,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC;IAC7D,OAAO;AACT;AAEA;;;;CAIC,GACD,IAAI,kBAAkB;AAEtB;;;;CAIC,GACD,IAAI,8BAA8B;AAElC;;;;CAIC,GACD,IAAI,sBAAsB;AAE1B;;;;CAIC,GACD,IAAI,qBAAqB;AAEzB;;;;CAIC,GACD,IAAI,SAAS;IACX;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;CAC7D;AAED;;;;CAIC,GACD,IAAI,SAAS;IACX;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;CACrC;AAED;;;;CAIC,GACD,IAAI,SAAS;IACX;IAAY;IAAY;IAAY;IAAY;IAAY;CAC7D;AAED;;;;;;;CAOC,GACD,SAAS,UAAU,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;IAC9B,kEAAkE;IAClE,IAAI,GACF,IAAI,EAAE,CAAC,IAAI,EACX,IAAI,EAAE,CAAC,MAAM,EAAE;IAEjB,KAAK,CAAC,CAAC,EAAE;IAET;;;;;;;;;;;;;;IAcE,GAEF,yDAAyD;IACzD,aAAa;IACb,IAAI,CAAC,CAAC,MAAM,GAAG;IACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;IAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;IACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;IAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;IACb,IAAI,CAAC,CAAC,MAAM,GAAG;IACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;IAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;IACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;IAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;IACb,aAAa;IACb,IAAI,CAAC,CAAC,MAAM,GAAG;IACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;IAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;IACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;IAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;IACb,IAAI,CAAC,CAAC,MAAM,GAAG;IACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;IAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;IACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;IAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;IACb,aAAa;IACb,IAAI,CAAC,CAAC,MAAM,GAAG;IACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;IAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;IACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;IAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;IACb,IAAI,CAAC,CAAC,MAAM,GAAG;IACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;IAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;IACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;IAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;IACb,aAAa;IACb,IAAI,CAAC,CAAC,MAAM,GAAG;IACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;IAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;IACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;IAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;IACb,IAAI,CAAC,CAAC,MAAM,GAAG;IACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;IAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;IACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;IAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;IACb,aAAa;IACb,IAAI,CAAC,CAAC,MAAM,GAAG;IACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;IAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;IACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;IAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;IACb,IAAI,CAAC,CAAC,MAAM,GAAG;IACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;IAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;IACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;IAC1B,KAAK,IAAI,CAAC,CAAC,GAAG;IACd,aAAa;IACb,IAAI,CAAC,CAAC,MAAM,GAAG;IACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;IAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;IACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;IAC1B,KAAK,IAAI,CAAC,CAAC,GAAG;IACd,IAAI,CAAC,CAAC,MAAM,GAAG;IACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;IAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;IACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;IAC1B,KAAK,IAAI,CAAC,CAAC,GAAG;IACd,aAAa;IACb,IAAI,CAAC,CAAC,MAAM,GAAG;IACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;IAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;IACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;IAC1B,KAAK,IAAI,CAAC,CAAC,GAAG;IACd,IAAI,CAAC,CAAC,MAAM,GAAG;IACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;IAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;IACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;IAC1B,KAAK,IAAI,CAAC,CAAC,GAAG;IACd,aAAa;IACb,IAAI,CAAC,CAAC,MAAM,GAAG;IACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;IAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;IACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;IAC1B,KAAK,IAAI,CAAC,CAAC,GAAG;IACd,IAAI,CAAC,CAAC,MAAM,GAAG;IACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;IAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;IACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;IAC1B,KAAK,IAAI,CAAC,CAAC,GAAG;IAEd,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,sBAAsB,EAAE;IACxC,EAAE,CAAC,MAAM,EAAE,GAAG;IACd,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,cAAc,IAAI,EAAE,IAAI;IAC/B,IAAK,IAAI,IAAI,GAAG,OAAO,GAAG,IAAI,GAAG,EAAE,EACjC,AAAC,OAAO,AAAC,QAAQ,IAAM,IAAI,CAAC,KAAK,GAAG,MACjC,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,MAAM;IACpC,OAAO;QAAE,KAAK;QAAM,MAAM;IAAK;AACjC;AAEA;;;;;CAKC,GACD,SAAS,KAAK,GAAG,EAAE,CAAC,EAAE,CAAC;IACrB,IAAI,SAAS,GACX,KAAK;QAAC;QAAG;KAAE,EACX,OAAO,EAAE,MAAM,EACf,OAAO,EAAE,MAAM,EACf;IACF,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IACxB,AAAC,KAAK,cAAc,KAAK,SACtB,SAAS,GAAG,IAAI,EAChB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG;IACzB,IAAK,IAAI,GAAG,IAAI,MAAM,KAAK,EACzB,AAAC,KAAK,UAAU,IAAI,GAAG,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAI,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE;IAClE,IAAK,IAAI,GAAG,IAAI,MAAM,KAAK,EACzB,AAAC,KAAK,UAAU,IAAI,GAAG,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAI,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE;AACpE;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;IAC9B,IAAI,OAAO,GACT,KAAK;QAAC;QAAG;KAAE,EACX,OAAO,EAAE,MAAM,EACf,OAAO,EAAE,MAAM,EACf;IACF,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IACxB,AAAC,KAAK,cAAc,KAAK,OAAS,OAAO,GAAG,IAAI,EAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG;IAC1E,OAAO;IACP,IAAK,IAAI,GAAG,IAAI,MAAM,KAAK,EACzB,AAAC,KAAK,cAAc,MAAM,OACvB,OAAO,GAAG,IAAI,EACd,EAAE,CAAC,EAAE,IAAI,GAAG,GAAG,EACf,KAAK,cAAc,MAAM,OACzB,OAAO,GAAG,IAAI,EACd,EAAE,CAAC,EAAE,IAAI,GAAG,GAAG,EACf,KAAK,UAAU,IAAI,GAAG,GAAG,IACzB,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EACZ,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE;IACrB,IAAK,IAAI,GAAG,IAAI,MAAM,KAAK,EACzB,AAAC,KAAK,cAAc,MAAM,OACvB,OAAO,GAAG,IAAI,EACd,EAAE,CAAC,EAAE,IAAI,GAAG,GAAG,EACf,KAAK,cAAc,MAAM,OACzB,OAAO,GAAG,IAAI,EACd,EAAE,CAAC,EAAE,IAAI,GAAG,GAAG,EACf,KAAK,UAAU,IAAI,GAAG,GAAG,IACzB,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EACZ,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE;AACvB;AAEA;;;;;;;;;;CAUC,GACD,SAAS,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB;IACzD,IAAI,QAAQ,OAAO,KAAK,IACtB,OAAO,MAAM,MAAM,EACnB;IAEF,WAAW;IACX,IAAI,SAAS,KAAK,SAAS,IAAI;QAC7B,MAAM,MAAM,sCAAsC;QAClD,IAAI,UAAU;YACZ,SAAS,SAAS,IAAI,CAAC,IAAI,EAAE;YAC7B;QACF,OAAO,MAAM;IACf;IACA,IAAI,KAAK,MAAM,KAAK,iBAAiB;QACnC,MAAM,MACJ,0BAA0B,KAAK,MAAM,GAAG,SAAS;QAEnD,IAAI,UAAU;YACZ,SAAS,SAAS,IAAI,CAAC,IAAI,EAAE;YAC7B;QACF,OAAO,MAAM;IACf;IACA,SAAS,AAAC,KAAK,WAAY;IAE3B,IAAI,GACF,GACA,IAAI,GACJ;IAEF,iDAAiD;IACjD,IAAI,OAAO,eAAe,YAAY;QACpC,IAAI,IAAI,WAAW;QACnB,IAAI,IAAI,WAAW;IACrB,OAAO;QACL,IAAI,OAAO,KAAK;QAChB,IAAI,OAAO,KAAK;IAClB;IAEA,QAAQ,MAAM,GAAG,GAAG;IAEpB;;;;GAIC,GACD,SAAS;QACP,IAAI,kBAAkB,iBAAiB,IAAI;QAC3C,IAAI,IAAI,QAAQ;YACd,IAAI,QAAQ,KAAK,GAAG;YACpB,MAAO,IAAI,QAAU;gBACnB,IAAI,IAAI;gBACR,KAAK,GAAG,GAAG;gBACX,KAAK,MAAM,GAAG;gBACd,IAAI,KAAK,GAAG,KAAK,QAAQ,oBAAoB;YAC/C;QACF,OAAO;YACL,IAAK,IAAI,GAAG,IAAI,IAAI,IAClB,IAAK,IAAI,GAAG,IAAI,QAAQ,GAAG,IAAK,UAAU,OAAO,KAAK,GAAG,GAAG;YAC9D,IAAI,MAAM,EAAE;YACZ,IAAK,IAAI,GAAG,IAAI,MAAM,IACpB,IAAI,IAAI,CAAC,CAAC,AAAC,KAAK,CAAC,EAAE,IAAI,KAAM,IAAI,MAAM,IACrC,IAAI,IAAI,CAAC,CAAC,AAAC,KAAK,CAAC,EAAE,IAAI,KAAM,IAAI,MAAM,IACvC,IAAI,IAAI,CAAC,CAAC,AAAC,KAAK,CAAC,EAAE,IAAI,IAAK,IAAI,MAAM,IACtC,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,MAAM;YACnC,IAAI,UAAU;gBACZ,SAAS,MAAM;gBACf;YACF,OAAO,OAAO;QAChB;QACA,IAAI,UAAU,SAAS;IACzB;IAEA,QAAQ;IACR,IAAI,OAAO,aAAa,aAAa;QACnC;IAEA,OAAO;IACT,OAAO;QACL,IAAI;QACJ,MAAO,KAAM,IAAI,OAAO,CAAC,MAAM,MAAM,MAAM,aAAa,OAAO,OAAO,EAAE;IAC1E;AACF;AAEA;;;;;;;;;CASC,GACD,SAAS,MAAM,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,gBAAgB;IACvD,IAAI;IACJ,IAAI,OAAO,aAAa,YAAY,OAAO,SAAS,UAAU;QAC5D,MAAM,MAAM;QACZ,IAAI,UAAU;YACZ,SAAS,SAAS,IAAI,CAAC,IAAI,EAAE;YAC7B;QACF,OAAO,MAAM;IACf;IAEA,oBAAoB;IACpB,IAAI,OAAO;IACX,IAAI,KAAK,MAAM,CAAC,OAAO,OAAO,KAAK,MAAM,CAAC,OAAO,KAAK;QACpD,MAAM,MAAM,2BAA2B,KAAK,SAAS,CAAC,GAAG;QACzD,IAAI,UAAU;YACZ,SAAS,SAAS,IAAI,CAAC,IAAI,EAAE;YAC7B;QACF,OAAO,MAAM;IACf;IACA,IAAI,KAAK,MAAM,CAAC,OAAO,KAAK,AAAC,QAAQ,OAAO,YAAY,CAAC,IAAM,SAAS;SACnE;QACH,QAAQ,KAAK,MAAM,CAAC;QACpB,IACE,AAAC,UAAU,OAAO,UAAU,OAAO,UAAU,OAC7C,KAAK,MAAM,CAAC,OAAO,KACnB;YACA,MAAM,MAAM,4BAA4B,KAAK,SAAS,CAAC,GAAG;YAC1D,IAAI,UAAU;gBACZ,SAAS,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC7B;YACF,OAAO,MAAM;QACf;QACA,SAAS;IACX;IAEA,2BAA2B;IAC3B,IAAI,KAAK,MAAM,CAAC,SAAS,KAAK,KAAK;QACjC,MAAM,MAAM;QACZ,IAAI,UAAU;YACZ,SAAS,SAAS,IAAI,CAAC,IAAI,EAAE;YAC7B;QACF,OAAO,MAAM;IACf;IACA,IAAI,KAAK,SAAS,KAAK,SAAS,CAAC,QAAQ,SAAS,IAAI,MAAM,IAC1D,KAAK,SAAS,KAAK,SAAS,CAAC,SAAS,GAAG,SAAS,IAAI,KACtD,SAAS,KAAK,IACd,YAAY,KAAK,SAAS,CAAC,SAAS,GAAG,SAAS;IAClD,YAAY,SAAS,MAAM,SAAS;IAEpC,IAAI,YAAY,UAAU,WACxB,QAAQ,cAAc,WAAW;IAEnC;;;;;GAKC,GACD,SAAS,OAAO,KAAK;QACnB,IAAI,MAAM,EAAE;QACZ,IAAI,IAAI,CAAC;QACT,IAAI,SAAS,KAAK,IAAI,IAAI,CAAC;QAC3B,IAAI,IAAI,CAAC;QACT,IAAI,SAAS,IAAI,IAAI,IAAI,CAAC;QAC1B,IAAI,IAAI,CAAC,OAAO,QAAQ;QACxB,IAAI,IAAI,CAAC;QACT,IAAI,IAAI,CAAC,cAAc,OAAO,MAAM,MAAM;QAC1C,IAAI,IAAI,CAAC,cAAc,OAAO,OAAO,MAAM,GAAG,IAAI;QAClD,OAAO,IAAI,IAAI,CAAC;IAClB;IAEA,OAAO;IACP,IAAI,OAAO,YAAY,aACrB,OAAO,OAAO,OAAO,WAAW,OAAO;SAEpC;QACH,OACE,WACA,OACA,QACA,SAAU,GAAG,EAAE,KAAK;YAClB,IAAI,KAAK,SAAS,KAAK;iBAClB,SAAS,MAAM,OAAO;QAC7B,GACA;IAEJ;AACF;AASO,SAAS,aAAa,KAAK,EAAE,MAAM;IACxC,OAAO,cAAc,OAAO;AAC9B;AASO,SAAS,aAAa,MAAM,EAAE,MAAM;IACzC,OAAO,cAAc,QAAQ;AAC/B;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "ignoreList": [0], "debugId": null}}]}