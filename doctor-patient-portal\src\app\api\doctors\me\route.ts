import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import Doctor from '@/models/Doctor';
import { authenticateUser } from '@/lib/auth';
import { ApiResponse } from '@/types';
import { DoctorWithUser } from '@/types';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate user
    let user;
    try {
      user = await authenticateUser(request);
    } catch (error) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    // Check if user is a doctor
    if (user.role !== 'doctor') {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Access denied. Only doctors can access this endpoint.',
      }, { status: 403 });
    }

    // Find doctor profile
    const doctor = await Doctor.findOne({ userId: user._id })
      .populate('userId', '-password')
      .lean();

    if (!doctor) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Doctor profile not found',
      }, { status: 404 });
    }

    const doctorWithUser: DoctorWithUser = {
      ...doctor,
      user: doctor.userId as any,
    };

    return NextResponse.json<ApiResponse<DoctorWithUser>>({
      success: true,
      data: doctorWithUser,
    });

  } catch (error) {
    console.error('Get current doctor error:', error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to fetch doctor profile',
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate user
    let user;
    try {
      user = await authenticateUser(request);
    } catch (error) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    // Check if user is a doctor
    if (user.role !== 'doctor') {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Access denied. Only doctors can access this endpoint.',
      }, { status: 403 });
    }

    // Find doctor profile
    const doctor = await Doctor.findOne({ userId: user._id });

    if (!doctor) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Doctor profile not found',
      }, { status: 404 });
    }

    const body = await request.json();
    const {
      specialty,
      bio,
      experience,
      consultationFee,
      location,
      qualifications
    } = body;

    // Update doctor profile
    const updatedDoctor = await Doctor.findByIdAndUpdate(
      doctor._id,
      {
        ...(specialty && { specialty }),
        ...(bio && { bio }),
        ...(experience !== undefined && { experience }),
        ...(consultationFee !== undefined && { consultationFee }),
        ...(location && { location }),
        ...(qualifications && { qualifications })
      },
      { new: true, runValidators: true }
    ).populate('userId', '-password');

    if (!updatedDoctor) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Failed to update doctor profile',
      }, { status: 500 });
    }

    const doctorWithUser: DoctorWithUser = {
      ...updatedDoctor.toObject(),
      user: updatedDoctor.userId as any,
    };

    return NextResponse.json<ApiResponse<DoctorWithUser>>({
      success: true,
      data: doctorWithUser,
      message: 'Doctor profile updated successfully',
    });

  } catch (error) {
    console.error('Update current doctor error:', error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to update doctor profile',
    }, { status: 500 });
  }
}
