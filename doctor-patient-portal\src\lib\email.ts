import nodemailer from 'nodemailer';

// Email service utility using No<PERSON>mail<PERSON> with Gmail SMTP
interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

// Create transporter for Gmail SMTP
function createTransporter() {
  const smtpConfig = {
    service: process.env.EMAIL_SERVICE || 'gmail',
    host: process.env.EMAIL_HOST || process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT || process.env.SMTP_PORT || '587'),
    secure: process.env.EMAIL_SECURE === 'true' || false,
    auth: {
      user: process.env.EMAIL_USER || process.env.SMTP_USER,
      pass: process.env.EMAIL_PASSWORD || process.env.SMTP_PASS,
    },
  };

  console.log('📧 SMTP Configuration:', {
    service: smtpConfig.service,
    host: smtpConfig.host,
    port: smtpConfig.port,
    secure: smtpConfig.secure,
    user: smtpConfig.auth.user ? '***configured***' : 'not configured',
    pass: smtpConfig.auth.pass ? '***configured***' : 'not configured',
  });

  return nodemailer.createTransport(smtpConfig);
}

export async function sendEmail(options: EmailOptions): Promise<boolean> {
  try {
    // Check if email credentials are configured
    if (!process.env.SMTP_USER || !process.env.SMTP_PASS ||
        process.env.SMTP_USER === '<EMAIL>' ||
        process.env.SMTP_PASS === 'your-app-password') {

      console.log('⚠️  Email credentials not configured. Logging email instead:');
      console.log('📧 Email would be sent:');
      console.log('To:', options.to);
      console.log('Subject:', options.subject);
      console.log('Content:', options.text || 'HTML content provided');

      // Return true for development/testing purposes
      return true;
    }

    console.log('📧 Attempting to send email to:', options.to);

    const transporter = createTransporter();

    // Verify connection configuration
    await transporter.verify();
    console.log('✅ SMTP connection verified');

    const mailOptions = {
      from: `"Doctor-Patient Portal" <${process.env.EMAIL_FROM || process.env.SMTP_USER}>`,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Email sent successfully:', result.messageId);

    return true;
  } catch (error) {
    console.error('❌ Email sending failed:', error);

    // Log the email content as fallback
    console.log('📧 Email content (failed to send):');
    console.log('To:', options.to);
    console.log('Subject:', options.subject);
    console.log('Content:', options.text || 'HTML content provided');

    return false;
  }
}

export function generatePasswordResetEmail(resetUrl: string, userName: string): { html: string; text: string } {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Password Reset - Doctor-Patient Portal</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px 20px; }
        .button { 
          display: inline-block; 
          background: #2563eb; 
          color: white; 
          padding: 12px 30px; 
          text-decoration: none; 
          border-radius: 5px; 
          margin: 20px 0; 
        }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🏥 Doctor-Patient Portal</h1>
        </div>
        <div class="content">
          <h2>Password Reset Request</h2>
          <p>Hello ${userName},</p>
          <p>We received a request to reset your password for your Doctor-Patient Portal account.</p>
          <p>Click the button below to reset your password:</p>
          <p style="text-align: center;">
            <a href="${resetUrl}" class="button">Reset Password</a>
          </p>
          <p><strong>This link will expire in 1 hour.</strong></p>
          <p>If you didn't request this password reset, please ignore this email. Your password will remain unchanged.</p>
          <p>For security reasons, please don't share this link with anyone.</p>
          <hr>
          <p>If the button doesn't work, copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #2563eb;">${resetUrl}</p>
        </div>
        <div class="footer">
          <p>© 2024 Doctor-Patient Portal. All rights reserved.</p>
          <p>This is an automated email. Please do not reply to this message.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
    Doctor-Patient Portal - Password Reset Request

    Hello ${userName},

    We received a request to reset your password for your Doctor-Patient Portal account.

    To reset your password, please visit the following link:
    ${resetUrl}

    This link will expire in 1 hour.

    If you didn't request this password reset, please ignore this email. Your password will remain unchanged.

    For security reasons, please don't share this link with anyone.

    © 2024 Doctor-Patient Portal. All rights reserved.
    This is an automated email. Please do not reply to this message.
  `;

  return { html, text };
}

export async function sendPasswordResetEmail(email: string, resetUrl: string): Promise<boolean> {
  const userName = email.split('@')[0]; // Simple name extraction
  const { html, text } = generatePasswordResetEmail(resetUrl, userName);

  return await sendEmail({
    to: email,
    subject: 'Reset Your Password - Doctor-Patient Portal',
    html,
    text,
  });
}
