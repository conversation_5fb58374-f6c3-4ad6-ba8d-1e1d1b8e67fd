import { NextRequest, NextResponse } from 'next/server';
import { jwtVerify } from 'jose';

const protectedRoutes = [
  '/dashboard',
  '/doctor',
  '/patient',
  '/onboarding',
];

const authRoutes = [
  '/login',
  '/register',
];

const redirectRoutes = [
  '/sign-in',
  '/sign-up',
];

export default async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if the route is protected
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.startsWith(route)
  );

  // Check if the route is an auth route
  const isAuthRoute = authRoutes.some(route =>
    pathname.startsWith(route)
  );

  // Check if the route is a redirect route (legacy routes)
  const isRedirectRoute = redirectRoutes.some(route =>
    pathname.startsWith(route)
  );

  // Get token from cookies or Authorization header
  const token = request.cookies.get('authToken')?.value ||
                request.headers.get('Authorization')?.replace('Bearer ', '');

  // Verify token if it exists
  let isAuthenticated = false;
  if (token) {
    try {
      const secret = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key');
      await jwtVerify(token, secret);
      isAuthenticated = true;
    } catch (error) {
      // Token is invalid
      isAuthenticated = false;
      console.log('Middleware: Invalid token detected, user should be redirected to login');
    }
  }

  // Handle redirect routes first (always allow these to load for client-side redirect)
  if (isRedirectRoute) {
    return NextResponse.next();
  }

  // Redirect logic
  if (isProtectedRoute && !isAuthenticated) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  if (isAuthRoute && isAuthenticated) {
    console.log(`Middleware: Redirecting authenticated user from ${pathname} to dashboard`);
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};
