'use client';

import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuthStore, useNotificationStore } from '@/lib/store';
import { NotificationPayload } from '@/lib/socket';
import { toast } from 'sonner';

interface UseSocketReturn {
  socket: Socket | null;
  isConnected: boolean;
  joinAppointment: (appointmentId: string) => void;
  leaveAppointment: (appointmentId: string) => void;
  emitAppointmentUpdate: (data: {
    appointmentId: string;
    status: string;
    recipientId: string;
    message: string;
  }) => void;
  emitNewAppointment: (data: {
    appointmentId: string;
    doctorId: string;
    patientId: string;
    message: string;
  }) => void;
}

export const useSocket = (): UseSocketReturn => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const { user, isAuthenticated } = useAuthStore();
  const { addNotification } = useNotificationStore();
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    if (!isAuthenticated || !user) {
      return;
    }

    // Get token from cookies or localStorage
    const getToken = () => {
      // Check if we're on the client side
      if (typeof window === 'undefined') {
        return '';
      }
      // In a real app, you might get this from a secure cookie
      // For now, we'll assume it's available in the auth store or make an API call
      return localStorage.getItem('token') || '';
    };

    const token = getToken();
    if (!token) {
      console.warn('No token available for Socket.IO connection');
      return;
    }

    // Initialize socket connection
    const socketInstance = io(process.env.NODE_ENV === 'production' 
      ? process.env.NEXT_PUBLIC_SOCKET_URL || '' 
      : 'http://localhost:3000', {
      path: '/api/socket',
      auth: {
        token,
      },
      transports: ['websocket', 'polling'],
    });

    socketRef.current = socketInstance;
    setSocket(socketInstance);

    // Connection event handlers
    socketInstance.on('connect', () => {
      console.log('Connected to Socket.IO server');
      setIsConnected(true);
    });

    socketInstance.on('disconnect', (reason) => {
      console.log('Disconnected from Socket.IO server:', reason);
      setIsConnected(false);
    });

    socketInstance.on('connect_error', (error) => {
      console.error('Socket.IO connection error:', error);
      setIsConnected(false);
    });

    // Welcome message
    socketInstance.on('connected', (data) => {
      console.log('Socket.IO welcome message:', data);
      toast.success('Connected to real-time updates');
    });

    // Notification handler
    socketInstance.on('notification', (notification: NotificationPayload) => {
      console.log('Received notification:', notification);
      
      // Add to notification store
      addNotification(notification);
      
      // Show toast notification
      toast.info(notification.message, {
        duration: 5000,
        action: {
          label: 'View',
          onClick: () => {
            // Navigate to appointment or relevant page
            if (typeof window !== 'undefined') {
              window.location.href = `/appointments/${notification.appointmentId}`;
            }
          },
        },
      });
    });

    // Appointment update handler
    socketInstance.on('appointment_updated', (data) => {
      console.log('Appointment updated:', data);
      
      // You can emit a custom event or update local state here
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('appointmentUpdated', {
          detail: data,
        }));
      }
    });

    // Typing indicators (for future chat feature)
    socketInstance.on('user_typing', (data) => {
      console.log('User typing:', data);
    });

    socketInstance.on('user_stopped_typing', (data) => {
      console.log('User stopped typing:', data);
    });

    // Cleanup on unmount
    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }
      setSocket(null);
      setIsConnected(false);
    };
  }, [isAuthenticated, user, addNotification]);

  // Helper functions
  const joinAppointment = (appointmentId: string) => {
    if (socket) {
      socket.emit('join_appointment', appointmentId);
    }
  };

  const leaveAppointment = (appointmentId: string) => {
    if (socket) {
      socket.emit('leave_appointment', appointmentId);
    }
  };

  const emitAppointmentUpdate = (data: {
    appointmentId: string;
    status: string;
    recipientId: string;
    message: string;
  }) => {
    if (socket) {
      socket.emit('appointment_status_update', data);
    }
  };

  const emitNewAppointment = (data: {
    appointmentId: string;
    doctorId: string;
    patientId: string;
    message: string;
  }) => {
    if (socket) {
      socket.emit('new_appointment', data);
    }
  };

  return {
    socket,
    isConnected,
    joinAppointment,
    leaveAppointment,
    emitAppointmentUpdate,
    emitNewAppointment,
  };
};

// Custom hook for appointment-specific socket events
export const useAppointmentSocket = (appointmentId: string) => {
  const { socket, joinAppointment, leaveAppointment } = useSocket();
  const [appointmentUpdates, setAppointmentUpdates] = useState<any[]>([]);

  useEffect(() => {
    if (socket && appointmentId) {
      joinAppointment(appointmentId);

      const handleAppointmentUpdate = (data: any) => {
        if (data.appointmentId === appointmentId) {
          setAppointmentUpdates(prev => [...prev, data]);
        }
      };

      socket.on('appointment_updated', handleAppointmentUpdate);

      return () => {
        leaveAppointment(appointmentId);
        socket.off('appointment_updated', handleAppointmentUpdate);
      };
    }
  }, [socket, appointmentId, joinAppointment, leaveAppointment]);

  return {
    appointmentUpdates,
  };
};
