{"name": "doctor-patient-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --hostname 0.0.0.0", "dev-local": "next dev --turbopack", "build": "next build", "start": "next start --hostname 0.0.0.0", "lint": "next lint"}, "dependencies": {"@fullcalendar/daygrid": "^6.1.18", "@fullcalendar/interaction": "^6.1.18", "@fullcalendar/react": "^6.1.18", "@fullcalendar/timegrid": "^6.1.18", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@types/multer": "^2.0.0", "@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.0.1", "express": "^5.1.0", "framer-motion": "^12.23.0", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "mongoose": "^8.16.1", "multer": "^2.0.1", "next": "15.3.5", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.6", "svix": "^1.69.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.75", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.19.4", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}