import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';

export interface UserDocument extends Document {
  email: string;
  password: string;
  role: 'doctor' | 'patient';
  isEmailVerified: boolean;
  resetPasswordToken?: string;
  resetPasswordExpiry?: Date;
  emailVerificationToken?: string;
  emailVerificationExpiry?: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}



const UserSchema = new Schema<UserDocument>({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
  },
  password: {
    type: String,
    required: true,
    minlength: 6,
  },
  role: {
    type: String,
    enum: ['doctor', 'patient'],
    required: true,
  },
  isEmailVerified: {
    type: Boolean,
    default: false,
  },
  emailVerificationToken: {
    type: String,
  },
  emailVerificationExpiry: {
    type: Date,
  },
  resetPasswordToken: {
    type: String,
  },
  resetPasswordExpiry: {
    type: Date,
  },
}, {
  timestamps: true,
});

// Hash password before saving
UserSchema.pre('save', async function (next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Compare password method
UserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

// Generate email verification token
UserSchema.methods.generateEmailVerificationToken = function (): string {
  const crypto = require('crypto');
  const token = crypto.randomBytes(32).toString('hex');

  this.emailVerificationToken = token;
  this.emailVerificationExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

  return token;
};

// Generate password reset token
UserSchema.methods.generatePasswordResetToken = function (): string {
  const crypto = require('crypto');
  const token = crypto.randomBytes(32).toString('hex');

  this.resetPasswordToken = token;
  this.resetPasswordExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

  return token;
};

// Verify email verification token
UserSchema.methods.verifyEmailToken = function (token: string): boolean {
  return this.emailVerificationToken === token &&
         this.emailVerificationExpiry &&
         this.emailVerificationExpiry > new Date();
};

// Verify password reset token
UserSchema.methods.verifyPasswordResetToken = function (token: string): boolean {
  return this.resetPasswordToken === token &&
         this.resetPasswordExpiry &&
         this.resetPasswordExpiry > new Date();
};

// Clean JSON output (remove sensitive fields)
UserSchema.methods.toJSON = function () {
  const userObject = this.toObject();
  delete userObject.password;
  delete userObject.resetPasswordToken;
  delete userObject.resetPasswordExpiry;
  delete userObject.emailVerificationToken;
  delete userObject.emailVerificationExpiry;
  delete userObject.__v;
  return userObject;
};

// Indexes for better query performance
// Note: email index is already created by unique: true
UserSchema.index({ role: 1 });

export default mongoose.models.User || mongoose.model<UserDocument>('User', UserSchema);
