import { NextRequest, NextResponse } from 'next/server';
import { sendEmail } from '@/lib/email';

export async function POST(request: NextRequest) {
  try {
    const { to, subject, message } = await request.json();

    if (!to) {
      return NextResponse.json({
        success: false,
        error: 'Recipient email is required',
      }, { status: 400 });
    }

    // Send test email
    const result = await sendEmail({
      to,
      subject: subject || 'Test Email from Doctor <PERSON>',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">Email Configuration Test</h2>
          <p>This is a test email from your Doctor Patient Portal application.</p>
          <p><strong>Message:</strong> ${message || 'Email system is working correctly!'}</p>
          <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin: 0 0 10px 0; color: #2563eb;">Email Configuration Status:</h3>
            <p>✅ SMTP connection successful</p>
            <p>✅ Email delivery working</p>
            <p>✅ HTML formatting supported</p>
          </div>
          <p>If you received this email, your email configuration is working correctly!</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
          <p style="color: #6b7280; font-size: 14px;">
            This is an automated test email from Doctor Patient Portal.
          </p>
        </div>
      `,
    });

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Test email sent successfully',
        data: result,
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Failed to send test email',
        details: result.error,
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Test email error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

// GET endpoint to check email configuration
export async function GET() {
  try {
    const config = {
      service: process.env.EMAIL_SERVICE || 'gmail',
      host: process.env.EMAIL_HOST || process.env.SMTP_HOST || 'smtp.gmail.com',
      port: process.env.EMAIL_PORT || process.env.SMTP_PORT || '587',
      user: process.env.EMAIL_USER || process.env.SMTP_USER,
      from: process.env.EMAIL_FROM || process.env.EMAIL_FROM,
      appUrl: process.env.APP_URL || process.env.FRONTEND_URL || 'http://localhost:3000',
    };

    const status = {
      configured: !!(config.user && (process.env.EMAIL_PASSWORD || process.env.SMTP_PASS)),
      service: config.service,
      host: config.host,
      port: config.port,
      user: config.user ? '***configured***' : 'not configured',
      password: (process.env.EMAIL_PASSWORD || process.env.SMTP_PASS) ? '***configured***' : 'not configured',
      from: config.from,
      appUrl: config.appUrl,
    };

    return NextResponse.json({
      success: true,
      message: 'Email configuration status',
      data: status,
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Failed to check email configuration',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
