import { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';
import User from '@/models/User';

export async function authenticateUser(request: NextRequest) {
  const authHeader = request.headers.get('Authorization');
  const cookieToken = request.cookies.get('authToken')?.value;

  const token = authHeader?.replace('Bearer ', '') || cookieToken;

  if (!token) {
    throw new Error('No authentication token provided');
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
    const user = await User.findById(decoded.userId);

    if (!user) {
      throw new Error('User not found');
    }

    return user;
  } catch (error) {
    throw new Error('Invalid or expired token');
  }
}
