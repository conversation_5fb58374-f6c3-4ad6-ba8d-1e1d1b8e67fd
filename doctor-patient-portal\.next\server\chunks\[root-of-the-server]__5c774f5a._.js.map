{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/api/auth/logout/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { ApiResponse } from '@/types';\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Create response\n    const response = NextResponse.json<ApiResponse>({\n      success: true,\n      data: {\n        message: 'Logged out successfully',\n      },\n    });\n\n    // Clear the auth cookie - always clear it regardless of token presence\n    response.cookies.set('authToken', '', {\n      httpOnly: true,\n      secure: process.env.NODE_ENV === 'production',\n      sameSite: 'strict',\n      maxAge: 0, // Expire immediately\n      path: '/', // Ensure it clears for all paths\n    });\n\n    // Also try to clear any other potential cookie variations\n    response.cookies.delete('authToken');\n\n    return response;\n\n  } catch (error) {\n    console.error('Logout error:', error);\n    return NextResponse.json<ApiResponse>({\n      success: false,\n      error: 'Internal server error',\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,kBAAkB;QAClB,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YAC9C,SAAS;YACT,MAAM;gBACJ,SAAS;YACX;QACF;QAEA,uEAAuE;QACvE,SAAS,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI;YACpC,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ;YACR,MAAM;QACR;QAEA,0DAA0D;QAC1D,SAAS,OAAO,CAAC,MAAM,CAAC;QAExB,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}