'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/components/providers/AuthProvider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function DebugAuthPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const [localStorageToken, setLocalStorageToken] = useState<string | null>(null);
  const [apiResponse, setApiResponse] = useState<any>(null);

  useEffect(() => {
    // Check if we're on the client side
    if (typeof window === 'undefined') {
      return;
    }

    // Check localStorage token
    const token = localStorage.getItem('authToken');
    setLocalStorageToken(token);

    // Test API call
    const testApi = async () => {
      try {
        const response = await fetch('/api/auth/me');
        const data = await response.json();
        setApiResponse({ status: response.status, data });
      } catch (error) {
        setApiResponse({ error: error.message });
      }
    };

    testApi();
  }, []);

  const clearAllAuth = async () => {
    // Check if we're on the client side
    if (typeof window === 'undefined') {
      return;
    }

    try {
      // Call logout API to clear server-side cookie
      await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
        },
      });
    } catch (error) {
      console.error('Error calling logout API:', error);
    }

    // Clear client-side storage
    localStorage.removeItem('authToken');

    // Reload to reset all state
    window.location.href = '/';
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <Card>
          <CardHeader>
            <CardTitle>Authentication Debug Page</CardTitle>
            <CardDescription>
              This page helps diagnose authentication state issues
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* AuthProvider State */}
            <div>
              <h3 className="text-lg font-semibold mb-2">AuthProvider State</h3>
              <div className="bg-gray-100 p-4 rounded-lg">
                <p><strong>isLoading:</strong> {isLoading.toString()}</p>
                <p><strong>isAuthenticated:</strong> {isAuthenticated.toString()}</p>
                <p><strong>user:</strong> {user ? JSON.stringify(user, null, 2) : 'null'}</p>
              </div>
            </div>

            {/* localStorage Token */}
            <div>
              <h3 className="text-lg font-semibold mb-2">localStorage Token</h3>
              <div className="bg-gray-100 p-4 rounded-lg">
                <p><strong>Token exists:</strong> {!!localStorageToken}</p>
                <p><strong>Token preview:</strong> {localStorageToken ? `${localStorageToken.substring(0, 50)}...` : 'null'}</p>
              </div>
            </div>

            {/* API Response */}
            <div>
              <h3 className="text-lg font-semibold mb-2">API /auth/me Response</h3>
              <div className="bg-gray-100 p-4 rounded-lg">
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(apiResponse, null, 2)}
                </pre>
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Actions</h3>
              <div className="flex space-x-4">
                <Button onClick={clearAllAuth} variant="destructive">
                  Clear All Authentication
                </Button>
                <Button onClick={() => window.location.reload()} variant="outline">
                  Reload Page
                </Button>
                <Button onClick={() => window.location.href = '/'} variant="outline">
                  Go to Home
                </Button>
              </div>
            </div>

            {/* Explanation */}
            <div>
              <h3 className="text-lg font-semibold mb-2">Issue Explanation</h3>
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-sm">
                  If you're experiencing the "Sign In redirects to dashboard" issue, it's likely because:
                </p>
                <ul className="list-disc list-inside text-sm mt-2 space-y-1">
                  <li>There's a valid authentication cookie on the server-side (middleware sees you as authenticated)</li>
                  <li>But no token in localStorage on the client-side (AuthProvider sees you as not authenticated)</li>
                  <li>When you click "Sign In", the middleware redirects you to dashboard because it thinks you're authenticated</li>
                  <li>But the client-side doesn't show you as authenticated because localStorage is empty</li>
                </ul>
                <p className="text-sm mt-2">
                  <strong>Solution:</strong> Click "Clear All Authentication" to sync both client and server states.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
