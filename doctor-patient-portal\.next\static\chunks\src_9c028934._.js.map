{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { cn } from '@/lib/utils';\nimport { Button } from '@/components/ui/button';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport {\n  Home,\n  Calendar,\n  Users,\n  User,\n  LogOut,\n  Menu,\n  X,\n  ClipboardList,\n  Search,\n  Bell,\n} from 'lucide-react';\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport { useUIStore } from '@/lib/store';\nimport { toast } from 'sonner';\nimport Logo from '@/components/ui/logo';\n\ninterface SidebarProps {\n  className?: string;\n}\n\nexport default function Sidebar({ className }: SidebarProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const { user, logout } = useAuth();\n  const { sidebarOpen, toggleSidebar } = useUIStore();\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\n\n  const isDoctor = user?.role === 'doctor';\n\n  const doctorNavItems = [\n    {\n      title: 'Dashboard',\n      href: '/doctor/dashboard',\n      icon: Home,\n    },\n    {\n      title: 'Appointments',\n      href: '/doctor/appointments',\n      icon: Calendar,\n    },\n    {\n      title: 'Profile',\n      href: '/doctor/profile',\n      icon: User,\n    },\n  ];\n\n  const patientNavItems = [\n    {\n      title: 'Dashboard',\n      href: '/patient/dashboard',\n      icon: Home,\n    },\n    {\n      title: 'Find Doctors',\n      href: '/doctors',\n      icon: Search,\n    },\n    {\n      title: 'My Appointments',\n      href: '/patient/appointments',\n      icon: Calendar,\n    },\n    {\n      title: 'Profile',\n      href: '/patient/profile',\n      icon: User,\n    },\n  ];\n\n  const navItems = isDoctor ? doctorNavItems : patientNavItems;\n\n  const handleLogout = async () => {\n    setIsLoggingOut(true);\n    try {\n      await logout();\n      toast.success('Logged out successfully');\n    } catch (error) {\n      toast.error('Failed to logout');\n    } finally {\n      setIsLoggingOut(false);\n    }\n  };\n\n  const getInitials = (firstName: string, lastName: string) => {\n    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();\n  };\n\n  const sidebarVariants = {\n    open: {\n      x: 0,\n      transition: {\n        type: 'spring',\n        stiffness: 300,\n        damping: 30,\n      },\n    },\n    closed: {\n      x: '-100%',\n      transition: {\n        type: 'spring',\n        stiffness: 300,\n        damping: 30,\n      },\n    },\n  };\n\n  return (\n    <>\n      {/* Mobile overlay */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n            onClick={toggleSidebar}\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Sidebar */}\n      <motion.aside\n        variants={sidebarVariants}\n        animate={sidebarOpen ? 'open' : 'closed'}\n        className={cn(\n          'fixed left-0 top-0 z-50 h-full w-64 bg-white border-r border-gray-200 shadow-lg lg:relative lg:translate-x-0',\n          className\n        )}\n      >\n        <div className=\"flex h-full flex-col\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"p-2 bg-blue-600 rounded-lg\">\n                <Stethoscope className=\"w-6 h-6 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-lg font-bold text-gray-900\">HealthCare</h1>\n                <p className=\"text-xs text-gray-500\">Portal</p>\n              </div>\n            </div>\n            \n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={toggleSidebar}\n              className=\"lg:hidden\"\n            >\n              <X className=\"w-5 h-5\" />\n            </Button>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 p-4 space-y-2\">\n            {navItems.map((item) => {\n              const isActive = pathname === item.href;\n              const Icon = item.icon;\n\n              return (\n                <motion.div\n                  key={item.href}\n                  whileHover={{ x: 4 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <Button\n                    variant={isActive ? 'default' : 'ghost'}\n                    className={cn(\n                      'w-full justify-start text-left',\n                      isActive\n                        ? 'bg-blue-600 text-white hover:bg-blue-700'\n                        : 'text-gray-700 hover:bg-gray-100'\n                    )}\n                    onClick={() => {\n                      router.push(item.href);\n                      if (typeof window !== 'undefined' && window.innerWidth < 1024) {\n                        toggleSidebar();\n                      }\n                    }}\n                  >\n                    <Icon className=\"w-5 h-5 mr-3\" />\n                    {item.title}\n                  </Button>\n                </motion.div>\n              );\n            })}\n          </nav>\n\n          {/* User Profile */}\n          <div className=\"p-4 border-t border-gray-200\">\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button\n                  variant=\"ghost\"\n                  className=\"w-full justify-start p-2 h-auto\"\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <Avatar className=\"w-10 h-10\">\n                      <AvatarImage\n                        src={user?.profileImage}\n                        alt={`${user?.firstName || 'User'} ${user?.lastName || ''}`}\n                      />\n                      <AvatarFallback className=\"bg-blue-100 text-blue-600 font-semibold\">\n                        {user && getInitials(user.firstName || 'U', user.lastName || 'U')}\n                      </AvatarFallback>\n                    </Avatar>\n\n                    <div className=\"flex-1 text-left\">\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {isDoctor ? 'Dr. ' : ''}{user?.firstName || user?.email?.split('@')[0] || 'User'} {user?.lastName || ''}\n                      </p>\n                      <p className=\"text-xs text-gray-500 capitalize\">\n                        {user?.role || 'User'}\n                      </p>\n                    </div>\n                  </div>\n                </Button>\n              </DropdownMenuTrigger>\n              \n              <DropdownMenuContent align=\"end\" className=\"w-56\">\n                <DropdownMenuItem\n                  onClick={() => router.push(isDoctor ? '/doctor/profile' : '/patient/profile')}\n                >\n                  <User className=\"mr-2 h-4 w-4\" />\n                  Profile\n                </DropdownMenuItem>\n\n                <DropdownMenuSeparator />\n                \n                <DropdownMenuItem\n                  onClick={handleLogout}\n                  disabled={isLoggingOut}\n                  className=\"text-red-600 focus:text-red-600\"\n                >\n                  <LogOut className=\"mr-2 h-4 w-4\" />\n                  {isLoggingOut ? 'Logging out...' : 'Logout'}\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n        </div>\n      </motion.aside>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;;;AA7BA;;;;;;;;;;;;AAoCe,SAAS,QAAQ,EAAE,SAAS,EAAgB;;IACzD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;IAChD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,WAAW,MAAM,SAAS;IAEhC,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,MAAM;YACN,MAAM,sMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;QAChB;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;QACZ;KACD;IAED,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,MAAM;YACN,MAAM,sMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yMAAA,CAAA,SAAM;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;QAChB;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;QACZ;KACD;IAED,MAAM,WAAW,WAAW,iBAAiB;IAE7C,MAAM,eAAe;QACnB,gBAAgB;QAChB,IAAI;YACF,MAAM;YACN,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc,CAAC,WAAmB;QACtC,OAAO,GAAG,UAAU,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,IAAI,CAAC,WAAW;IAClE;IAEA,MAAM,kBAAkB;QACtB,MAAM;YACJ,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;QACA,QAAQ;YACN,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,qBACE;;0BAEE,6LAAC,4LAAA,CAAA,kBAAe;0BACb,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;;;;;;0BAMf,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;gBACX,UAAU;gBACV,SAAS,cAAc,SAAS;gBAChC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gHACA;0BAGF,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAY,WAAU;;;;;;;;;;;sDAEzB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAkC;;;;;;8DAChD,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAIzC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,MAAM,OAAO,KAAK,IAAI;gCAEtB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,YAAY;wCAAE,GAAG;oCAAE;oCACnB,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,WAAW,YAAY;wCAChC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kCACA,WACI,6CACA;wCAEN,SAAS;4CACP,OAAO,IAAI,CAAC,KAAK,IAAI;4CACrB,IAAI,aAAkB,eAAe,OAAO,UAAU,GAAG,MAAM;gDAC7D;4CACF;wCACF;;0DAEA,6LAAC;gDAAK,WAAU;;;;;;4CACf,KAAK,KAAK;;;;;;;mCApBR,KAAK,IAAI;;;;;4BAwBpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDAAC,WAAU;;0EAChB,6LAAC,qIAAA,CAAA,cAAW;gEACV,KAAK,MAAM;gEACX,KAAK,GAAG,MAAM,aAAa,OAAO,CAAC,EAAE,MAAM,YAAY,IAAI;;;;;;0EAE7D,6LAAC,qIAAA,CAAA,iBAAc;gEAAC,WAAU;0EACvB,QAAQ,YAAY,KAAK,SAAS,IAAI,KAAK,KAAK,QAAQ,IAAI;;;;;;;;;;;;kEAIjE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;;oEACV,WAAW,SAAS;oEAAI,MAAM,aAAa,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAAI;oEAAO;oEAAE,MAAM,YAAY;;;;;;;0EAEvG,6LAAC;gEAAE,WAAU;0EACV,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOzB,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DACzC,6LAAC,+IAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,OAAO,IAAI,CAAC,WAAW,oBAAoB;;kEAE1D,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAInC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0DAEtB,6LAAC,+IAAA,CAAA,mBAAgB;gDACf,SAAS;gDACT,UAAU;gDACV,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDACjB,eAAe,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;GAjOwB;;QACP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACH,kJAAA,CAAA,UAAO;QACO,sHAAA,CAAA,aAAU;;;KAJ3B", "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1060, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Menu, Bell, Search } from 'lucide-react';\nimport { useUIStore, useNotificationStore } from '@/lib/store';\n\ninterface HeaderProps {\n  title: string;\n  subtitle?: string;\n}\n\nexport default function Header({ title, subtitle }: HeaderProps) {\n  const { toggleSidebar } = useUIStore();\n  const { notifications, unreadCount, markAsRead } = useNotificationStore();\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleNotificationClick = (index: number) => {\n    markAsRead(index);\n  };\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 px-4 py-3 lg:px-6\">\n      <div className=\"flex items-center justify-between\">\n        {/* Left side */}\n        <div className=\"flex items-center space-x-4\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={toggleSidebar}\n            className=\"lg:hidden\"\n          >\n            <Menu className=\"w-5 h-5\" />\n          </Button>\n\n          <div>\n            <h1 className=\"text-xl font-semibold text-gray-900\">{title}</h1>\n            {subtitle && (\n              <p className=\"text-sm text-gray-600\">{subtitle}</p>\n            )}\n          </div>\n        </div>\n\n        {/* Right side */}\n        <div className=\"flex items-center space-x-3\">\n          {/* Search - Hidden on mobile */}\n          <div className=\"hidden md:flex items-center space-x-2\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n\n          {/* Notifications */}\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n                <Bell className=\"w-5 h-5\" />\n                {unreadCount > 0 && (\n                  <motion.div\n                    initial={{ scale: 0 }}\n                    animate={{ scale: 1 }}\n                    className=\"absolute -top-1 -right-1\"\n                  >\n                    <Badge \n                      variant=\"destructive\" \n                      className=\"h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs\"\n                    >\n                      {unreadCount > 9 ? '9+' : unreadCount}\n                    </Badge>\n                  </motion.div>\n                )}\n              </Button>\n            </DropdownMenuTrigger>\n            \n            <DropdownMenuContent align=\"end\" className=\"w-80\">\n              <div className=\"p-3 border-b border-gray-200\">\n                <h3 className=\"font-semibold text-gray-900\">Notifications</h3>\n                {unreadCount > 0 && (\n                  <p className=\"text-sm text-gray-600\">\n                    You have {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}\n                  </p>\n                )}\n              </div>\n\n              <div className=\"max-h-96 overflow-y-auto\">\n                {notifications.length === 0 ? (\n                  <div className=\"p-4 text-center text-gray-500\">\n                    <Bell className=\"w-8 h-8 mx-auto mb-2 text-gray-300\" />\n                    <p className=\"text-sm\">No notifications yet</p>\n                  </div>\n                ) : (\n                  notifications.slice(0, 10).map((notification, index) => (\n                    <DropdownMenuItem\n                      key={index}\n                      className=\"p-3 cursor-pointer hover:bg-gray-50\"\n                      onClick={() => handleNotificationClick(index)}\n                    >\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-gray-900\">\n                          {notification.message}\n                        </p>\n                        <p className=\"text-xs text-gray-500 mt-1\">\n                          {new Date(notification.timestamp).toLocaleString()}\n                        </p>\n                      </div>\n                      {index < unreadCount && (\n                        <div className=\"w-2 h-2 bg-blue-600 rounded-full ml-2\" />\n                      )}\n                    </DropdownMenuItem>\n                  ))\n                )}\n              </div>\n\n              {notifications.length > 10 && (\n                <div className=\"p-3 border-t border-gray-200 text-center\">\n                  <Button variant=\"ghost\" size=\"sm\" className=\"text-blue-600\">\n                    View all notifications\n                  </Button>\n                </div>\n              )}\n            </DropdownMenuContent>\n          </DropdownMenu>\n\n          {/* Mobile search button */}\n          <Button variant=\"ghost\" size=\"sm\" className=\"md:hidden\">\n            <Search className=\"w-5 h-5\" />\n          </Button>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AACA;;;AAbA;;;;;;;;AAoBe,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAe;;IAC7D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,0BAA0B,CAAC;QAC/B,WAAW;IACb;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAGlB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;gCACpD,0BACC,6LAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;8BAM5C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;;;;;;sCAMhB,6LAAC,+IAAA,CAAA,eAAY;;8CACX,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;;0DAC1C,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,cAAc,mBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,OAAO;gDAAE;gDACpB,SAAS;oDAAE,OAAO;gDAAE;gDACpB,WAAU;0DAEV,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,WAAU;8DAET,cAAc,IAAI,OAAO;;;;;;;;;;;;;;;;;;;;;;8CAOpC,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA8B;;;;;;gDAC3C,cAAc,mBACb,6LAAC;oDAAE,WAAU;;wDAAwB;wDACzB;wDAAY;wDAAqB,gBAAgB,IAAI,MAAM;;;;;;;;;;;;;sDAK3E,6LAAC;4CAAI,WAAU;sDACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAE,WAAU;kEAAU;;;;;;;;;;;uDAGzB,cAAc,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,cAAc,sBAC5C,6LAAC,+IAAA,CAAA,mBAAgB;oDAEf,WAAU;oDACV,SAAS,IAAM,wBAAwB;;sEAEvC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EACV,aAAa,OAAO;;;;;;8EAEvB,6LAAC;oEAAE,WAAU;8EACV,IAAI,KAAK,aAAa,SAAS,EAAE,cAAc;;;;;;;;;;;;wDAGnD,QAAQ,6BACP,6LAAC;4DAAI,WAAU;;;;;;;mDAbZ;;;;;;;;;;wCAoBZ,cAAc,MAAM,GAAG,oBACtB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;sCASpE,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,WAAU;sCAC1C,cAAA,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B;GA9HwB;;QACI,sHAAA,CAAA,aAAU;QACe,sHAAA,CAAA,uBAAoB;;;KAFjD", "debugId": null}}, {"offset": {"line": 1419, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport Sidebar from './Sidebar';\nimport Header from './Header';\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport { useUIStore } from '@/lib/store';\nimport { cn } from '@/lib/utils';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  title: string;\n  subtitle?: string;\n}\n\nexport default function DashboardLayout({\n  children,\n  title,\n  subtitle\n}: DashboardLayoutProps) {\n  const router = useRouter();\n  const { user, isLoading, isAuthenticated } = useAuth();\n  const { sidebarOpen } = useUIStore();\n\n  useEffect(() => {\n    if (isLoading) return;\n\n    if (!isAuthenticated || !user) {\n      router.push('/login');\n      return;\n    }\n  }, [user, isLoading, isAuthenticated, router]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated || !user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar />\n      \n      <div\n        className={cn(\n          'transition-all duration-300 ease-in-out',\n          sidebarOpen ? 'lg:ml-64' : 'lg:ml-0'\n        )}\n      >\n        <Header title={title} subtitle={subtitle} />\n        \n        <main className=\"p-4 lg:p-6\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            {children}\n          </motion.div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAiBe,SAAS,gBAAgB,EACtC,QAAQ,EACR,KAAK,EACL,QAAQ,EACa;;IACrB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;IACnD,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;IAEjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,WAAW;YAEf,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;oCAAG;QAAC;QAAM;QAAW;QAAiB;KAAO;IAE7C,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,mBAAmB,CAAC,MAAM;QAC7B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;;;;;0BAER,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2CACA,cAAc,aAAa;;kCAG7B,6LAAC,yIAAA,CAAA,UAAM;wBAAC,OAAO;wBAAO,UAAU;;;;;;kCAEhC,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;sCAE3B;;;;;;;;;;;;;;;;;;;;;;;AAMb;GA1DwB;;QAKP,qIAAA,CAAA,YAAS;QACqB,kJAAA,CAAA,UAAO;QAC5B,sHAAA,CAAA,aAAU;;;KAPZ", "debugId": null}}, {"offset": {"line": 1569, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/dashboard/AppointmentCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { format } from 'date-fns';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { \n  Calendar, \n  Clock, \n  User, \n  MapPin, \n  FileText, \n  CheckCircle, \n  XCircle, \n  AlertCircle,\n  MoreHorizontal\n} from 'lucide-react';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { AppointmentWithDetails, AppointmentStatus } from '@/types';\nimport { useAuthStore } from '@/lib/store';\nimport { toast } from 'sonner';\n\ninterface AppointmentCardProps {\n  appointment: AppointmentWithDetails;\n  onStatusUpdate?: (appointmentId: string, status: AppointmentStatus) => void;\n  onViewDetails?: (appointment: AppointmentWithDetails) => void;\n}\n\nexport default function AppointmentCard({ \n  appointment, \n  onStatusUpdate, \n  onViewDetails \n}: AppointmentCardProps) {\n  const [isLoading, setIsLoading] = useState(false);\n  const { user } = useAuthStore();\n\n  const getStatusColor = (status: AppointmentStatus) => {\n    switch (status) {\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n      case 'approved':\n        return 'bg-green-100 text-green-800 border-green-200';\n      case 'rejected':\n        return 'bg-red-100 text-red-800 border-red-200';\n      case 'completed':\n        return 'bg-blue-100 text-blue-800 border-blue-200';\n      case 'cancelled':\n        return 'bg-gray-100 text-gray-800 border-gray-200';\n      default:\n        return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n  };\n\n  const getStatusIcon = (status: AppointmentStatus) => {\n    switch (status) {\n      case 'pending':\n        return <AlertCircle className=\"w-4 h-4\" />;\n      case 'approved':\n        return <CheckCircle className=\"w-4 h-4\" />;\n      case 'rejected':\n      case 'cancelled':\n        return <XCircle className=\"w-4 h-4\" />;\n      case 'completed':\n        return <CheckCircle className=\"w-4 h-4\" />;\n      default:\n        return <AlertCircle className=\"w-4 h-4\" />;\n    }\n  };\n\n  const handleStatusUpdate = async (newStatus: AppointmentStatus) => {\n    if (!onStatusUpdate) return;\n\n    setIsLoading(true);\n    try {\n      await onStatusUpdate(appointment._id, newStatus);\n      toast.success(`Appointment ${newStatus} successfully`);\n    } catch (error) {\n      toast.error('Failed to update appointment status');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getInitials = (firstName: string, lastName: string) => {\n    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();\n  };\n\n  const isDoctor = user?.role === 'doctor';\n  const canUpdateStatus = isDoctor && ['pending'].includes(appointment.status);\n  const displayUser = isDoctor ? appointment.patient : appointment.doctor.user;\n  const displayName = isDoctor \n    ? `${appointment.patient.profile.firstName} ${appointment.patient.profile.lastName}`\n    : `Dr. ${appointment.doctor.user.profile.firstName} ${appointment.doctor.user.profile.lastName}`;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n      whileHover={{ y: -2 }}\n    >\n      <Card className=\"transition-all duration-300 hover:shadow-md\">\n        <CardHeader className=\"pb-3\">\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex items-start space-x-3\">\n              <Avatar className=\"w-12 h-12\">\n                <AvatarImage \n                  src={displayUser.profile.profileImage} \n                  alt={displayName}\n                />\n                <AvatarFallback className=\"bg-blue-100 text-blue-600 font-semibold\">\n                  {getInitials(displayUser.profile.firstName, displayUser.profile.lastName)}\n                </AvatarFallback>\n              </Avatar>\n              \n              <div className=\"flex-1 min-w-0\">\n                <h3 className=\"font-semibold text-gray-900 truncate\">\n                  {displayName}\n                </h3>\n                \n                {!isDoctor && (\n                  <p className=\"text-sm text-gray-600\">\n                    {appointment.doctor.specialty}\n                  </p>\n                )}\n                \n                <div className=\"flex items-center mt-1\">\n                  <Badge \n                    variant=\"outline\" \n                    className={`${getStatusColor(appointment.status)} border`}\n                  >\n                    {getStatusIcon(appointment.status)}\n                    <span className=\"ml-1 capitalize\">{appointment.status}</span>\n                  </Badge>\n                </div>\n              </div>\n            </div>\n\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"ghost\" size=\"sm\" className=\"h-8 w-8 p-0\">\n                  <MoreHorizontal className=\"h-4 w-4\" />\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent align=\"end\">\n                <DropdownMenuItem onClick={() => onViewDetails?.(appointment)}>\n                  <FileText className=\"mr-2 h-4 w-4\" />\n                  View Details\n                </DropdownMenuItem>\n                \n                {canUpdateStatus && (\n                  <>\n                    <DropdownMenuItem \n                      onClick={() => handleStatusUpdate('approved')}\n                      disabled={isLoading}\n                    >\n                      <CheckCircle className=\"mr-2 h-4 w-4 text-green-600\" />\n                      Approve\n                    </DropdownMenuItem>\n                    <DropdownMenuItem \n                      onClick={() => handleStatusUpdate('rejected')}\n                      disabled={isLoading}\n                    >\n                      <XCircle className=\"mr-2 h-4 w-4 text-red-600\" />\n                      Reject\n                    </DropdownMenuItem>\n                  </>\n                )}\n                \n                {appointment.status === 'pending' && !isDoctor && (\n                  <DropdownMenuItem \n                    onClick={() => handleStatusUpdate('cancelled')}\n                    disabled={isLoading}\n                  >\n                    <XCircle className=\"mr-2 h-4 w-4 text-red-600\" />\n                    Cancel\n                  </DropdownMenuItem>\n                )}\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n        </CardHeader>\n\n        <CardContent className=\"pt-0\">\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <Calendar className=\"w-4 h-4 mr-2 text-gray-400\" />\n              <span>{format(new Date(appointment.dateTime), 'MMMM d, yyyy')}</span>\n            </div>\n\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <Clock className=\"w-4 h-4 mr-2 text-gray-400\" />\n              <span>{format(new Date(appointment.dateTime), 'h:mm a')}</span>\n            </div>\n\n            {!isDoctor && (\n              <div className=\"flex items-center text-sm text-gray-600\">\n                <MapPin className=\"w-4 h-4 mr-2 text-gray-400\" />\n                <span className=\"truncate\">\n                  {appointment.doctor.location.city}, {appointment.doctor.location.state}\n                </span>\n              </div>\n            )}\n\n            {appointment.symptoms && (\n              <div className=\"mt-3 p-2 bg-gray-50 rounded-md\">\n                <p className=\"text-sm text-gray-600\">\n                  <span className=\"font-medium\">Symptoms: </span>\n                  {appointment.symptoms}\n                </p>\n              </div>\n            )}\n\n            {appointment.notes && (\n              <div className=\"mt-2 p-2 bg-blue-50 rounded-md\">\n                <p className=\"text-sm text-gray-600\">\n                  <span className=\"font-medium\">Notes: </span>\n                  {appointment.notes}\n                </p>\n              </div>\n            )}\n\n            {appointment.diagnosis && (\n              <div className=\"mt-2 p-2 bg-green-50 rounded-md\">\n                <p className=\"text-sm text-gray-600\">\n                  <span className=\"font-medium\">Diagnosis: </span>\n                  {appointment.diagnosis}\n                </p>\n              </div>\n            )}\n\n            {appointment.prescription && (\n              <div className=\"mt-2 p-2 bg-purple-50 rounded-md\">\n                <p className=\"text-sm text-gray-600\">\n                  <span className=\"font-medium\">Prescription: </span>\n                  {appointment.prescription}\n                </p>\n              </div>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAOA;AACA;;;AA5BA;;;;;;;;;;;;AAoCe,SAAS,gBAAgB,EACtC,WAAW,EACX,cAAc,EACd,aAAa,EACQ;;IACrB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,gBAAgB;QAErB,aAAa;QACb,IAAI;YACF,MAAM,eAAe,YAAY,GAAG,EAAE;YACtC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,UAAU,aAAa,CAAC;QACvD,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc,CAAC,WAAmB;QACtC,OAAO,GAAG,UAAU,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,IAAI,CAAC,WAAW;IAClE;IAEA,MAAM,WAAW,MAAM,SAAS;IAChC,MAAM,kBAAkB,YAAY;QAAC;KAAU,CAAC,QAAQ,CAAC,YAAY,MAAM;IAC3E,MAAM,cAAc,WAAW,YAAY,OAAO,GAAG,YAAY,MAAM,CAAC,IAAI;IAC5E,MAAM,cAAc,WAChB,GAAG,YAAY,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,GAClF,CAAC,IAAI,EAAE,YAAY,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;IAElG,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,YAAY;YAAE,GAAG,CAAC;QAAE;kBAEpB,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,qIAAA,CAAA,cAAW;gDACV,KAAK,YAAY,OAAO,CAAC,YAAY;gDACrC,KAAK;;;;;;0DAEP,6LAAC,qIAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,YAAY,YAAY,OAAO,CAAC,SAAS,EAAE,YAAY,OAAO,CAAC,QAAQ;;;;;;;;;;;;kDAI5E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX;;;;;;4CAGF,CAAC,0BACA,6LAAC;gDAAE,WAAU;0DACV,YAAY,MAAM,CAAC,SAAS;;;;;;0DAIjC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,WAAW,GAAG,eAAe,YAAY,MAAM,EAAE,OAAO,CAAC;;wDAExD,cAAc,YAAY,MAAM;sEACjC,6LAAC;4DAAK,WAAU;sEAAmB,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM7D,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,6LAAC,mNAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG9B,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,gBAAgB;;kEAC/C,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAItC,iCACC;;kEACE,6LAAC,+IAAA,CAAA,mBAAgB;wDACf,SAAS,IAAM,mBAAmB;wDAClC,UAAU;;0EAEV,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAgC;;;;;;;kEAGzD,6LAAC,+IAAA,CAAA,mBAAgB;wDACf,SAAS,IAAM,mBAAmB;wDAClC,UAAU;;0EAEV,6LAAC,+MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;4DAA8B;;;;;;;;;4CAMtD,YAAY,MAAM,KAAK,aAAa,CAAC,0BACpC,6LAAC,+IAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,mBAAmB;gDAClC,UAAU;;kEAEV,6LAAC,+MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS7D,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAM,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,YAAY,QAAQ,GAAG;;;;;;;;;;;;0CAGhD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;kDAAM,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,YAAY,QAAQ,GAAG;;;;;;;;;;;;4BAG/C,CAAC,0BACA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;;4CACb,YAAY,MAAM,CAAC,QAAQ,CAAC,IAAI;4CAAC;4CAAG,YAAY,MAAM,CAAC,QAAQ,CAAC,KAAK;;;;;;;;;;;;;4BAK3E,YAAY,QAAQ,kBACnB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;sDACX,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAC7B,YAAY,QAAQ;;;;;;;;;;;;4BAK1B,YAAY,KAAK,kBAChB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;sDACX,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAC7B,YAAY,KAAK;;;;;;;;;;;;4BAKvB,YAAY,SAAS,kBACpB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;sDACX,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAC7B,YAAY,SAAS;;;;;;;;;;;;4BAK3B,YAAY,YAAY,kBACvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;sDACX,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAC7B,YAAY,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C;GAxNwB;;QAML,sHAAA,CAAA,eAAY;;;KANP", "debugId": null}}, {"offset": {"line": 2144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/patient/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport { motion } from 'framer-motion';\nimport { Calendar, Clock, Users, Plus, Search } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport DashboardLayout from '@/components/layout/DashboardLayout';\nimport AppointmentCard from '@/components/dashboard/AppointmentCard';\nimport { AppointmentWithDetails } from '@/types';\nimport { toast } from 'sonner';\n\nexport default function PatientDashboard() {\n  const [upcomingAppointments, setUpcomingAppointments] = useState<AppointmentWithDetails[]>([]);\n  const [recentAppointments, setRecentAppointments] = useState<AppointmentWithDetails[]>([]);\n  const [appointments, setAppointments] = useState<AppointmentWithDetails[]>([]);\n  const [loading, setLoading] = useState(true);\n  const router = useRouter();\n  const { user, isLoading: authLoading, isAuthenticated } = useAuth();\n\n  const fetchAppointments = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/appointments?limit=20');\n      const result = await response.json();\n\n      if (result.success) {\n        const allAppointments = result.data;\n        setAppointments(allAppointments);\n\n        // Separate upcoming and recent appointments\n        const now = new Date();\n        const upcoming = allAppointments.filter((apt: AppointmentWithDetails) => \n          new Date(apt.dateTime) > now && ['pending', 'approved'].includes(apt.status)\n        );\n        const recent = allAppointments.filter((apt: AppointmentWithDetails) => \n          new Date(apt.dateTime) <= now || ['completed', 'cancelled', 'rejected'].includes(apt.status)\n        ).slice(0, 5);\n\n        setUpcomingAppointments(upcoming);\n        setRecentAppointments(recent);\n      } else {\n        toast.error('Failed to fetch appointments');\n      }\n    } catch (error) {\n      toast.error('Failed to fetch appointments');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleStatusUpdate = async (appointmentId: string, status: string) => {\n    try {\n      const response = await fetch(`/api/appointments/${appointmentId}`, {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ status }),\n      });\n\n      if (response.ok) {\n        fetchAppointments(); // Refresh appointments\n      } else {\n        throw new Error('Failed to update appointment');\n      }\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  useEffect(() => {\n    if (authLoading) return;\n\n    if (!isAuthenticated || !user) {\n      router.push('/login');\n      return;\n    }\n\n    if (user.role !== 'patient') {\n      router.push('/dashboard'); // Will redirect to appropriate dashboard\n      return;\n    }\n\n    fetchAppointments();\n  }, [authLoading, isAuthenticated, user, router]);\n\n  // Show loading state while authentication is loading\n  if (authLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading dashboard...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Show loading state while user data is being verified\n  if (!isAuthenticated || !user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Verifying authentication...</p>\n        </div>\n      </div>\n    );\n  }\n\n  const stats = [\n    {\n      title: 'Upcoming Appointments',\n      value: upcomingAppointments.length,\n      icon: Calendar,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-100',\n    },\n    {\n      title: 'Total Appointments',\n      value: appointments.length,\n      icon: Clock,\n      color: 'text-green-600',\n      bgColor: 'bg-green-100',\n    },\n    {\n      title: 'Doctors Consulted',\n      value: new Set(appointments.map(apt => apt.doctorId)).size,\n      icon: Users,\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-100',\n    },\n  ];\n\n  return (\n    <DashboardLayout\n      title={`Welcome back, ${user?.firstName || user?.email?.split('@')[0] || 'User'}!`}\n      subtitle=\"Manage your appointments and health records\"\n    >\n      <div className=\"space-y-6\">\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          {stats.map((stat, index) => (\n            <motion.div\n              key={stat.title}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.3, delay: index * 0.1 }}\n            >\n              <Card>\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">{stat.title}</p>\n                      <p className=\"text-3xl font-bold text-gray-900\">{stat.value}</p>\n                    </div>\n                    <div className={`p-3 rounded-full ${stat.bgColor}`}>\n                      <stat.icon className={`w-6 h-6 ${stat.color}`} />\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Quick Actions */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Quick Actions</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <Button\n                onClick={() => router.push('/doctors')}\n                className=\"h-16 text-left justify-start bg-blue-600 hover:bg-blue-700\"\n              >\n                <Search className=\"w-6 h-6 mr-3\" />\n                <div>\n                  <div className=\"font-semibold\">Find Doctors</div>\n                  <div className=\"text-sm opacity-90\">Search and book appointments</div>\n                </div>\n              </Button>\n              \n              <Button\n                onClick={() => router.push('/patient/appointments')}\n                variant=\"outline\"\n                className=\"h-16 text-left justify-start\"\n              >\n                <Calendar className=\"w-6 h-6 mr-3\" />\n                <div>\n                  <div className=\"font-semibold\">View All Appointments</div>\n                  <div className=\"text-sm text-gray-600\">Manage your appointments</div>\n                </div>\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Upcoming Appointments */}\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between\">\n            <CardTitle>Upcoming Appointments</CardTitle>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => router.push('/doctors')}\n            >\n              <Plus className=\"w-4 h-4 mr-2\" />\n              Book New\n            </Button>\n          </CardHeader>\n          <CardContent>\n            {loading ? (\n              <div className=\"space-y-4\">\n                {Array.from({ length: 3 }).map((_, index) => (\n                  <div key={index} className=\"animate-pulse\">\n                    <div className=\"h-32 bg-gray-200 rounded-lg\"></div>\n                  </div>\n                ))}\n              </div>\n            ) : upcomingAppointments.length === 0 ? (\n              <div className=\"text-center py-8\">\n                <Calendar className=\"w-12 h-12 text-gray-300 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                  No upcoming appointments\n                </h3>\n                <p className=\"text-gray-600 mb-4\">\n                  Book an appointment with a doctor to get started\n                </p>\n                <Button onClick={() => router.push('/doctors')}>\n                  Find Doctors\n                </Button>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {upcomingAppointments.slice(0, 3).map((appointment) => (\n                  <AppointmentCard\n                    key={appointment._id}\n                    appointment={appointment}\n                    onStatusUpdate={handleStatusUpdate}\n                  />\n                ))}\n                {upcomingAppointments.length > 3 && (\n                  <div className=\"text-center pt-4\">\n                    <Button\n                      variant=\"outline\"\n                      onClick={() => router.push('/patient/appointments')}\n                    >\n                      View All Appointments\n                    </Button>\n                  </div>\n                )}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Recent Appointments */}\n        {recentAppointments.length > 0 && (\n          <Card>\n            <CardHeader>\n              <CardTitle>Recent Appointments</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {recentAppointments.map((appointment) => (\n                  <AppointmentCard\n                    key={appointment._id}\n                    appointment={appointment}\n                  />\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        )}\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;;;AAZA;;;;;;;;;;;AAce,SAAS;;IACtB,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IAC7F,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IACzF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IAC7E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,WAAW,WAAW,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;IAEhE,MAAM,oBAAoB;QACxB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,kBAAkB,OAAO,IAAI;gBACnC,gBAAgB;gBAEhB,4CAA4C;gBAC5C,MAAM,MAAM,IAAI;gBAChB,MAAM,WAAW,gBAAgB,MAAM,CAAC,CAAC,MACvC,IAAI,KAAK,IAAI,QAAQ,IAAI,OAAO;wBAAC;wBAAW;qBAAW,CAAC,QAAQ,CAAC,IAAI,MAAM;gBAE7E,MAAM,SAAS,gBAAgB,MAAM,CAAC,CAAC,MACrC,IAAI,KAAK,IAAI,QAAQ,KAAK,OAAO;wBAAC;wBAAa;wBAAa;qBAAW,CAAC,QAAQ,CAAC,IAAI,MAAM,GAC3F,KAAK,CAAC,GAAG;gBAEX,wBAAwB;gBACxB,sBAAsB;YACxB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,OAAO,eAAuB;QACvD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,eAAe,EAAE;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,qBAAqB,uBAAuB;YAC9C,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,aAAa;YAEjB,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,KAAK,IAAI,KAAK,WAAW;gBAC3B,OAAO,IAAI,CAAC,eAAe,yCAAyC;gBACpE;YACF;YAEA;QACF;qCAAG;QAAC;QAAa;QAAiB;QAAM;KAAO;IAE/C,qDAAqD;IACrD,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,uDAAuD;IACvD,IAAI,CAAC,mBAAmB,CAAC,MAAM;QAC7B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO,qBAAqB,MAAM;YAClC,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,aAAa,MAAM;YAC1B,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,IAAI,IAAI,aAAa,GAAG,CAAC,CAAA,MAAO,IAAI,QAAQ,GAAG,IAAI;YAC1D,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;QACX;KACD;IAED,qBACE,6LAAC,kJAAA,CAAA,UAAe;QACd,OAAO,CAAC,cAAc,EAAE,MAAM,aAAa,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC;QAClF,UAAS;kBAET,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;sCAEhD,cAAA,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC,KAAK,KAAK;;;;;;kEAC5D,6LAAC;wDAAE,WAAU;kEAAoC,KAAK,KAAK;;;;;;;;;;;;0DAE7D,6LAAC;gDAAI,WAAW,CAAC,iBAAiB,EAAE,KAAK,OAAO,EAAE;0DAChD,cAAA,6LAAC,KAAK,IAAI;oDAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAbhD,KAAK,KAAK;;;;;;;;;;8BAuBrB,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;;0DAEV,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,6LAAC;wDAAI,WAAU;kEAAqB;;;;;;;;;;;;;;;;;;kDAIxC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,SAAQ;wCACR,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQjD,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;4BAAC,WAAU;;8CACpB,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,OAAO,IAAI,CAAC;;sDAE3B,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAIrC,6LAAC,mIAAA,CAAA,cAAW;sCACT,wBACC,6LAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;wCAAgB,WAAU;kDACzB,cAAA,6LAAC;4CAAI,WAAU;;;;;;uCADP;;;;;;;;;uCAKZ,qBAAqB,MAAM,KAAK,kBAClC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;kDAAa;;;;;;;;;;;qDAKlD,6LAAC;gCAAI,WAAU;;oCACZ,qBAAqB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,4BACrC,6LAAC,qJAAA,CAAA,UAAe;4CAEd,aAAa;4CACb,gBAAgB;2CAFX,YAAY,GAAG;;;;;oCAKvB,qBAAqB,MAAM,GAAG,mBAC7B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAWZ,mBAAmB,MAAM,GAAG,mBAC3B,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;0CACZ,mBAAmB,GAAG,CAAC,CAAC,4BACvB,6LAAC,qJAAA,CAAA,UAAe;wCAEd,aAAa;uCADR,YAAY,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxC;GA5QwB;;QAKP,qIAAA,CAAA,YAAS;QACkC,kJAAA,CAAA,UAAO;;;KAN3C", "debugId": null}}]}