{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/test-profile/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport { useRouter } from 'next/navigation';\nimport { useEffect } from 'react';\n\nexport default function TestProfilePage() {\n  const { user, isLoading, isAuthenticated } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    console.log('TestProfile - Auth state:', { user, isLoading, isAuthenticated });\n  }, [user, isLoading, isAuthenticated]);\n\n  if (isLoading) {\n    return <div>Loading authentication...</div>;\n  }\n\n  if (!isAuthenticated) {\n    return <div>Not authenticated. <button onClick={() => router.push('/login')}>Go to Login</button></div>;\n  }\n\n  return (\n    <div className=\"p-8\">\n      <h1 className=\"text-2xl font-bold mb-4\">Profile Test Page</h1>\n      <div className=\"space-y-2\">\n        <p><strong>User ID:</strong> {user?.id}</p>\n        <p><strong>Email:</strong> {user?.email}</p>\n        <p><strong>Name:</strong> {user?.firstName} {user?.lastName}</p>\n        <p><strong>Role:</strong> {user?.role}</p>\n        <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>\n      </div>\n      \n      <div className=\"mt-6 space-x-4\">\n        <button \n          onClick={() => router.push(user?.role === 'doctor' ? '/doctor/profile' : '/patient/profile')}\n          className=\"bg-blue-500 text-white px-4 py-2 rounded\"\n        >\n          Go to Profile Page\n        </button>\n        <button \n          onClick={() => router.push(user?.role === 'doctor' ? '/doctor/dashboard' : '/patient/dashboard')}\n          className=\"bg-green-500 text-white px-4 py-2 rounded\"\n        >\n          Go to Dashboard\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,6BAA6B;YAAE;YAAM;YAAW;QAAgB;IAC9E,GAAG;QAAC;QAAM;QAAW;KAAgB;IAErC,IAAI,WAAW;QACb,qBAAO,8OAAC;sBAAI;;;;;;IACd;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBAAO,8OAAC;;gBAAI;8BAAmB,8OAAC;oBAAO,SAAS,IAAM,OAAO,IAAI,CAAC;8BAAW;;;;;;;;;;;;IAC/E;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAiB;4BAAE,MAAM;;;;;;;kCACpC,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAe;4BAAE,MAAM;;;;;;;kCAClC,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAc;4BAAE,MAAM;4BAAU;4BAAE,MAAM;;;;;;;kCACnD,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAc;4BAAE,MAAM;;;;;;;kCACjC,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAuB;4BAAE,kBAAkB,QAAQ;;;;;;;;;;;;;0BAGhE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC,MAAM,SAAS,WAAW,oBAAoB;wBACzE,WAAU;kCACX;;;;;;kCAGD,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC,MAAM,SAAS,WAAW,sBAAsB;wBAC3E,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}]}