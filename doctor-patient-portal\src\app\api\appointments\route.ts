import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import connectDB from '@/lib/db';
import Appointment from '@/models/Appointment';
import Doctor from '@/models/Doctor';
import Patient from '@/models/Patient';
import User from '@/models/User';
import { validateRequestBody, validateQueryParams, bookAppointmentSchema, appointmentFilterSchema } from '@/lib/validations';
import { ApiResponse, PaginatedResponse, AppointmentWithDetails } from '@/types';

// Helper function to authenticate user
async function authenticateUser(request: NextRequest) {
  const authHeader = request.headers.get('Authorization');
  const cookieToken = request.cookies.get('authToken')?.value;

  const token = authHeader?.replace('Bearer ', '') || cookieToken;

  if (!token) {
    throw new Error('No authentication token provided');
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
    const user = await User.findById(decoded.userId);

    if (!user) {
      throw new Error('User not found');
    }

    return user;
  } catch (error) {
    throw new Error('Invalid or expired token');
  }
}

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate user
    let user;
    try {
      user = await authenticateUser(request);
    } catch (error) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    const userRole = user.role;

    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const {
      status,
      startDate,
      endDate,
      page,
      limit
    } = validateQueryParams(appointmentFilterSchema, queryParams);

    // Build query based on user role
    const query: any = {};
    
    if (userRole === 'patient') {
      query.patientId = user._id;
    } else if (userRole === 'doctor') {
      // Find doctor profile for this user
      console.log('Looking for doctor profile with userId:', user._id);
      const doctor = await Doctor.findOne({ userId: user._id });
      console.log('Doctor profile found:', !!doctor);

      if (!doctor) {
        console.log('Doctor profile not found for user:', user.email);
        return NextResponse.json<ApiResponse>({
          success: false,
          error: 'Doctor profile not found. Please complete your doctor profile setup.',
        }, { status: 404 });
      }
      query.doctorId = doctor._id;
    }

    if (status) {
      query.status = status;
    }

    if (startDate || endDate) {
      query.dateTime = {};
      if (startDate) query.dateTime.$gte = startDate;
      if (endDate) query.dateTime.$lte = endDate;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get appointments with populated data
    const appointments = await Appointment.find(query)
      .populate('patientId', '-password')
      .populate({
        path: 'doctorId',
        populate: {
          path: 'userId',
          select: '-password'
        }
      })
      .sort({ dateTime: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await Appointment.countDocuments(query);
    const pages = Math.ceil(total / limit);

    // Transform data and fetch patient profiles
    const appointmentsWithDetails: AppointmentWithDetails[] = await Promise.all(
      appointments.map(async (appointment) => {
        // Fetch patient profile from Patient collection
        const patientProfile = await Patient.findOne({ userId: appointment.patientId._id }).lean();

        return {
          ...appointment,
          patient: {
            ...patientProfile,
            user: appointment.patientId as any,
          } as any,
          doctor: appointment.doctorId as any,
        };
      })
    );

    return NextResponse.json<PaginatedResponse<AppointmentWithDetails>>({
      success: true,
      data: appointmentsWithDetails,
      pagination: {
        page,
        limit,
        total,
        pages,
      },
    });

  } catch (error) {
    console.error('Get appointments error:', error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch appointments',
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate user
    let user;
    try {
      user = await authenticateUser(request);
    } catch (error) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    if (user.role !== 'patient') {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Only patients can book appointments',
      }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = validateRequestBody(bookAppointmentSchema, body);

    // Check if doctor exists
    const doctor = await Doctor.findById(validatedData.doctorId);
    if (!doctor) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Doctor not found',
      }, { status: 404 });
    }

    // Check for existing appointment at the same time
    const existingAppointment = await Appointment.findOne({
      doctorId: validatedData.doctorId,
      dateTime: validatedData.dateTime,
      status: { $in: ['pending', 'approved'] }
    });

    if (existingAppointment) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'This time slot is already booked',
      }, { status: 400 });
    }

    // Create appointment
    const appointment = new Appointment({
      patientId: user._id,
      doctorId: validatedData.doctorId,
      dateTime: validatedData.dateTime,
      symptoms: validatedData.symptoms,
      notes: validatedData.notes,
      status: 'pending',
    });

    await appointment.save();

    // Populate the appointment data
    const populatedAppointment = await Appointment.findById(appointment._id)
      .populate('patientId', '-password')
      .populate({
        path: 'doctorId',
        populate: {
          path: 'userId',
          select: '-password'
        }
      });

    // TODO: Emit Socket.io event for new appointment
    // This would be implemented with a proper Socket.io server setup
    // For now, we'll add a comment as a placeholder

    return NextResponse.json<ApiResponse<AppointmentWithDetails>>({
      success: true,
      data: {
        ...populatedAppointment!.toObject(),
        patient: populatedAppointment!.patientId as any,
        doctor: populatedAppointment!.doctorId as any,
      },
      message: 'Appointment booked successfully',
    }, { status: 201 });

  } catch (error) {
    console.error('Book appointment error:', error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to book appointment',
    }, { status: 500 });
  }
}
