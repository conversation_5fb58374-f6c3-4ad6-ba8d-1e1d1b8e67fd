# 🌐 Hosting & Network Access Guide

## 📱 **Access from Other Devices (Local Network)**

### **✅ Your Server is Now Accessible!**

Your Next.js server is configured to accept connections from other devices:
- **Local**: `http://localhost:3000`
- **Network**: `http://0.0.0.0:3000`

### **🔍 Find Your IP Address**

**Method 1: Command Line**
```bash
# Windows
ipconfig

# Look for "IPv4 Address" under your active network adapter
# Example: ***********
```

**Method 2: Network Settings**
1. Open **Settings** → **Network & Internet**
2. Click on your active connection (Wi-Fi/Ethernet)
3. Find your **IPv4 address**

### **📲 Access from Other Devices**

**From phones, tablets, other computers on the same network:**
- **URL**: `http://YOUR_IP_ADDRESS:3000`
- **Example**: `http://***********:3000`

**Requirements:**
- ✅ **Same Wi-Fi network** (all devices connected to same router)
- ✅ **Firewall allows** port 3000 (Windows may prompt)
- ✅ **Server running** with `npm run dev`

### **🔥 Windows Firewall Setup**

If other devices can't connect:

1. **Open Windows Defender Firewall**
2. Click **"Allow an app through firewall"**
3. Click **"Change settings"** → **"Allow another app"**
4. Browse to **Node.js** or add port **3000**
5. Check both **Private** and **Public** networks

**Or use Command Prompt (as Administrator):**
```bash
netsh advfirewall firewall add rule name="Node.js Port 3000" dir=in action=allow protocol=TCP localport=3000
```

## 🚀 **Production Hosting Options**

### **Option 1: Vercel (Recommended - Free)**

**✅ Best for Next.js applications**

**Steps:**
1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Deploy**
   ```bash
   cd doctor-patient-portal
   vercel
   ```

3. **Follow prompts:**
   - Link to Vercel account
   - Configure project settings
   - Deploy automatically

**Features:**
- ✅ **Free tier** with generous limits
- ✅ **Automatic deployments** from Git
- ✅ **Global CDN** for fast loading
- ✅ **Custom domains** supported
- ✅ **Environment variables** management
- ✅ **Serverless functions** for API routes

### **Option 2: Netlify (Free)**

**Steps:**
1. **Build the project**
   ```bash
   npm run build
   npm run export  # If using static export
   ```

2. **Deploy via Netlify CLI**
   ```bash
   npm install -g netlify-cli
   netlify deploy --prod --dir=out
   ```

**Features:**
- ✅ **Free tier** available
- ✅ **Git integration**
- ✅ **Form handling**
- ✅ **Edge functions**

### **Option 3: Railway (Easy)**

**Steps:**
1. **Connect GitHub repository**
2. **Deploy automatically**
3. **Configure environment variables**

**Features:**
- ✅ **Simple deployment**
- ✅ **Database hosting**
- ✅ **Custom domains**
- ✅ **Pay-as-you-scale**

### **Option 4: DigitalOcean App Platform**

**Steps:**
1. **Connect repository**
2. **Configure build settings**
3. **Deploy**

**Features:**
- ✅ **Managed hosting**
- ✅ **Auto-scaling**
- ✅ **Database integration**
- ✅ **Starting at $5/month**

### **Option 5: AWS Amplify**

**Steps:**
1. **Connect GitHub repository**
2. **Configure build settings**
3. **Deploy with CI/CD**

**Features:**
- ✅ **AWS integration**
- ✅ **Global CDN**
- ✅ **Custom domains**
- ✅ **Pay-per-use**

## 🗄️ **Database Hosting for Production**

### **MongoDB Atlas (Recommended)**

**✅ Free tier available**

**Steps:**
1. **Create account** at [mongodb.com/atlas](https://mongodb.com/atlas)
2. **Create cluster** (choose free M0)
3. **Get connection string**
4. **Update environment variables**

**Connection String Example:**
```env
MONGODB_URI=mongodb+srv://username:<EMAIL>/doctor-patient-portal?retryWrites=true&w=majority
```

### **Alternative Database Options:**
- **PlanetScale** (MySQL-compatible)
- **Supabase** (PostgreSQL)
- **FaunaDB** (Serverless)

## 📧 **Email Service for Production**

### **SendGrid (Recommended)**

**Steps:**
1. **Create account** at [sendgrid.com](https://sendgrid.com)
2. **Get API key**
3. **Update environment variables**

```env
EMAIL_SERVICE=sendgrid
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_USER=apikey
EMAIL_PASSWORD=your-sendgrid-api-key
EMAIL_FROM=<EMAIL>
```

### **Alternative Email Services:**
- **Mailgun** - Developer-friendly
- **Amazon SES** - Cost-effective
- **Postmark** - High deliverability

## 🔧 **Environment Variables for Production**

**Create `.env.production` or configure in hosting platform:**

```env
# Database
MONGODB_URI=your-production-mongodb-uri

# Authentication
JWT_SECRET=your-super-secure-production-jwt-secret

# Email
EMAIL_SERVICE=sendgrid
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_USER=apikey
EMAIL_PASSWORD=your-sendgrid-api-key
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Doctor Patient Portal

# URLs
APP_URL=https://yourdomain.com
FRONTEND_URL=https://yourdomain.com
```

## 🎯 **Quick Deployment Steps**

### **For Vercel (Easiest):**

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

2. **Deploy with Vercel**
   ```bash
   npx vercel
   ```

3. **Configure environment variables** in Vercel dashboard

4. **Update MongoDB** to allow Vercel IPs

5. **Test production deployment**

### **Domain Setup:**
1. **Buy domain** (Namecheap, GoDaddy, etc.)
2. **Configure DNS** to point to hosting provider
3. **Enable HTTPS** (usually automatic)
4. **Update environment variables** with new domain

## 📊 **Testing Network Access**

### **Test from Another Device:**

1. **Find your computer's IP**: `***********` (example)
2. **On another device**, open browser
3. **Navigate to**: `http://***********:3000`
4. **Should see**: Your doctor-patient portal

### **Troubleshooting:**
- ✅ **Same network**: Both devices on same Wi-Fi
- ✅ **Firewall**: Allow port 3000
- ✅ **Server running**: `npm run dev` active
- ✅ **Correct IP**: Use `ipconfig` to verify

## 🎉 **Summary**

### **Local Network Access:**
- **URL**: `http://YOUR_IP:3000`
- **Works on**: Same Wi-Fi network
- **Perfect for**: Testing with team/family

### **Production Hosting:**
- **Recommended**: Vercel (free, easy)
- **Database**: MongoDB Atlas (free tier)
- **Email**: SendGrid (free tier)
- **Domain**: Optional but professional

**Your doctor-patient portal is now ready for both local testing and production deployment!** 🚀
