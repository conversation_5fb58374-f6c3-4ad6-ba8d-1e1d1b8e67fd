module.exports = {

"[project]/node_modules/jose/dist/webapi/index.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_jose_dist_webapi_47ade9aa._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/jose/dist/webapi/index.js [app-route] (ecmascript)");
    });
});
}}),

};