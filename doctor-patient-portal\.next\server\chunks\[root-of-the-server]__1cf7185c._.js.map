{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/db.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/doctor-patient-portal';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\n// Global is used here to maintain a cached connection across hot reloads in development\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached!.conn) {\n    return cached!.conn;\n  }\n\n  if (!cached!.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached!.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Connected to MongoDB');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached!.conn = await cached!.promise;\n  } catch (e) {\n    cached!.promise = null;\n    throw e;\n  }\n\n  return cached!.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAYA,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAQ,IAAI,EAAE;QAChB,OAAO,OAAQ,IAAI;IACrB;IAEA,IAAI,CAAC,OAAQ,OAAO,EAAE;QACpB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAQ,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YAC1D,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAQ,IAAI,GAAG,MAAM,OAAQ,OAAO;IACtC,EAAE,OAAO,GAAG;QACV,OAAQ,OAAO,GAAG;QAClB,MAAM;IACR;IAEA,OAAO,OAAQ,IAAI;AACrB;uCAEe", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\nexport interface UserDocument extends Document {\n  email: string;\n  password: string;\n  role: 'doctor' | 'patient';\n  isEmailVerified: boolean;\n  resetPasswordToken?: string;\n  resetPasswordExpiry?: Date;\n  emailVerificationToken?: string;\n  emailVerificationExpiry?: Date;\n  comparePassword(candidatePassword: string): Promise<boolean>;\n}\n\n\n\nconst UserSchema = new Schema<UserDocument>({\n  email: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true,\n  },\n  password: {\n    type: String,\n    required: true,\n    minlength: 6,\n  },\n  role: {\n    type: String,\n    enum: ['doctor', 'patient'],\n    required: true,\n  },\n  isEmailVerified: {\n    type: Boolean,\n    default: false,\n  },\n  emailVerificationToken: {\n    type: String,\n  },\n  emailVerificationExpiry: {\n    type: Date,\n  },\n  resetPasswordToken: {\n    type: String,\n  },\n  resetPasswordExpiry: {\n    type: Date,\n  },\n}, {\n  timestamps: true,\n});\n\n// Hash password before saving\nUserSchema.pre('save', async function (next) {\n  if (!this.isModified('password')) return next();\n\n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error as Error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Generate email verification token\nUserSchema.methods.generateEmailVerificationToken = function (): string {\n  const crypto = require('crypto');\n  const token = crypto.randomBytes(32).toString('hex');\n\n  this.emailVerificationToken = token;\n  this.emailVerificationExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours\n\n  return token;\n};\n\n// Generate password reset token\nUserSchema.methods.generatePasswordResetToken = function (): string {\n  const crypto = require('crypto');\n  const token = crypto.randomBytes(32).toString('hex');\n\n  this.resetPasswordToken = token;\n  this.resetPasswordExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour\n\n  return token;\n};\n\n// Verify email verification token\nUserSchema.methods.verifyEmailToken = function (token: string): boolean {\n  return this.emailVerificationToken === token &&\n         this.emailVerificationExpiry &&\n         this.emailVerificationExpiry > new Date();\n};\n\n// Verify password reset token\nUserSchema.methods.verifyPasswordResetToken = function (token: string): boolean {\n  return this.resetPasswordToken === token &&\n         this.resetPasswordExpiry &&\n         this.resetPasswordExpiry > new Date();\n};\n\n// Clean JSON output (remove sensitive fields)\nUserSchema.methods.toJSON = function () {\n  const userObject = this.toObject();\n  delete userObject.password;\n  delete userObject.resetPasswordToken;\n  delete userObject.resetPasswordExpiry;\n  delete userObject.emailVerificationToken;\n  delete userObject.emailVerificationExpiry;\n  delete userObject.__v;\n  return userObject;\n};\n\n// Indexes for better query performance\n// Note: email index is already created by unique: true\nUserSchema.index({ role: 1 });\n\nexport default mongoose.models.User || mongoose.model<UserDocument>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAgBA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAe;IAC1C,OAAO;QACL,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAU;SAAU;QAC3B,UAAU;IACZ;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,wBAAwB;QACtB,MAAM;IACR;IACA,yBAAyB;QACvB,MAAM;IACR;IACA,oBAAoB;QAClB,MAAM;IACR;IACA,qBAAqB;QACnB,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAgB,IAAI;IACzC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAgB,iBAAyB;IAC5E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,oCAAoC;AACpC,WAAW,OAAO,CAAC,8BAA8B,GAAG;IAClD,MAAM;IACN,MAAM,QAAQ,OAAO,WAAW,CAAC,IAAI,QAAQ,CAAC;IAE9C,IAAI,CAAC,sBAAsB,GAAG;IAC9B,IAAI,CAAC,uBAAuB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,OAAO,WAAW;IAEtF,OAAO;AACT;AAEA,gCAAgC;AAChC,WAAW,OAAO,CAAC,0BAA0B,GAAG;IAC9C,MAAM;IACN,MAAM,QAAQ,OAAO,WAAW,CAAC,IAAI,QAAQ,CAAC;IAE9C,IAAI,CAAC,kBAAkB,GAAG;IAC1B,IAAI,CAAC,mBAAmB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,OAAO,SAAS;IAE3E,OAAO;AACT;AAEA,kCAAkC;AAClC,WAAW,OAAO,CAAC,gBAAgB,GAAG,SAAU,KAAa;IAC3D,OAAO,IAAI,CAAC,sBAAsB,KAAK,SAChC,IAAI,CAAC,uBAAuB,IAC5B,IAAI,CAAC,uBAAuB,GAAG,IAAI;AAC5C;AAEA,8BAA8B;AAC9B,WAAW,OAAO,CAAC,wBAAwB,GAAG,SAAU,KAAa;IACnE,OAAO,IAAI,CAAC,kBAAkB,KAAK,SAC5B,IAAI,CAAC,mBAAmB,IACxB,IAAI,CAAC,mBAAmB,GAAG,IAAI;AACxC;AAEA,8CAA8C;AAC9C,WAAW,OAAO,CAAC,MAAM,GAAG;IAC1B,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO,WAAW,kBAAkB;IACpC,OAAO,WAAW,mBAAmB;IACrC,OAAO,WAAW,sBAAsB;IACxC,OAAO,WAAW,uBAAuB;IACzC,OAAO,WAAW,GAAG;IACrB,OAAO;AACT;AAEA,uCAAuC;AACvC,uDAAuD;AACvD,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;uCAEZ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAe,QAAQ", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/email.ts"], "sourcesContent": ["import nodemailer from 'nodemailer';\n\n// Email service utility using No<PERSON>mail<PERSON> with Gmail SMTP\ninterface EmailOptions {\n  to: string;\n  subject: string;\n  html: string;\n  text?: string;\n}\n\n// Create transporter for Gmail SMTP\nfunction createTransporter() {\n  const smtpConfig = {\n    service: process.env.EMAIL_SERVICE || 'gmail',\n    host: process.env.EMAIL_HOST || process.env.SMTP_HOST || 'smtp.gmail.com',\n    port: parseInt(process.env.EMAIL_PORT || process.env.SMTP_PORT || '587'),\n    secure: process.env.EMAIL_SECURE === 'true' || false,\n    auth: {\n      user: process.env.EMAIL_USER || process.env.SMTP_USER,\n      pass: process.env.EMAIL_PASSWORD || process.env.SMTP_PASS,\n    },\n  };\n\n  console.log('📧 SMTP Configuration:', {\n    service: smtpConfig.service,\n    host: smtpConfig.host,\n    port: smtpConfig.port,\n    secure: smtpConfig.secure,\n    user: smtpConfig.auth.user ? '***configured***' : 'not configured',\n    pass: smtpConfig.auth.pass ? '***configured***' : 'not configured',\n  });\n\n  return nodemailer.createTransport(smtpConfig);\n}\n\nexport async function sendEmail(options: EmailOptions): Promise<boolean> {\n  try {\n    // Check if email credentials are configured\n    if (!process.env.SMTP_USER || !process.env.SMTP_PASS ||\n        process.env.SMTP_USER === '<EMAIL>' ||\n        process.env.SMTP_PASS === 'your-app-password') {\n\n      console.log('⚠️  Email credentials not configured. Logging email instead:');\n      console.log('📧 Email would be sent:');\n      console.log('To:', options.to);\n      console.log('Subject:', options.subject);\n      console.log('Content:', options.text || 'HTML content provided');\n\n      // Return true for development/testing purposes\n      return true;\n    }\n\n    console.log('📧 Attempting to send email to:', options.to);\n\n    const transporter = createTransporter();\n\n    // Verify connection configuration\n    await transporter.verify();\n    console.log('✅ SMTP connection verified');\n\n    const mailOptions = {\n      from: `\"Doctor-Patient Portal\" <${process.env.EMAIL_FROM || process.env.SMTP_USER}>`,\n      to: options.to,\n      subject: options.subject,\n      html: options.html,\n      text: options.text,\n    };\n\n    const result = await transporter.sendMail(mailOptions);\n    console.log('✅ Email sent successfully:', result.messageId);\n\n    return true;\n  } catch (error) {\n    console.error('❌ Email sending failed:', error);\n\n    // Log the email content as fallback\n    console.log('📧 Email content (failed to send):');\n    console.log('To:', options.to);\n    console.log('Subject:', options.subject);\n    console.log('Content:', options.text || 'HTML content provided');\n\n    return false;\n  }\n}\n\nexport function generatePasswordResetEmail(resetUrl: string, userName: string): { html: string; text: string } {\n  const html = `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <title>Password Reset - Doctor-Patient Portal</title>\n      <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background: #2563eb; color: white; padding: 20px; text-align: center; }\n        .content { padding: 30px 20px; }\n        .button { \n          display: inline-block; \n          background: #2563eb; \n          color: white; \n          padding: 12px 30px; \n          text-decoration: none; \n          border-radius: 5px; \n          margin: 20px 0; \n        }\n        .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <h1>🏥 Doctor-Patient Portal</h1>\n        </div>\n        <div class=\"content\">\n          <h2>Password Reset Request</h2>\n          <p>Hello ${userName},</p>\n          <p>We received a request to reset your password for your Doctor-Patient Portal account.</p>\n          <p>Click the button below to reset your password:</p>\n          <p style=\"text-align: center;\">\n            <a href=\"${resetUrl}\" class=\"button\">Reset Password</a>\n          </p>\n          <p><strong>This link will expire in 1 hour.</strong></p>\n          <p>If you didn't request this password reset, please ignore this email. Your password will remain unchanged.</p>\n          <p>For security reasons, please don't share this link with anyone.</p>\n          <hr>\n          <p>If the button doesn't work, copy and paste this link into your browser:</p>\n          <p style=\"word-break: break-all; color: #2563eb;\">${resetUrl}</p>\n        </div>\n        <div class=\"footer\">\n          <p>© 2024 Doctor-Patient Portal. All rights reserved.</p>\n          <p>This is an automated email. Please do not reply to this message.</p>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n\n  const text = `\n    Doctor-Patient Portal - Password Reset Request\n\n    Hello ${userName},\n\n    We received a request to reset your password for your Doctor-Patient Portal account.\n\n    To reset your password, please visit the following link:\n    ${resetUrl}\n\n    This link will expire in 1 hour.\n\n    If you didn't request this password reset, please ignore this email. Your password will remain unchanged.\n\n    For security reasons, please don't share this link with anyone.\n\n    © 2024 Doctor-Patient Portal. All rights reserved.\n    This is an automated email. Please do not reply to this message.\n  `;\n\n  return { html, text };\n}\n\nexport async function sendPasswordResetEmail(email: string, resetUrl: string): Promise<boolean> {\n  const userName = email.split('@')[0]; // Simple name extraction\n  const { html, text } = generatePasswordResetEmail(resetUrl, userName);\n\n  return await sendEmail({\n    to: email,\n    subject: 'Reset Your Password - Doctor-Patient Portal',\n    html,\n    text,\n  });\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAUA,oCAAoC;AACpC,SAAS;IACP,MAAM,aAAa;QACjB,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;QACtC,MAAM,QAAQ,GAAG,CAAC,UAAU,IAAI,QAAQ,GAAG,CAAC,SAAS,IAAI;QACzD,MAAM,SAAS,QAAQ,GAAG,CAAC,UAAU,IAAI,QAAQ,GAAG,CAAC,SAAS,IAAI;QAClE,QAAQ,QAAQ,GAAG,CAAC,YAAY,KAAK,UAAU;QAC/C,MAAM;YACJ,MAAM,QAAQ,GAAG,CAAC,UAAU,IAAI,QAAQ,GAAG,CAAC,SAAS;YACrD,MAAM,QAAQ,GAAG,CAAC,cAAc,IAAI,QAAQ,GAAG,CAAC,SAAS;QAC3D;IACF;IAEA,QAAQ,GAAG,CAAC,0BAA0B;QACpC,SAAS,WAAW,OAAO;QAC3B,MAAM,WAAW,IAAI;QACrB,MAAM,WAAW,IAAI;QACrB,QAAQ,WAAW,MAAM;QACzB,MAAM,WAAW,IAAI,CAAC,IAAI,GAAG,qBAAqB;QAClD,MAAM,WAAW,IAAI,CAAC,IAAI,GAAG,qBAAqB;IACpD;IAEA,OAAO,iJAAA,CAAA,UAAU,CAAC,eAAe,CAAC;AACpC;AAEO,eAAe,UAAU,OAAqB;IACnD,IAAI;QACF,4CAA4C;QAC5C,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS,IAChD,QAAQ,GAAG,CAAC,SAAS,KAAK,0BAC1B,QAAQ,GAAG,CAAC,SAAS,KAAK,qBAAqB;YAEjD,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,OAAO,QAAQ,EAAE;YAC7B,QAAQ,GAAG,CAAC,YAAY,QAAQ,OAAO;YACvC,QAAQ,GAAG,CAAC,YAAY,QAAQ,IAAI,IAAI;YAExC,+CAA+C;YAC/C,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,mCAAmC,QAAQ,EAAE;QAEzD,MAAM,cAAc;QAEpB,kCAAkC;QAClC,MAAM,YAAY,MAAM;QACxB,QAAQ,GAAG,CAAC;QAEZ,MAAM,cAAc;YAClB,MAAM,CAAC,yBAAyB,EAAE,QAAQ,GAAG,CAAC,UAAU,IAAI,QAAQ,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;YACpF,IAAI,QAAQ,EAAE;YACd,SAAS,QAAQ,OAAO;YACxB,MAAM,QAAQ,IAAI;YAClB,MAAM,QAAQ,IAAI;QACpB;QAEA,MAAM,SAAS,MAAM,YAAY,QAAQ,CAAC;QAC1C,QAAQ,GAAG,CAAC,8BAA8B,OAAO,SAAS;QAE1D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QAEzC,oCAAoC;QACpC,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,OAAO,QAAQ,EAAE;QAC7B,QAAQ,GAAG,CAAC,YAAY,QAAQ,OAAO;QACvC,QAAQ,GAAG,CAAC,YAAY,QAAQ,IAAI,IAAI;QAExC,OAAO;IACT;AACF;AAEO,SAAS,2BAA2B,QAAgB,EAAE,QAAgB;IAC3E,MAAM,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mBA8BG,EAAE,SAAS;;;;qBAIT,EAAE,SAAS;;;;;;;4DAO4B,EAAE,SAAS;;;;;;;;;EASrE,CAAC;IAED,MAAM,OAAO,CAAC;;;UAGN,EAAE,SAAS;;;;;IAKjB,EAAE,SAAS;;;;;;;;;;EAUb,CAAC;IAED,OAAO;QAAE;QAAM;IAAK;AACtB;AAEO,eAAe,uBAAuB,KAAa,EAAE,QAAgB;IAC1E,MAAM,WAAW,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,yBAAyB;IAC/D,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,2BAA2B,UAAU;IAE5D,OAAO,MAAM,UAAU;QACrB,IAAI;QACJ,SAAS;QACT;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 499, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/api/auth/forgot-password/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/db';\nimport User from '@/models/User';\nimport { sendEmail } from '@/lib/email';\nimport { ApiResponse } from '@/types';\n\nexport async function POST(request: NextRequest) {\n  try {\n    await connectDB();\n\n    const { email } = await request.json();\n\n    if (!email) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Email is required',\n      }, { status: 400 });\n    }\n\n    const user = await User.findOne({ email: email.toLowerCase() });\n\n    if (!user) {\n      // Don't reveal if user exists or not for security\n      return NextResponse.json<ApiResponse>({\n        success: true,\n        data: {\n          message: 'If an account with that email exists, a password reset link has been sent.',\n        },\n      });\n    }\n\n    // Generate password reset token\n    const resetToken = user.generatePasswordResetToken();\n\n    // Save user with validation disabled for password field (in case user has no password)\n    await user.save({ validateBeforeSave: false });\n\n    // Send password reset email\n    try {\n      const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${resetToken}`;\n      \n      await sendEmail({\n        to: user.email,\n        subject: 'Reset Your Password - Doctor Patient Portal',\n        html: `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <h2 style=\"color: #dc2626;\">Password Reset Request</h2>\n            <p>Hi ${user.profile.firstName},</p>\n            <p>We received a request to reset your password for your Doctor Patient Portal account.</p>\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"${resetUrl}\" \n                 style=\"background-color: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;\">\n                Reset Password\n              </a>\n            </div>\n            <p>If the button doesn't work, copy and paste this link into your browser:</p>\n            <p style=\"word-break: break-all; color: #6b7280;\">${resetUrl}</p>\n            <p>This link will expire in 1 hour.</p>\n            <hr style=\"margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;\">\n            <p style=\"color: #6b7280; font-size: 14px;\">\n              If you didn't request this password reset, please ignore this email and your password will remain unchanged.\n            </p>\n          </div>\n        `,\n      });\n      \n      console.log(`✅ Password reset email sent to ${user.email}`);\n    } catch (emailError) {\n      console.error('❌ Failed to send password reset email:', emailError);\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Failed to send password reset email',\n      }, { status: 500 });\n    }\n\n    return NextResponse.json<ApiResponse>({\n      success: true,\n      data: {\n        message: 'If an account with that email exists, a password reset link has been sent.',\n      },\n    });\n\n  } catch (error) {\n    console.error('Forgot password error:', error);\n    return NextResponse.json<ApiResponse>({\n      success: false,\n      error: 'Internal server error',\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEpC,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,OAAO,MAAM,WAAW;QAAG;QAE7D,IAAI,CAAC,MAAM;YACT,kDAAkD;YAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,MAAM;oBACJ,SAAS;gBACX;YACF;QACF;QAEA,gCAAgC;QAChC,MAAM,aAAa,KAAK,0BAA0B;QAElD,uFAAuF;QACvF,MAAM,KAAK,IAAI,CAAC;YAAE,oBAAoB;QAAM;QAE5C,4BAA4B;QAC5B,IAAI;YACF,MAAM,WAAW,GAAG,QAAQ,GAAG,CAAC,YAAY,IAAI,wBAAwB,sBAAsB,EAAE,YAAY;YAE5G,MAAM,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE;gBACd,IAAI,KAAK,KAAK;gBACd,SAAS;gBACT,MAAM,CAAC;;;kBAGG,EAAE,KAAK,OAAO,CAAC,SAAS,CAAC;;;uBAGpB,EAAE,SAAS;;;;;;8DAM4B,EAAE,SAAS;;;;;;;QAOjE,CAAC;YACH;YAEA,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,KAAK,KAAK,EAAE;QAC5D,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,MAAM;gBACJ,SAAS;YACX;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}