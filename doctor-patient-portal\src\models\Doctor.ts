import mongoose, { Document, Schema } from 'mongoose';
import { Doctor as IDoctor, AvailabilitySlot } from '@/types';

export interface DoctorDocument extends Document, Omit<IDoctor, '_id'> {}

const AvailabilitySlotSchema = new Schema<AvailabilitySlot>({
  dayOfWeek: {
    type: Number,
    required: true,
    min: 0,
    max: 6,
  },
  startTime: {
    type: String,
    required: true,
    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
  },
  endTime: {
    type: String,
    required: true,
    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
  },
  isAvailable: {
    type: Boolean,
    default: true,
  },
});

const LocationSchema = new Schema({
  address: {
    type: String,
    required: true,
    trim: true,
  },
  city: {
    type: String,
    required: true,
    trim: true,
  },
  state: {
    type: String,
    required: true,
    trim: true,
  },
  zipCode: {
    type: String,
    required: true,
    trim: true,
  },
});

const DoctorSchema = new Schema<DoctorDocument>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
  },
  firstName: {
    type: String,
    required: true,
    trim: true,
  },
  lastName: {
    type: String,
    required: true,
    trim: true,
  },
  phone: {
    type: String,
    trim: true,
  },
  specialty: {
    type: String,
    required: true,
    trim: true,
  },
  bio: {
    type: String,
    required: true,
    maxlength: 1000,
  },
  experience: {
    type: Number,
    required: true,
    min: 0,
  },
  rating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5,
  },
  totalRatings: {
    type: Number,
    default: 0,
    min: 0,
  },
  profileImage: {
    type: String,
  },
  availability: [AvailabilitySlotSchema],
  consultationFee: {
    type: Number,
    required: true,
    min: 0,
  },
  location: {
    type: LocationSchema,
    required: true,
  },
}, {
  timestamps: true,
});

// Indexes for better query performance
// Note: userId index is already created by unique: true
DoctorSchema.index({ specialty: 1 });
DoctorSchema.index({ rating: -1 });
DoctorSchema.index({ 'location.city': 1 });
DoctorSchema.index({ 'location.state': 1 });

// Compound indexes for common queries
DoctorSchema.index({ specialty: 1, rating: -1 });
DoctorSchema.index({ 'location.city': 1, specialty: 1 });

export default mongoose.models.Doctor || mongoose.model<DoctorDocument>('Doctor', DoctorSchema);
