'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Calendar, Clock, Users, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import DashboardLayout from '@/components/layout/DashboardLayout';
import AppointmentCard from '@/components/dashboard/AppointmentCard';
import { AppointmentWithDetails } from '@/types';
import { useAuth } from '@/components/providers/AuthProvider';
import { toast } from 'sonner';

export default function DoctorDashboard() {
  const [pendingAppointments, setPendingAppointments] = useState<AppointmentWithDetails[]>([]);
  const [todayAppointments, setTodayAppointments] = useState<AppointmentWithDetails[]>([]);
  const [appointments, setAppointments] = useState<AppointmentWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [needsProfileSetup, setNeedsProfileSetup] = useState(false);
  const router = useRouter();
  const { user, isLoading: authLoading, isAuthenticated } = useAuth();

  const fetchAppointments = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/appointments?limit=50');

      // Handle different response types to prevent JSON parsing errors
      let result;
      const contentType = response.headers.get('content-type');

      if (contentType && contentType.includes('application/json')) {
        result = await response.json();
        console.log('Parsed JSON result:', result);
      } else {
        // If response is not JSON (e.g., HTML error page), create error object
        const text = await response.text();
        console.log('Non-JSON response:', text.substring(0, 200));
        result = {
          success: false,
          error: `Server returned non-JSON response (${response.status})`
        };
      }

      if (result.success) {
        const allAppointments = result.data;
        setAppointments(allAppointments);

        // Filter pending appointments
        const pending = allAppointments.filter((apt: AppointmentWithDetails) => 
          apt.status === 'pending'
        );

        // Filter today's appointments
        const today = new Date();
        const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
        
        const todaysAppts = allAppointments.filter((apt: AppointmentWithDetails) => {
          const aptDate = new Date(apt.dateTime);
          return aptDate >= todayStart && aptDate < todayEnd && apt.status === 'approved';
        });

        setPendingAppointments(pending);
        setTodayAppointments(todaysAppts);
      } else {
        console.log('Appointments API failed with result:', result);
        console.log('Result error:', result.error);

        // Check if the error is due to missing doctor profile
        if (result.error && result.error.includes('Doctor profile not found')) {
          console.log('Setting needsProfileSetup to true');
          setNeedsProfileSetup(true);
          return;
        }

        // Show error message for other failures
        const errorMessage = result.error || 'Failed to fetch appointments';
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Fetch appointments error:', error);
      // This catch block handles JSON parsing errors and network errors
      if (error instanceof SyntaxError && error.message.includes('JSON')) {
        toast.error('Server returned invalid response. Please try again.');
        setNeedsProfileSetup(true); // Show profile setup as fallback
      } else {
        toast.error('Failed to fetch appointments');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Always try to fetch appointments if we have any indication of authentication
    // This handles the case where server-side auth works but client-side is still loading
    const shouldFetchAppointments = () => {
      // If auth is still loading, wait
      if (authLoading) return false;

      // If clearly not authenticated, redirect to login
      if (!isAuthenticated && !user) {
        router.push('/login');
        return false;
      }

      // If we have a user but wrong role, redirect
      if (user && user.role !== 'doctor') {
        router.push('/dashboard');
        return false;
      }

      // If we have a doctor user or auth is working server-side, try to fetch
      return true;
    };

    if (shouldFetchAppointments()) {
      fetchAppointments();
    }
  }, [authLoading, isAuthenticated, user, router]);

  // Also try to fetch appointments when the component mounts, regardless of auth state
  // This handles the server-side auth scenario
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!needsProfileSetup && appointments.length === 0) {
        fetchAppointments();
      }
    }, 1000); // Give auth provider time to initialize

    return () => clearTimeout(timer);
  }, []);



  const createDoctorProfile = async () => {
    try {
      const response = await fetch('/api/doctors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
        },
        body: JSON.stringify({
          specialty: 'General Practice', // Note: field name is 'specialty' not 'specialization'
          bio: 'Welcome to my practice. I am dedicated to providing quality healthcare.',
          experience: 0,
          qualifications: [],
          consultationFee: 50, // Default consultation fee
          availability: [],
          location: {
            address: 'To be updated',
            city: 'To be updated',
            state: 'To be updated',
            zipCode: '00000'
          }
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success('Doctor profile created successfully!');
        setNeedsProfileSetup(false);
        fetchAppointments(); // Retry fetching appointments
      } else {
        // Try to parse error response
        let errorMessage = 'Failed to create doctor profile';
        try {
          const errorResult = await response.json();
          errorMessage = errorResult.error || errorMessage;
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError);
        }
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Create doctor profile error:', error);
      toast.error('Failed to create doctor profile');
    }
  };

  const handleStatusUpdate = async (appointmentId: string, status: string) => {
    try {
      const response = await fetch(`/api/appointments/${appointmentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      if (response.ok) {
        fetchAppointments(); // Refresh appointments
      } else {
        throw new Error('Failed to update appointment');
      }
    } catch (error) {
      throw error;
    }
  };

  const getStatusCounts = () => {
    const pending = appointments.filter(apt => apt.status === 'pending').length;
    const approved = appointments.filter(apt => apt.status === 'approved').length;
    const completed = appointments.filter(apt => apt.status === 'completed').length;
    
    return { pending, approved, completed };
  };

  const statusCounts = getStatusCounts();

  const stats = [
    {
      title: 'Pending Requests',
      value: statusCounts.pending,
      icon: AlertCircle,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      title: 'Approved Appointments',
      value: statusCounts.approved,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Total Patients',
      value: new Set(appointments.map(apt => apt.patientId)).size,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Completed Consultations',
      value: statusCounts.completed,
      icon: CheckCircle,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ];

  // Debug current state
  console.log('DoctorDashboard render - needsProfileSetup:', needsProfileSetup, 'authLoading:', authLoading, 'isAuthenticated:', isAuthenticated);

  // Show profile setup if needed
  const shouldShowProfileSetup = needsProfileSetup;

  if (shouldShowProfileSetup) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Complete Your Doctor Profile</h2>
            <p className="text-gray-600 mb-6">
              You need to set up your doctor profile to access the dashboard and manage appointments.
            </p>
            <button
              onClick={createDoctorProfile}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              Create Doctor Profile
            </button>
            <p className="text-sm text-gray-500 mt-4">
              You can update your profile details later from the settings page.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Show loading state while authentication is loading
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Show loading state while user data is being verified
  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Verifying authentication...</p>
        </div>
      </div>
    );
  }

  return (
    <DashboardLayout
      title={`Welcome, Dr. ${user?.firstName || 'Doctor'}!`}
      subtitle="Manage your appointments and patient consultations"
    >
      <div className="space-y-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                      <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                    </div>
                    <div className={`p-3 rounded-full ${stat.bgColor}`}>
                      <stat.icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                onClick={() => router.push('/doctor/appointments')}
                className="h-16 text-left justify-start bg-blue-600 hover:bg-blue-700"
              >
                <Calendar className="w-6 h-6 mr-3" />
                <div>
                  <div className="font-semibold">View All Appointments</div>
                  <div className="text-sm opacity-90">Manage your schedule</div>
                </div>
              </Button>
              
              <Button
                onClick={() => router.push('/doctor/profile')}
                variant="outline"
                className="h-16 text-left justify-start"
              >
                <Users className="w-6 h-6 mr-3" />
                <div>
                  <div className="font-semibold">Update Profile</div>
                  <div className="text-sm text-gray-600">Edit your information</div>
                </div>
              </Button>

              <Button
                onClick={() => router.push('/doctor/appointments?status=pending')}
                variant="outline"
                className="h-16 text-left justify-start"
              >
                <AlertCircle className="w-6 h-6 mr-3" />
                <div>
                  <div className="font-semibold">Pending Requests</div>
                  <div className="text-sm text-gray-600">
                    {statusCounts.pending} waiting for approval
                  </div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Pending Appointment Requests */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <span>Pending Requests</span>
                {statusCounts.pending > 0 && (
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                    {statusCounts.pending}
                  </Badge>
                )}
              </CardTitle>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/doctor/appointments?status=pending')}
            >
              View All
            </Button>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="animate-pulse">
                    <div className="h-32 bg-gray-200 rounded-lg"></div>
                  </div>
                ))}
              </div>
            ) : pendingAppointments.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No pending requests
                </h3>
                <p className="text-gray-600">
                  All appointment requests have been reviewed
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {pendingAppointments.slice(0, 3).map((appointment) => (
                  <AppointmentCard
                    key={appointment._id}
                    appointment={appointment}
                    onStatusUpdate={handleStatusUpdate}
                  />
                ))}
                {pendingAppointments.length > 3 && (
                  <div className="text-center pt-4">
                    <Button
                      variant="outline"
                      onClick={() => router.push('/doctor/appointments?status=pending')}
                    >
                      View All Pending Requests
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Today's Appointments */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="w-5 h-5" />
              <span>Today's Schedule</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 2 }).map((_, index) => (
                  <div key={index} className="animate-pulse">
                    <div className="h-32 bg-gray-200 rounded-lg"></div>
                  </div>
                ))}
              </div>
            ) : todayAppointments.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No appointments today
                </h3>
                <p className="text-gray-600">
                  Your schedule is clear for today
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {todayAppointments.map((appointment) => (
                  <AppointmentCard
                    key={appointment._id}
                    appointment={appointment}
                    onStatusUpdate={handleStatusUpdate}
                  />
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
