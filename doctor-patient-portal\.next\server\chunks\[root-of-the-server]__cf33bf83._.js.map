{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/db.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/doctor-patient-portal';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\n// Global is used here to maintain a cached connection across hot reloads in development\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached!.conn) {\n    return cached!.conn;\n  }\n\n  if (!cached!.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached!.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Connected to MongoDB');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached!.conn = await cached!.promise;\n  } catch (e) {\n    cached!.promise = null;\n    throw e;\n  }\n\n  return cached!.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAYA,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAQ,IAAI,EAAE;QAChB,OAAO,OAAQ,IAAI;IACrB;IAEA,IAAI,CAAC,OAAQ,OAAO,EAAE;QACpB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAQ,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YAC1D,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAQ,IAAI,GAAG,MAAM,OAAQ,OAAO;IACtC,EAAE,OAAO,GAAG;QACV,OAAQ,OAAO,GAAG;QAClB,MAAM;IACR;IAEA,OAAO,OAAQ,IAAI;AACrB;uCAEe", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/Appointment.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { Appointment as IAppointment, AppointmentStatus } from '@/types';\n\nexport interface AppointmentDocument extends Document, Omit<IAppointment, '_id'> {}\n\nconst AppointmentSchema = new Schema<AppointmentDocument>({\n  patientId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: true,\n  },\n  doctorId: {\n    type: Schema.Types.ObjectId,\n    ref: 'Doctor',\n    required: true,\n  },\n  dateTime: {\n    type: Date,\n    required: true,\n  },\n  status: {\n    type: String,\n    enum: ['pending', 'approved', 'rejected', 'completed', 'cancelled'],\n    default: 'pending',\n  },\n  notes: {\n    type: String,\n    maxlength: 500,\n  },\n  prescription: {\n    type: String,\n    maxlength: 1000,\n  },\n  symptoms: {\n    type: String,\n    maxlength: 500,\n  },\n  diagnosis: {\n    type: String,\n    maxlength: 1000,\n  },\n}, {\n  timestamps: true,\n});\n\n// Indexes for better query performance\nAppointmentSchema.index({ patientId: 1 });\nAppointmentSchema.index({ doctorId: 1 });\nAppointmentSchema.index({ dateTime: 1 });\nAppointmentSchema.index({ status: 1 });\n\n// Compound indexes for common queries\nAppointmentSchema.index({ patientId: 1, status: 1 });\nAppointmentSchema.index({ doctorId: 1, status: 1 });\nAppointmentSchema.index({ doctorId: 1, dateTime: 1 });\nAppointmentSchema.index({ patientId: 1, dateTime: -1 });\n\n// Prevent double booking - same doctor, same time\nAppointmentSchema.index(\n  { doctorId: 1, dateTime: 1 },\n  { \n    unique: true,\n    partialFilterExpression: { \n      status: { $in: ['pending', 'approved'] } \n    }\n  }\n);\n\nexport default mongoose.models.Appointment || mongoose.model<AppointmentDocument>('Appointment', AppointmentSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAKA,MAAM,oBAAoB,IAAI,yGAAA,CAAA,SAAM,CAAsB;IACxD,WAAW;QACT,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;IACZ;IACA,UAAU;QACR,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;IACZ;IACA,UAAU;QACR,MAAM;QACN,UAAU;IACZ;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAW;YAAY;YAAY;YAAa;SAAY;QACnE,SAAS;IACX;IACA,OAAO;QACL,MAAM;QACN,WAAW;IACb;IACA,cAAc;QACZ,MAAM;QACN,WAAW;IACb;IACA,UAAU;QACR,MAAM;QACN,WAAW;IACb;IACA,WAAW;QACT,MAAM;QACN,WAAW;IACb;AACF,GAAG;IACD,YAAY;AACd;AAEA,uCAAuC;AACvC,kBAAkB,KAAK,CAAC;IAAE,WAAW;AAAE;AACvC,kBAAkB,KAAK,CAAC;IAAE,UAAU;AAAE;AACtC,kBAAkB,KAAK,CAAC;IAAE,UAAU;AAAE;AACtC,kBAAkB,KAAK,CAAC;IAAE,QAAQ;AAAE;AAEpC,sCAAsC;AACtC,kBAAkB,KAAK,CAAC;IAAE,WAAW;IAAG,QAAQ;AAAE;AAClD,kBAAkB,KAAK,CAAC;IAAE,UAAU;IAAG,QAAQ;AAAE;AACjD,kBAAkB,KAAK,CAAC;IAAE,UAAU;IAAG,UAAU;AAAE;AACnD,kBAAkB,KAAK,CAAC;IAAE,WAAW;IAAG,UAAU,CAAC;AAAE;AAErD,kDAAkD;AAClD,kBAAkB,KAAK,CACrB;IAAE,UAAU;IAAG,UAAU;AAAE,GAC3B;IACE,QAAQ;IACR,yBAAyB;QACvB,QAAQ;YAAE,KAAK;gBAAC;gBAAW;aAAW;QAAC;IACzC;AACF;uCAGa,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,WAAW,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAsB,eAAe", "debugId": null}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/Doctor.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { Doctor as IDoctor, AvailabilitySlot } from '@/types';\n\nexport interface DoctorDocument extends Document, Omit<IDoctor, '_id'> {}\n\nconst AvailabilitySlotSchema = new Schema<AvailabilitySlot>({\n  dayOfWeek: {\n    type: Number,\n    required: true,\n    min: 0,\n    max: 6,\n  },\n  startTime: {\n    type: String,\n    required: true,\n    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,\n  },\n  endTime: {\n    type: String,\n    required: true,\n    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,\n  },\n  isAvailable: {\n    type: Boolean,\n    default: true,\n  },\n});\n\nconst LocationSchema = new Schema({\n  address: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  city: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  state: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  zipCode: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n});\n\nconst DoctorSchema = new Schema<DoctorDocument>({\n  userId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: true,\n    unique: true,\n  },\n  specialty: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  bio: {\n    type: String,\n    required: true,\n    maxlength: 1000,\n  },\n  experience: {\n    type: Number,\n    required: true,\n    min: 0,\n  },\n  rating: {\n    type: Number,\n    default: 0,\n    min: 0,\n    max: 5,\n  },\n  totalRatings: {\n    type: Number,\n    default: 0,\n    min: 0,\n  },\n  profileImage: {\n    type: String,\n  },\n  availability: [AvailabilitySlotSchema],\n  consultationFee: {\n    type: Number,\n    required: true,\n    min: 0,\n  },\n  location: {\n    type: LocationSchema,\n    required: true,\n  },\n}, {\n  timestamps: true,\n});\n\n// Indexes for better query performance\n// Note: userId index is already created by unique: true\nDoctorSchema.index({ specialty: 1 });\nDoctorSchema.index({ rating: -1 });\nDoctorSchema.index({ 'location.city': 1 });\nDoctorSchema.index({ 'location.state': 1 });\n\n// Compound indexes for common queries\nDoctorSchema.index({ specialty: 1, rating: -1 });\nDoctorSchema.index({ 'location.city': 1, specialty: 1 });\n\nexport default mongoose.models.Doctor || mongoose.model<DoctorDocument>('Doctor', DoctorSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAKA,MAAM,yBAAyB,IAAI,yGAAA,CAAA,SAAM,CAAmB;IAC1D,WAAW;QACT,MAAM;QACN,UAAU;QACV,KAAK;QACL,KAAK;IACP;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;AACF;AAEA,MAAM,iBAAiB,IAAI,yGAAA,CAAA,SAAM,CAAC;IAChC,SAAS;QACP,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,MAAM;IACR;AACF;AAEA,MAAM,eAAe,IAAI,yGAAA,CAAA,SAAM,CAAiB;IAC9C,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;QACV,QAAQ;IACV;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,KAAK;QACH,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,YAAY;QACV,MAAM;QACN,UAAU;QACV,KAAK;IACP;IACA,QAAQ;QACN,MAAM;QACN,SAAS;QACT,KAAK;QACL,KAAK;IACP;IACA,cAAc;QACZ,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,cAAc;QACZ,MAAM;IACR;IACA,cAAc;QAAC;KAAuB;IACtC,iBAAiB;QACf,MAAM;QACN,UAAU;QACV,KAAK;IACP;IACA,UAAU;QACR,MAAM;QACN,UAAU;IACZ;AACF,GAAG;IACD,YAAY;AACd;AAEA,uCAAuC;AACvC,wDAAwD;AACxD,aAAa,KAAK,CAAC;IAAE,WAAW;AAAE;AAClC,aAAa,KAAK,CAAC;IAAE,QAAQ,CAAC;AAAE;AAChC,aAAa,KAAK,CAAC;IAAE,iBAAiB;AAAE;AACxC,aAAa,KAAK,CAAC;IAAE,kBAAkB;AAAE;AAEzC,sCAAsC;AACtC,aAAa,KAAK,CAAC;IAAE,WAAW;IAAG,QAAQ,CAAC;AAAE;AAC9C,aAAa,KAAK,CAAC;IAAE,iBAAiB;IAAG,WAAW;AAAE;uCAEvC,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAiB,UAAU", "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport bcrypt from 'bcryptjs';\nimport { User as IUser, UserProfile } from '@/types';\n\nexport interface UserDocument extends Document, Omit<IUser, '_id'> {\n  password: string;\n  resetPasswordToken?: string;\n  resetPasswordExpiry?: Date;\n  isEmailVerified: boolean;\n  emailVerificationToken?: string;\n  emailVerificationExpiry?: Date;\n  comparePassword(candidatePassword: string): Promise<boolean>;\n}\n\nconst EmergencyContactSchema = new Schema({\n  name: {\n    type: String,\n    trim: true,\n  },\n  relationship: {\n    type: String,\n    trim: true,\n  },\n  phone: {\n    type: String,\n    trim: true,\n  },\n});\n\nconst MedicalHistorySchema = new Schema({\n  allergies: {\n    type: String,\n    trim: true,\n  },\n  medications: {\n    type: String,\n    trim: true,\n  },\n  conditions: {\n    type: String,\n    trim: true,\n  },\n  surgeries: {\n    type: String,\n    trim: true,\n  },\n});\n\nconst InsuranceSchema = new Schema({\n  provider: {\n    type: String,\n    trim: true,\n  },\n  policyNumber: {\n    type: String,\n    trim: true,\n  },\n  groupNumber: {\n    type: String,\n    trim: true,\n  },\n});\n\nconst UserProfileSchema = new Schema<UserProfile>({\n  firstName: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  lastName: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  phone: {\n    type: String,\n    trim: true,\n  },\n  profileImage: {\n    type: String,\n  },\n  dateOfBirth: {\n    type: Date,\n  },\n  gender: {\n    type: String,\n    enum: ['male', 'female', 'other', 'prefer-not-to-say'],\n  },\n  address: {\n    type: String,\n    trim: true,\n  },\n  city: {\n    type: String,\n    trim: true,\n  },\n  state: {\n    type: String,\n    trim: true,\n  },\n  zipCode: {\n    type: String,\n    trim: true,\n  },\n  emergencyContact: {\n    type: EmergencyContactSchema,\n  },\n  medicalHistory: {\n    type: MedicalHistorySchema,\n  },\n  insurance: {\n    type: InsuranceSchema,\n  },\n});\n\nconst UserSchema = new Schema<UserDocument>({\n  email: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true,\n  },\n  password: {\n    type: String,\n    required: true,\n    minlength: 6,\n  },\n  role: {\n    type: String,\n    enum: ['doctor', 'patient'],\n    required: true,\n  },\n  profile: {\n    type: UserProfileSchema,\n    required: true,\n  },\n  isEmailVerified: {\n    type: Boolean,\n    default: false,\n  },\n  emailVerificationToken: {\n    type: String,\n  },\n  emailVerificationExpiry: {\n    type: Date,\n  },\n  resetPasswordToken: {\n    type: String,\n  },\n  resetPasswordExpiry: {\n    type: Date,\n  },\n}, {\n  timestamps: true,\n});\n\n// Hash password before saving\nUserSchema.pre('save', async function (next) {\n  if (!this.isModified('password')) return next();\n\n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error as Error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Generate email verification token\nUserSchema.methods.generateEmailVerificationToken = function (): string {\n  const crypto = require('crypto');\n  const token = crypto.randomBytes(32).toString('hex');\n\n  this.emailVerificationToken = token;\n  this.emailVerificationExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours\n\n  return token;\n};\n\n// Generate password reset token\nUserSchema.methods.generatePasswordResetToken = function (): string {\n  const crypto = require('crypto');\n  const token = crypto.randomBytes(32).toString('hex');\n\n  this.resetPasswordToken = token;\n  this.resetPasswordExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour\n\n  return token;\n};\n\n// Verify email verification token\nUserSchema.methods.verifyEmailToken = function (token: string): boolean {\n  return this.emailVerificationToken === token &&\n         this.emailVerificationExpiry &&\n         this.emailVerificationExpiry > new Date();\n};\n\n// Verify password reset token\nUserSchema.methods.verifyPasswordResetToken = function (token: string): boolean {\n  return this.resetPasswordToken === token &&\n         this.resetPasswordExpiry &&\n         this.resetPasswordExpiry > new Date();\n};\n\n// Clean JSON output (remove sensitive fields)\nUserSchema.methods.toJSON = function () {\n  const userObject = this.toObject();\n  delete userObject.password;\n  delete userObject.resetPasswordToken;\n  delete userObject.resetPasswordExpiry;\n  delete userObject.emailVerificationToken;\n  delete userObject.emailVerificationExpiry;\n  delete userObject.__v;\n  return userObject;\n};\n\n// Indexes for better query performance\n// Note: email index is already created by unique: true\nUserSchema.index({ role: 1 });\n\nexport default mongoose.models.User || mongoose.model<UserDocument>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAaA,MAAM,yBAAyB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACxC,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;AACF;AAEA,MAAM,uBAAuB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACtC,WAAW;QACT,MAAM;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,MAAM;IACR;IACA,YAAY;QACV,MAAM;QACN,MAAM;IACR;IACA,WAAW;QACT,MAAM;QACN,MAAM;IACR;AACF;AAEA,MAAM,kBAAkB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACjC,UAAU;QACR,MAAM;QACN,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,MAAM;IACR;AACF;AAEA,MAAM,oBAAoB,IAAI,yGAAA,CAAA,SAAM,CAAc;IAChD,WAAW;QACT,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,cAAc;QACZ,MAAM;IACR;IACA,aAAa;QACX,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAQ;YAAU;YAAS;SAAoB;IACxD;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,kBAAkB;QAChB,MAAM;IACR;IACA,gBAAgB;QACd,MAAM;IACR;IACA,WAAW;QACT,MAAM;IACR;AACF;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAe;IAC1C,OAAO;QACL,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAU;SAAU;QAC3B,UAAU;IACZ;IACA,SAAS;QACP,MAAM;QACN,UAAU;IACZ;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,wBAAwB;QACtB,MAAM;IACR;IACA,yBAAyB;QACvB,MAAM;IACR;IACA,oBAAoB;QAClB,MAAM;IACR;IACA,qBAAqB;QACnB,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAgB,IAAI;IACzC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAgB,iBAAyB;IAC5E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,oCAAoC;AACpC,WAAW,OAAO,CAAC,8BAA8B,GAAG;IAClD,MAAM;IACN,MAAM,QAAQ,OAAO,WAAW,CAAC,IAAI,QAAQ,CAAC;IAE9C,IAAI,CAAC,sBAAsB,GAAG;IAC9B,IAAI,CAAC,uBAAuB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,OAAO,WAAW;IAEtF,OAAO;AACT;AAEA,gCAAgC;AAChC,WAAW,OAAO,CAAC,0BAA0B,GAAG;IAC9C,MAAM;IACN,MAAM,QAAQ,OAAO,WAAW,CAAC,IAAI,QAAQ,CAAC;IAE9C,IAAI,CAAC,kBAAkB,GAAG;IAC1B,IAAI,CAAC,mBAAmB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,OAAO,SAAS;IAE3E,OAAO;AACT;AAEA,kCAAkC;AAClC,WAAW,OAAO,CAAC,gBAAgB,GAAG,SAAU,KAAa;IAC3D,OAAO,IAAI,CAAC,sBAAsB,KAAK,SAChC,IAAI,CAAC,uBAAuB,IAC5B,IAAI,CAAC,uBAAuB,GAAG,IAAI;AAC5C;AAEA,8BAA8B;AAC9B,WAAW,OAAO,CAAC,wBAAwB,GAAG,SAAU,KAAa;IACnE,OAAO,IAAI,CAAC,kBAAkB,KAAK,SAC5B,IAAI,CAAC,mBAAmB,IACxB,IAAI,CAAC,mBAAmB,GAAG,IAAI;AACxC;AAEA,8CAA8C;AAC9C,WAAW,OAAO,CAAC,MAAM,GAAG;IAC1B,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO,WAAW,kBAAkB;IACpC,OAAO,WAAW,mBAAmB;IACrC,OAAO,WAAW,sBAAsB;IACxC,OAAO,WAAW,uBAAuB;IACzC,OAAO,WAAW,GAAG;IACrB,OAAO;AACT;AAEA,uCAAuC;AACvC,uDAAuD;AACvD,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;uCAEZ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAe,QAAQ", "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/validations.ts"], "sourcesContent": ["import { z } from 'zod';\n\n// User validation schemas\nexport const userProfileSchema = z.object({\n  firstName: z.string().min(1, 'First name is required').max(50),\n  lastName: z.string().min(1, 'Last name is required').max(50),\n  phone: z.string().optional(),\n  profileImage: z.string().url().optional(),\n  dateOfBirth: z.date().optional(),\n});\n\nexport const loginSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n});\n\nexport const registerSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n  role: z.enum(['doctor', 'patient']),\n  profile: userProfileSchema,\n  doctorInfo: z.object({\n    specialty: z.string().min(1, 'Specialty is required'),\n    bio: z.string().min(10, 'Bio must be at least 10 characters').max(1000),\n    experience: z.number().min(0, 'Experience cannot be negative'),\n    consultationFee: z.number().min(0, 'Fee cannot be negative'),\n    location: z.object({\n      address: z.string().min(1, 'Address is required'),\n      city: z.string().min(1, 'City is required'),\n      state: z.string().min(1, 'State is required'),\n      zipCode: z.string().min(1, 'Zip code is required'),\n    }),\n  }).optional(),\n});\n\n// Doctor validation schemas\nexport const availabilitySlotSchema = z.object({\n  dayOfWeek: z.number().min(0).max(6),\n  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),\n  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),\n  isAvailable: z.boolean().default(true),\n});\n\nexport const doctorUpdateSchema = z.object({\n  specialty: z.string().min(1).optional(),\n  bio: z.string().min(10).max(1000).optional(),\n  experience: z.number().min(0).optional(),\n  consultationFee: z.number().min(0).optional(),\n  availability: z.array(availabilitySlotSchema).optional(),\n  location: z.object({\n    address: z.string().min(1),\n    city: z.string().min(1),\n    state: z.string().min(1),\n    zipCode: z.string().min(1),\n  }).optional(),\n});\n\n// Appointment validation schemas\nexport const bookAppointmentSchema = z.object({\n  doctorId: z.string().min(1, 'Doctor ID is required'),\n  dateTime: z.date(),\n  symptoms: z.string().max(500).optional(),\n  notes: z.string().max(500).optional(),\n});\n\nexport const updateAppointmentSchema = z.object({\n  status: z.enum(['pending', 'approved', 'rejected', 'completed', 'cancelled']).optional(),\n  notes: z.string().max(500).optional(),\n  prescription: z.string().max(1000).optional(),\n  diagnosis: z.string().max(1000).optional(),\n});\n\n// Search and filter schemas\nexport const searchDoctorsSchema = z.object({\n  specialty: z.string().optional(),\n  location: z.string().optional(),\n  rating: z.number().min(0).max(5).optional(),\n  availability: z.boolean().optional(),\n  sortBy: z.enum(['rating', 'experience', 'fee', 'name']).default('rating'),\n  sortOrder: z.enum(['asc', 'desc']).default('desc'),\n  page: z.number().min(1).default(1),\n  limit: z.number().min(1).max(50).default(10),\n});\n\n// Query parameter validation\nexport const paginationSchema = z.object({\n  page: z.number().min(1).default(1),\n  limit: z.number().min(1).max(50).default(10),\n});\n\nexport const appointmentFilterSchema = z.object({\n  status: z.enum(['pending', 'approved', 'rejected', 'completed', 'cancelled']).optional(),\n  startDate: z.date().optional(),\n  endDate: z.date().optional(),\n  ...paginationSchema.shape,\n});\n\n// Utility function to validate and parse request body\nexport function validateRequestBody<T>(schema: z.ZodSchema<T>, data: unknown): T {\n  const result = schema.safeParse(data);\n  if (!result.success) {\n    throw new Error(`Validation error: ${result.error.errors.map(e => e.message).join(', ')}`);\n  }\n  return result.data;\n}\n\n// Utility function to validate query parameters\nexport function validateQueryParams<T>(schema: z.ZodSchema<T>, params: Record<string, any>): T {\n  // Convert string numbers to actual numbers for query params\n  const processedParams = Object.entries(params).reduce((acc, [key, value]) => {\n    if (value === undefined || value === null || value === '') {\n      return acc;\n    }\n    \n    // Try to convert string numbers to numbers\n    if (typeof value === 'string' && !isNaN(Number(value))) {\n      acc[key] = Number(value);\n    } else if (value === 'true') {\n      acc[key] = true;\n    } else if (value === 'false') {\n      acc[key] = false;\n    } else {\n      acc[key] = value;\n    }\n    \n    return acc;\n  }, {} as Record<string, any>);\n\n  const result = schema.safeParse(processedParams);\n  if (!result.success) {\n    throw new Error(`Query validation error: ${result.error.errors.map(e => e.message).join(', ')}`);\n  }\n  return result.data;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAGO,MAAM,oBAAoB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC;IAC3D,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC;IACzD,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,cAAc,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;IACvC,aAAa,oKAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;AAChC;AAEO,MAAM,cAAc,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEO,MAAM,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,MAAM,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;KAAU;IAClC,SAAS;IACT,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC7B,KAAK,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,sCAAsC,GAAG,CAAC;QAClE,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC9B,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACnC,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACjB,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;YAC3B,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;YACxB,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;YACzB,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC7B;IACF,GAAG,QAAQ;AACb;AAGO,MAAM,yBAAyB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7C,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACjC,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,qCAAqC;IACjE,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,qCAAqC;IAC/D,aAAa,oKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AACnC;AAEO,MAAM,qBAAqB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;IACrC,KAAK,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,QAAQ;IAC1C,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;IACtC,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;IAC3C,cAAc,oKAAA,CAAA,IAAC,CAAC,KAAK,CAAC,wBAAwB,QAAQ;IACtD,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACjB,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;QACxB,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;QACrB,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;QACtB,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC1B,GAAG,QAAQ;AACb;AAGO,MAAM,wBAAwB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,UAAU,oKAAA,CAAA,IAAC,CAAC,IAAI;IAChB,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACtC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;AACrC;AAEO,MAAM,0BAA0B,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,QAAQ,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;QAAY;QAAY;QAAa;KAAY,EAAE,QAAQ;IACtF,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACnC,cAAc,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,QAAQ;IAC3C,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,QAAQ;AAC1C;AAGO,MAAM,sBAAsB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;IACzC,cAAc,oKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IAClC,QAAQ,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;QAAc;QAAO;KAAO,EAAE,OAAO,CAAC;IAChE,WAAW,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;KAAO,EAAE,OAAO,CAAC;IAC3C,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAChC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;AAC3C;AAGO,MAAM,mBAAmB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAChC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;AAC3C;AAEO,MAAM,0BAA0B,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,QAAQ,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;QAAY;QAAY;QAAa;KAAY,EAAE,QAAQ;IACtF,WAAW,oKAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;IAC5B,SAAS,oKAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;IAC1B,GAAG,iBAAiB,KAAK;AAC3B;AAGO,SAAS,oBAAuB,MAAsB,EAAE,IAAa;IAC1E,MAAM,SAAS,OAAO,SAAS,CAAC;IAChC,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO;IAC3F;IACA,OAAO,OAAO,IAAI;AACpB;AAGO,SAAS,oBAAuB,MAAsB,EAAE,MAA2B;IACxF,4DAA4D;IAC5D,MAAM,kBAAkB,OAAO,OAAO,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,MAAM;QACtE,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,OAAO;QACT;QAEA,2CAA2C;QAC3C,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,OAAO,SAAS;YACtD,GAAG,CAAC,IAAI,GAAG,OAAO;QACpB,OAAO,IAAI,UAAU,QAAQ;YAC3B,GAAG,CAAC,IAAI,GAAG;QACb,OAAO,IAAI,UAAU,SAAS;YAC5B,GAAG,CAAC,IAAI,GAAG;QACb,OAAO;YACL,GAAG,CAAC,IAAI,GAAG;QACb;QAEA,OAAO;IACT,GAAG,CAAC;IAEJ,MAAM,SAAS,OAAO,SAAS,CAAC;IAChC,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO;IACjG;IACA,OAAO,OAAO,IAAI;AACpB", "debugId": null}}, {"offset": {"line": 747, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/api/appointments/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport jwt from 'jsonwebtoken';\nimport connectDB from '@/lib/db';\nimport Appointment from '@/models/Appointment';\nimport Doctor from '@/models/Doctor';\nimport User from '@/models/User';\nimport { validateRequestBody, validateQueryParams, bookAppointmentSchema, appointmentFilterSchema } from '@/lib/validations';\nimport { ApiResponse, PaginatedResponse, AppointmentWithDetails } from '@/types';\n\n// Helper function to authenticate user\nasync function authenticateUser(request: NextRequest) {\n  const authHeader = request.headers.get('Authorization');\n  const cookieToken = request.cookies.get('authToken')?.value;\n\n  const token = authHeader?.replace('Bearer ', '') || cookieToken;\n\n  if (!token) {\n    throw new Error('No authentication token provided');\n  }\n\n  try {\n    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;\n    const user = await User.findById(decoded.userId);\n\n    if (!user) {\n      throw new Error('User not found');\n    }\n\n    return user;\n  } catch (error) {\n    throw new Error('Invalid or expired token');\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    await connectDB();\n\n    // Authenticate user\n    let user;\n    try {\n      user = await authenticateUser(request);\n    } catch (error) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Unauthorized',\n      }, { status: 401 });\n    }\n\n    const userRole = user.role;\n\n    const { searchParams } = new URL(request.url);\n    const queryParams = Object.fromEntries(searchParams.entries());\n    \n    const {\n      status,\n      startDate,\n      endDate,\n      page,\n      limit\n    } = validateQueryParams(appointmentFilterSchema, queryParams);\n\n    // Build query based on user role\n    const query: any = {};\n    \n    if (userRole === 'patient') {\n      query.patientId = user._id;\n    } else if (userRole === 'doctor') {\n      // Find doctor profile for this user\n      console.log('Looking for doctor profile with userId:', user._id);\n      const doctor = await Doctor.findOne({ userId: user._id });\n      console.log('Doctor profile found:', !!doctor);\n\n      if (!doctor) {\n        console.log('Doctor profile not found for user:', user.email);\n        return NextResponse.json<ApiResponse>({\n          success: false,\n          error: 'Doctor profile not found. Please complete your doctor profile setup.',\n        }, { status: 404 });\n      }\n      query.doctorId = doctor._id;\n    }\n\n    if (status) {\n      query.status = status;\n    }\n\n    if (startDate || endDate) {\n      query.dateTime = {};\n      if (startDate) query.dateTime.$gte = startDate;\n      if (endDate) query.dateTime.$lte = endDate;\n    }\n\n    // Calculate pagination\n    const skip = (page - 1) * limit;\n\n    // Get appointments with populated data\n    const appointments = await Appointment.find(query)\n      .populate('patientId', '-password')\n      .populate({\n        path: 'doctorId',\n        populate: {\n          path: 'userId',\n          select: '-password'\n        }\n      })\n      .sort({ dateTime: -1 })\n      .skip(skip)\n      .limit(limit)\n      .lean();\n\n    // Get total count for pagination\n    const total = await Appointment.countDocuments(query);\n    const pages = Math.ceil(total / limit);\n\n    // Transform data\n    const appointmentsWithDetails: AppointmentWithDetails[] = appointments.map(appointment => ({\n      ...appointment,\n      patient: appointment.patientId as any,\n      doctor: appointment.doctorId as any,\n    }));\n\n    return NextResponse.json<PaginatedResponse<AppointmentWithDetails>>({\n      success: true,\n      data: appointmentsWithDetails,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages,\n      },\n    });\n\n  } catch (error) {\n    console.error('Get appointments error:', error);\n    \n    return NextResponse.json<ApiResponse>({\n      success: false,\n      error: error instanceof Error ? error.message : 'Failed to fetch appointments',\n    }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    await connectDB();\n\n    // Authenticate user\n    let user;\n    try {\n      user = await authenticateUser(request);\n    } catch (error) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Unauthorized',\n      }, { status: 401 });\n    }\n\n    if (user.role !== 'patient') {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Only patients can book appointments',\n      }, { status: 403 });\n    }\n\n    const body = await request.json();\n    const validatedData = validateRequestBody(bookAppointmentSchema, body);\n\n    // Check if doctor exists\n    const doctor = await Doctor.findById(validatedData.doctorId);\n    if (!doctor) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'Doctor not found',\n      }, { status: 404 });\n    }\n\n    // Check for existing appointment at the same time\n    const existingAppointment = await Appointment.findOne({\n      doctorId: validatedData.doctorId,\n      dateTime: validatedData.dateTime,\n      status: { $in: ['pending', 'approved'] }\n    });\n\n    if (existingAppointment) {\n      return NextResponse.json<ApiResponse>({\n        success: false,\n        error: 'This time slot is already booked',\n      }, { status: 400 });\n    }\n\n    // Create appointment\n    const appointment = new Appointment({\n      patientId: user._id,\n      doctorId: validatedData.doctorId,\n      dateTime: validatedData.dateTime,\n      symptoms: validatedData.symptoms,\n      notes: validatedData.notes,\n      status: 'pending',\n    });\n\n    await appointment.save();\n\n    // Populate the appointment data\n    const populatedAppointment = await Appointment.findById(appointment._id)\n      .populate('patientId', '-password')\n      .populate({\n        path: 'doctorId',\n        populate: {\n          path: 'userId',\n          select: '-password'\n        }\n      });\n\n    // TODO: Emit Socket.io event for new appointment\n    // This would be implemented with a proper Socket.io server setup\n    // For now, we'll add a comment as a placeholder\n\n    return NextResponse.json<ApiResponse<AppointmentWithDetails>>({\n      success: true,\n      data: {\n        ...populatedAppointment!.toObject(),\n        patient: populatedAppointment!.patientId as any,\n        doctor: populatedAppointment!.doctorId as any,\n      },\n      message: 'Appointment booked successfully',\n    }, { status: 201 });\n\n  } catch (error) {\n    console.error('Book appointment error:', error);\n    \n    return NextResponse.json<ApiResponse>({\n      success: false,\n      error: error instanceof Error ? error.message : 'Failed to book appointment',\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAGA,uCAAuC;AACvC,eAAe,iBAAiB,OAAoB;IAClD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,cAAc;IAEtD,MAAM,QAAQ,YAAY,QAAQ,WAAW,OAAO;IAEpD,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;QAC5D,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,QAAQ,MAAM;QAE/C,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,oBAAoB;QACpB,IAAI;QACJ,IAAI;YACF,OAAO,MAAM,iBAAiB;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,WAAW,KAAK,IAAI;QAE1B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,cAAc,OAAO,WAAW,CAAC,aAAa,OAAO;QAE3D,MAAM,EACJ,MAAM,EACN,SAAS,EACT,OAAO,EACP,IAAI,EACJ,KAAK,EACN,GAAG,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD,EAAE,2HAAA,CAAA,0BAAuB,EAAE;QAEjD,iCAAiC;QACjC,MAAM,QAAa,CAAC;QAEpB,IAAI,aAAa,WAAW;YAC1B,MAAM,SAAS,GAAG,KAAK,GAAG;QAC5B,OAAO,IAAI,aAAa,UAAU;YAChC,oCAAoC;YACpC,QAAQ,GAAG,CAAC,2CAA2C,KAAK,GAAG;YAC/D,MAAM,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,OAAO,CAAC;gBAAE,QAAQ,KAAK,GAAG;YAAC;YACvD,QAAQ,GAAG,CAAC,yBAAyB,CAAC,CAAC;YAEvC,IAAI,CAAC,QAAQ;gBACX,QAAQ,GAAG,CAAC,sCAAsC,KAAK,KAAK;gBAC5D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;oBACpC,SAAS;oBACT,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB;YACA,MAAM,QAAQ,GAAG,OAAO,GAAG;QAC7B;QAEA,IAAI,QAAQ;YACV,MAAM,MAAM,GAAG;QACjB;QAEA,IAAI,aAAa,SAAS;YACxB,MAAM,QAAQ,GAAG,CAAC;YAClB,IAAI,WAAW,MAAM,QAAQ,CAAC,IAAI,GAAG;YACrC,IAAI,SAAS,MAAM,QAAQ,CAAC,IAAI,GAAG;QACrC;QAEA,uBAAuB;QACvB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,uCAAuC;QACvC,MAAM,eAAe,MAAM,8HAAA,CAAA,UAAW,CAAC,IAAI,CAAC,OACzC,QAAQ,CAAC,aAAa,aACtB,QAAQ,CAAC;YACR,MAAM;YACN,UAAU;gBACR,MAAM;gBACN,QAAQ;YACV;QACF,GACC,IAAI,CAAC;YAAE,UAAU,CAAC;QAAE,GACpB,IAAI,CAAC,MACL,KAAK,CAAC,OACN,IAAI;QAEP,iCAAiC;QACjC,MAAM,QAAQ,MAAM,8HAAA,CAAA,UAAW,CAAC,cAAc,CAAC;QAC/C,MAAM,QAAQ,KAAK,IAAI,CAAC,QAAQ;QAEhC,iBAAiB;QACjB,MAAM,0BAAoD,aAAa,GAAG,CAAC,CAAA,cAAe,CAAC;gBACzF,GAAG,WAAW;gBACd,SAAS,YAAY,SAAS;gBAC9B,QAAQ,YAAY,QAAQ;YAC9B,CAAC;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAA4C;YAClE,SAAS;YACT,MAAM;YACN,YAAY;gBACV;gBACA;gBACA;gBACA;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,oBAAoB;QACpB,IAAI;QACJ,IAAI;YACF,OAAO,MAAM,iBAAiB;QAChC,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,IAAI,KAAK,IAAI,KAAK,WAAW;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD,EAAE,2HAAA,CAAA,wBAAqB,EAAE;QAEjE,yBAAyB;QACzB,MAAM,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,cAAc,QAAQ;QAC3D,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,kDAAkD;QAClD,MAAM,sBAAsB,MAAM,8HAAA,CAAA,UAAW,CAAC,OAAO,CAAC;YACpD,UAAU,cAAc,QAAQ;YAChC,UAAU,cAAc,QAAQ;YAChC,QAAQ;gBAAE,KAAK;oBAAC;oBAAW;iBAAW;YAAC;QACzC;QAEA,IAAI,qBAAqB;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,qBAAqB;QACrB,MAAM,cAAc,IAAI,8HAAA,CAAA,UAAW,CAAC;YAClC,WAAW,KAAK,GAAG;YACnB,UAAU,cAAc,QAAQ;YAChC,UAAU,cAAc,QAAQ;YAChC,UAAU,cAAc,QAAQ;YAChC,OAAO,cAAc,KAAK;YAC1B,QAAQ;QACV;QAEA,MAAM,YAAY,IAAI;QAEtB,gCAAgC;QAChC,MAAM,uBAAuB,MAAM,8HAAA,CAAA,UAAW,CAAC,QAAQ,CAAC,YAAY,GAAG,EACpE,QAAQ,CAAC,aAAa,aACtB,QAAQ,CAAC;YACR,MAAM;YACN,UAAU;gBACR,MAAM;gBACN,QAAQ;YACV;QACF;QAEF,iDAAiD;QACjD,iEAAiE;QACjE,gDAAgD;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAsC;YAC5D,SAAS;YACT,MAAM;gBACJ,GAAG,qBAAsB,QAAQ,EAAE;gBACnC,SAAS,qBAAsB,SAAS;gBACxC,QAAQ,qBAAsB,QAAQ;YACxC;YACA,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}