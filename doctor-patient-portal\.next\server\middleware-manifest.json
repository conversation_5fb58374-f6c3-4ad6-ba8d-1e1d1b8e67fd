{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_758e4a24._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4db2b2d0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pvy1GiWnl9dxH35kCJNqLxyBSkMc6eTE+5YJ8NcLVqE=", "__NEXT_PREVIEW_MODE_ID": "65370b82f81bd5d724fb2f1b2f52d5af", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d921d740636a336a60e63901c93b3d8969a6d90f88b73243e0ab5fdeb6353f1c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "04f4e107ba48d3c49c9bf9b6b19420fe38dd1774bf89dc0fd3472dd53e2b9e71"}}}, "sortedMiddleware": ["/"], "functions": {}}