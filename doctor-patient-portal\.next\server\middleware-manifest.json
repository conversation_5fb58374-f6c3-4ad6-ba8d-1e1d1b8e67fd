{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_758e4a24._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4db2b2d0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pvy1GiWnl9dxH35kCJNqLxyBSkMc6eTE+5YJ8NcLVqE=", "__NEXT_PREVIEW_MODE_ID": "900932d5752cb315ba81ebd646e62900", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "084df3bf184890abe767c44f26369d474962fa0fc51075e1a01cce54eb8b8c4f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b1d04d5747ed0027ac366287b7963d735b518c999e3ceac489b870c0bc31dfcf"}}}, "sortedMiddleware": ["/"], "functions": {}}