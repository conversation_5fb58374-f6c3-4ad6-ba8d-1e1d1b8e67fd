'use client';

import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Stethoscope } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
  clickable?: boolean;
  variant?: 'default' | 'compact';
}

export default function Logo({ 
  size = 'md', 
  showText = true, 
  className,
  clickable = true,
  variant = 'default'
}: LogoProps) {
  const router = useRouter();

  const sizeClasses = {
    sm: {
      icon: 'w-4 h-4',
      container: 'p-1.5',
      text: 'text-sm',
      subtext: 'text-xs'
    },
    md: {
      icon: 'w-6 h-6',
      container: 'p-2',
      text: 'text-lg',
      subtext: 'text-xs'
    },
    lg: {
      icon: 'w-8 h-8',
      container: 'p-3',
      text: 'text-xl',
      subtext: 'text-sm'
    }
  };

  const handleClick = () => {
    if (clickable) {
      router.push('/');
    }
  };

  const logoContent = (
    <div className={cn(
      'flex items-center space-x-2',
      clickable && 'cursor-pointer group',
      className
    )}>
      <motion.div 
        className={cn(
          'bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg shadow-sm',
          sizeClasses[size].container,
          clickable && 'group-hover:shadow-md transition-shadow duration-200'
        )}
        whileHover={clickable ? { scale: 1.05 } : {}}
        whileTap={clickable ? { scale: 0.95 } : {}}
      >
        <Stethoscope className={cn('text-white', sizeClasses[size].icon)} />
      </motion.div>
      
      {showText && (
        <div className={cn(
          'flex flex-col',
          variant === 'compact' && 'hidden sm:flex'
        )}>
          <h1 className={cn(
            'font-bold text-gray-900 leading-tight',
            sizeClasses[size].text,
            clickable && 'group-hover:text-blue-600 transition-colors duration-200'
          )}>
            HealthCare
          </h1>
          {variant === 'default' && (
            <p className={cn(
              'text-gray-500 leading-tight',
              sizeClasses[size].subtext
            )}>
              Portal
            </p>
          )}
        </div>
      )}
    </div>
  );

  if (clickable) {
    return (
      <motion.div
        onClick={handleClick}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className="inline-block"
      >
        {logoContent}
      </motion.div>
    );
  }

  return logoContent;
}
