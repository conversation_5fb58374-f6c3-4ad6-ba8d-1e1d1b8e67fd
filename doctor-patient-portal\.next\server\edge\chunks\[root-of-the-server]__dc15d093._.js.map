{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { jwtVerify } from 'jose';\n\nconst protectedRoutes = [\n  '/dashboard',\n  '/doctor',\n  '/patient',\n  '/onboarding',\n];\n\nconst authRoutes = [\n  '/login',\n  '/register',\n];\n\nconst redirectRoutes = [\n  '/sign-in',\n  '/sign-up',\n];\n\nexport default async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n\n  // Check if the route is protected\n  const isProtectedRoute = protectedRoutes.some(route =>\n    pathname.startsWith(route)\n  );\n\n  // Check if the route is an auth route\n  const isAuthRoute = authRoutes.some(route =>\n    pathname.startsWith(route)\n  );\n\n  // Check if the route is a redirect route (legacy routes)\n  const isRedirectRoute = redirectRoutes.some(route =>\n    pathname.startsWith(route)\n  );\n\n  // Get token from cookies or Authorization header\n  const token = request.cookies.get('authToken')?.value ||\n                request.headers.get('Authorization')?.replace('Bearer ', '');\n\n  // Verify token if it exists\n  let isAuthenticated = false;\n  if (token) {\n    try {\n      const secret = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key');\n      await jwtVerify(token, secret);\n      isAuthenticated = true;\n    } catch (error) {\n      // Token is invalid\n      isAuthenticated = false;\n      console.log('Middleware: Invalid token detected, user should be redirected to login');\n    }\n  }\n\n  // Handle redirect routes first (always allow these to load for client-side redirect)\n  if (isRedirectRoute) {\n    return NextResponse.next();\n  }\n\n  // Redirect logic\n  if (isProtectedRoute && !isAuthenticated) {\n    return NextResponse.redirect(new URL('/login', request.url));\n  }\n\n  if (isAuthRoute && isAuthenticated) {\n    console.log(`Middleware: Redirecting authenticated user from ${pathname} to dashboard`);\n    return NextResponse.redirect(new URL('/dashboard', request.url));\n  }\n\n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: [\n    // Skip Next.js internals and all static files, unless found in search params\n    '/((?!_next|[^?]*\\\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',\n    // Always run for API routes\n    '/(api|trpc)(.*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;CACD;AAED,MAAM,aAAa;IACjB;IACA;CACD;AAED,MAAM,iBAAiB;IACrB;IACA;CACD;AAEc,eAAe,WAAW,OAAoB;IAC3D,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,kCAAkC;IAClC,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,SAAS,UAAU,CAAC;IAGtB,sCAAsC;IACtC,MAAM,cAAc,WAAW,IAAI,CAAC,CAAA,QAClC,SAAS,UAAU,CAAC;IAGtB,yDAAyD;IACzD,MAAM,kBAAkB,eAAe,IAAI,CAAC,CAAA,QAC1C,SAAS,UAAU,CAAC;IAGtB,iDAAiD;IACjD,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,cAAc,SAClC,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW;IAEvE,4BAA4B;IAC5B,IAAI,kBAAkB;IACtB,IAAI,OAAO;QACT,IAAI;YACF,MAAM,SAAS,IAAI,cAAc,MAAM,CAAC,QAAQ,GAAG,CAAC,UAAU,IAAI;YAClE,MAAM,CAAA,GAAA,+JAAA,CAAA,YAAS,AAAD,EAAE,OAAO;YACvB,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,mBAAmB;YACnB,kBAAkB;YAClB,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,qFAAqF;IACrF,IAAI,iBAAiB;QACnB,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,iBAAiB;IACjB,IAAI,oBAAoB,CAAC,iBAAiB;QACxC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;IAC5D;IAEA,IAAI,eAAe,iBAAiB;QAClC,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,SAAS,aAAa,CAAC;QACtF,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;IAChE;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP,6EAA6E;QAC7E;QACA,4BAA4B;QAC5B;KACD;AACH"}}]}