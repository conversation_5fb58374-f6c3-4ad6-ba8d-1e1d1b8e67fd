import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import connectDB from '@/lib/db';
import User from '@/models/User';
import Patient from '@/models/Patient';
import Doctor from '@/models/Doctor';
import { ApiResponse } from '@/types';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Get token from Authorization header or cookies
    const authHeader = request.headers.get('Authorization');
    const cookieToken = request.cookies.get('authToken')?.value;
    
    const token = authHeader?.replace('Bearer ', '') || cookieToken;

    if (!token) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'No authentication token provided',
      }, { status: 401 });
    }

    // Verify token
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
    } catch (error) {
      console.log('Token verification failed:', error);

      // Clear malformed token cookie
      const response = NextResponse.json<ApiResponse>({
        success: false,
        error: 'Invalid or expired token',
      }, { status: 401 });

      response.cookies.set('authToken', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 0, // Expire immediately
      });

      return response;
    }

    // Find user
    const user = await User.findById(decoded.userId);
    if (!user) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'User not found',
      }, { status: 404 });
    }

    // Fetch profile data from appropriate collection
    let profileData;
    if (user.role === 'patient') {
      profileData = await Patient.findOne({ userId: user._id });
    } else if (user.role === 'doctor') {
      profileData = await Doctor.findOne({ userId: user._id });
    }

    // Return user data
    const userData = {
      id: user._id,
      email: user.email,
      firstName: profileData?.firstName || '',
      lastName: profileData?.lastName || '',
      role: user.role,
      phone: profileData?.phone || '',
      profileImage: profileData?.profileImage,
      isEmailVerified: user.isEmailVerified,
    };

    return NextResponse.json<ApiResponse>({
      success: true,
      data: {
        user: userData,
      },
    });

  } catch (error) {
    console.error('Get user error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Internal server error',
    }, { status: 500 });
  }
}
