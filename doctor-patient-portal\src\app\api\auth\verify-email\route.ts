import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import User from '@/models/User';
import { ApiResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const { token } = await request.json();

    if (!token) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Verification token is required',
      }, { status: 400 });
    }

    // Find user with this verification token
    const user = await User.findOne({
      emailVerificationToken: token,
      emailVerificationExpiry: { $gt: new Date() },
    });

    if (!user) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Invalid or expired verification token',
      }, { status: 400 });
    }

    // Verify the email
    user.isEmailVerified = true;
    user.emailVerificationToken = undefined;
    user.emailVerificationExpiry = undefined;
    await user.save();

    console.log(`✅ Email verified for user: ${user.email}`);

    return NextResponse.json<ApiResponse>({
      success: true,
      data: {
        message: 'Email verified successfully',
        user: user.toJSON(),
      },
    });

  } catch (error) {
    console.error('Email verification error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Internal server error',
    }, { status: 500 });
  }
}

// Resend verification email
export async function PUT(request: NextRequest) {
  try {
    await connectDB();

    const { email } = await request.json();

    if (!email) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Email is required',
      }, { status: 400 });
    }

    const user = await User.findOne({ email: email.toLowerCase() });

    if (!user) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'User not found',
      }, { status: 404 });
    }

    if (user.isEmailVerified) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Email is already verified',
      }, { status: 400 });
    }

    // Generate new verification token
    const verificationToken = user.generateEmailVerificationToken();
    await user.save();

    // Send verification email (you can import your email service here)
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/verify-email?token=${verificationToken}`;
    
    // TODO: Send email using your email service
    console.log(`📧 Verification email resent to ${user.email}`);
    console.log(`🔗 Verification URL: ${verificationUrl}`);

    return NextResponse.json<ApiResponse>({
      success: true,
      data: {
        message: 'Verification email sent successfully',
      },
    });

  } catch (error) {
    console.error('Resend verification error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Internal server error',
    }, { status: 500 });
  }
}
