{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { cn } from '@/lib/utils';\nimport { Button } from '@/components/ui/button';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport {\n  Home,\n  Calendar,\n  Users,\n  User,\n  Settings,\n  LogOut,\n  Menu,\n  X,\n  Stethoscope,\n  ClipboardList,\n  Search,\n  Bell,\n} from 'lucide-react';\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport { useUIStore } from '@/lib/store';\nimport { toast } from 'sonner';\n\ninterface SidebarProps {\n  className?: string;\n}\n\nexport default function Sidebar({ className }: SidebarProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const { user, logout } = useAuth();\n  const { sidebarOpen, toggleSidebar } = useUIStore();\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\n\n  const isDoctor = user?.role === 'doctor';\n\n  const doctorNavItems = [\n    {\n      title: 'Dashboard',\n      href: '/doctor/dashboard',\n      icon: Home,\n    },\n    {\n      title: 'Appointments',\n      href: '/doctor/appointments',\n      icon: Calendar,\n    },\n    {\n      title: 'Profile',\n      href: '/doctor/profile',\n      icon: User,\n    },\n    {\n      title: 'Settings',\n      href: '/doctor/settings',\n      icon: Settings,\n    },\n  ];\n\n  const patientNavItems = [\n    {\n      title: 'Dashboard',\n      href: '/patient/dashboard',\n      icon: Home,\n    },\n    {\n      title: 'Find Doctors',\n      href: '/doctors',\n      icon: Search,\n    },\n    {\n      title: 'My Appointments',\n      href: '/patient/appointments',\n      icon: Calendar,\n    },\n    {\n      title: 'Profile',\n      href: '/patient/profile',\n      icon: User,\n    },\n    {\n      title: 'Settings',\n      href: '/patient/settings',\n      icon: Settings,\n    },\n  ];\n\n  const navItems = isDoctor ? doctorNavItems : patientNavItems;\n\n  const handleLogout = async () => {\n    setIsLoggingOut(true);\n    try {\n      await logout();\n      toast.success('Logged out successfully');\n    } catch (error) {\n      toast.error('Failed to logout');\n    } finally {\n      setIsLoggingOut(false);\n    }\n  };\n\n  const getInitials = (firstName: string, lastName: string) => {\n    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();\n  };\n\n  const sidebarVariants = {\n    open: {\n      x: 0,\n      transition: {\n        type: 'spring',\n        stiffness: 300,\n        damping: 30,\n      },\n    },\n    closed: {\n      x: '-100%',\n      transition: {\n        type: 'spring',\n        stiffness: 300,\n        damping: 30,\n      },\n    },\n  };\n\n  return (\n    <>\n      {/* Mobile overlay */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n            onClick={toggleSidebar}\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Sidebar */}\n      <motion.aside\n        variants={sidebarVariants}\n        animate={sidebarOpen ? 'open' : 'closed'}\n        className={cn(\n          'fixed left-0 top-0 z-50 h-full w-64 bg-white border-r border-gray-200 shadow-lg lg:relative lg:translate-x-0',\n          className\n        )}\n      >\n        <div className=\"flex h-full flex-col\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"p-2 bg-blue-600 rounded-lg\">\n                <Stethoscope className=\"w-6 h-6 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-lg font-bold text-gray-900\">HealthCare</h1>\n                <p className=\"text-xs text-gray-500\">Portal</p>\n              </div>\n            </div>\n            \n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={toggleSidebar}\n              className=\"lg:hidden\"\n            >\n              <X className=\"w-5 h-5\" />\n            </Button>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 p-4 space-y-2\">\n            {navItems.map((item) => {\n              const isActive = pathname === item.href;\n              const Icon = item.icon;\n\n              return (\n                <motion.div\n                  key={item.href}\n                  whileHover={{ x: 4 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <Button\n                    variant={isActive ? 'default' : 'ghost'}\n                    className={cn(\n                      'w-full justify-start text-left',\n                      isActive\n                        ? 'bg-blue-600 text-white hover:bg-blue-700'\n                        : 'text-gray-700 hover:bg-gray-100'\n                    )}\n                    onClick={() => {\n                      router.push(item.href);\n                      if (typeof window !== 'undefined' && window.innerWidth < 1024) {\n                        toggleSidebar();\n                      }\n                    }}\n                  >\n                    <Icon className=\"w-5 h-5 mr-3\" />\n                    {item.title}\n                  </Button>\n                </motion.div>\n              );\n            })}\n          </nav>\n\n          {/* User Profile */}\n          <div className=\"p-4 border-t border-gray-200\">\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button\n                  variant=\"ghost\"\n                  className=\"w-full justify-start p-2 h-auto\"\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <Avatar className=\"w-10 h-10\">\n                      <AvatarImage\n                        src={user?.profileImage}\n                        alt={`${user?.firstName || 'User'} ${user?.lastName || ''}`}\n                      />\n                      <AvatarFallback className=\"bg-blue-100 text-blue-600 font-semibold\">\n                        {user && getInitials(user.firstName || 'U', user.lastName || 'U')}\n                      </AvatarFallback>\n                    </Avatar>\n\n                    <div className=\"flex-1 text-left\">\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {isDoctor ? 'Dr. ' : ''}{user?.firstName || user?.email?.split('@')[0] || 'User'} {user?.lastName || ''}\n                      </p>\n                      <p className=\"text-xs text-gray-500 capitalize\">\n                        {user?.role || 'User'}\n                      </p>\n                    </div>\n                  </div>\n                </Button>\n              </DropdownMenuTrigger>\n              \n              <DropdownMenuContent align=\"end\" className=\"w-56\">\n                <DropdownMenuItem\n                  onClick={() => router.push(isDoctor ? '/doctor/profile' : '/patient/profile')}\n                >\n                  <User className=\"mr-2 h-4 w-4\" />\n                  Profile\n                </DropdownMenuItem>\n                \n                <DropdownMenuItem\n                  onClick={() => router.push(isDoctor ? '/doctor/settings' : '/patient/settings')}\n                >\n                  <Settings className=\"mr-2 h-4 w-4\" />\n                  Settings\n                </DropdownMenuItem>\n                \n                <DropdownMenuSeparator />\n                \n                <DropdownMenuItem\n                  onClick={handleLogout}\n                  disabled={isLoggingOut}\n                  className=\"text-red-600 focus:text-red-600\"\n                >\n                  <LogOut className=\"mr-2 h-4 w-4\" />\n                  {isLoggingOut ? 'Logging out...' : 'Logout'}\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n        </div>\n      </motion.aside>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;AA/BA;;;;;;;;;;;;;AAqCe,SAAS,QAAQ,EAAE,SAAS,EAAgB;IACzD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;IAChD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,WAAW,MAAM,SAAS;IAEhC,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,MAAM;YACN,MAAM,mMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;QAChB;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;QAChB;KACD;IAED,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,MAAM;YACN,MAAM,mMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,sMAAA,CAAA,SAAM;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;QAChB;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;QAChB;KACD;IAED,MAAM,WAAW,WAAW,iBAAiB;IAE7C,MAAM,eAAe;QACnB,gBAAgB;QAChB,IAAI;YACF,MAAM;YACN,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc,CAAC,WAAmB;QACtC,OAAO,GAAG,UAAU,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,IAAI,CAAC,WAAW;IAClE;IAEA,MAAM,kBAAkB;QACtB,MAAM;YACJ,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;QACA,QAAQ;YACN,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,qBACE;;0BAEE,8OAAC,yLAAA,CAAA,kBAAe;0BACb,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;;;;;;0BAMf,8OAAC,0LAAA,CAAA,SAAM,CAAC,KAAK;gBACX,UAAU;gBACV,SAAS,cAAc,SAAS;gBAChC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gHACA;0BAGF,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAEzB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAkC;;;;;;8DAChD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAIzC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,MAAM,OAAO,KAAK,IAAI;gCAEtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,YAAY;wCAAE,GAAG;oCAAE;oCACnB,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,WAAW,YAAY;wCAChC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kCACA,WACI,6CACA;wCAEN,SAAS;4CACP,OAAO,IAAI,CAAC,KAAK,IAAI;4CACrB,IAAI,gBAAkB,eAAe,OAAO,UAAU,GAAG,MAAM;;4CAE/D;wCACF;;0DAEA,8OAAC;gDAAK,WAAU;;;;;;4CACf,KAAK,KAAK;;;;;;;mCApBR,KAAK,IAAI;;;;;4BAwBpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4IAAA,CAAA,eAAY;;kDACX,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;;0EAChB,8OAAC,kIAAA,CAAA,cAAW;gEACV,KAAK,MAAM;gEACX,KAAK,GAAG,MAAM,aAAa,OAAO,CAAC,EAAE,MAAM,YAAY,IAAI;;;;;;0EAE7D,8OAAC,kIAAA,CAAA,iBAAc;gEAAC,WAAU;0EACvB,QAAQ,YAAY,KAAK,SAAS,IAAI,KAAK,KAAK,QAAQ,IAAI;;;;;;;;;;;;kEAIjE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;;oEACV,WAAW,SAAS;oEAAI,MAAM,aAAa,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAAI;oEAAO;oEAAE,MAAM,YAAY;;;;;;;0EAEvG,8OAAC;gEAAE,WAAU;0EACV,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOzB,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DACzC,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,OAAO,IAAI,CAAC,WAAW,oBAAoB;;kEAE1D,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAInC,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,OAAO,IAAI,CAAC,WAAW,qBAAqB;;kEAE3D,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAIvC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0DAEtB,8OAAC,4IAAA,CAAA,mBAAgB;gDACf,SAAS;gDACT,UAAU;gDACV,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDACjB,eAAe,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD", "debugId": null}}, {"offset": {"line": 853, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 899, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Menu, Bell, Search } from 'lucide-react';\nimport { useUIStore, useNotificationStore } from '@/lib/store';\n\ninterface HeaderProps {\n  title: string;\n  subtitle?: string;\n}\n\nexport default function Header({ title, subtitle }: HeaderProps) {\n  const { toggleSidebar } = useUIStore();\n  const { notifications, unreadCount, markAsRead } = useNotificationStore();\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleNotificationClick = (index: number) => {\n    markAsRead(index);\n  };\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 px-4 py-3 lg:px-6\">\n      <div className=\"flex items-center justify-between\">\n        {/* Left side */}\n        <div className=\"flex items-center space-x-4\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={toggleSidebar}\n            className=\"lg:hidden\"\n          >\n            <Menu className=\"w-5 h-5\" />\n          </Button>\n\n          <div>\n            <h1 className=\"text-xl font-semibold text-gray-900\">{title}</h1>\n            {subtitle && (\n              <p className=\"text-sm text-gray-600\">{subtitle}</p>\n            )}\n          </div>\n        </div>\n\n        {/* Right side */}\n        <div className=\"flex items-center space-x-3\">\n          {/* Search - Hidden on mobile */}\n          <div className=\"hidden md:flex items-center space-x-2\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n\n          {/* Notifications */}\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n                <Bell className=\"w-5 h-5\" />\n                {unreadCount > 0 && (\n                  <motion.div\n                    initial={{ scale: 0 }}\n                    animate={{ scale: 1 }}\n                    className=\"absolute -top-1 -right-1\"\n                  >\n                    <Badge \n                      variant=\"destructive\" \n                      className=\"h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs\"\n                    >\n                      {unreadCount > 9 ? '9+' : unreadCount}\n                    </Badge>\n                  </motion.div>\n                )}\n              </Button>\n            </DropdownMenuTrigger>\n            \n            <DropdownMenuContent align=\"end\" className=\"w-80\">\n              <div className=\"p-3 border-b border-gray-200\">\n                <h3 className=\"font-semibold text-gray-900\">Notifications</h3>\n                {unreadCount > 0 && (\n                  <p className=\"text-sm text-gray-600\">\n                    You have {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}\n                  </p>\n                )}\n              </div>\n\n              <div className=\"max-h-96 overflow-y-auto\">\n                {notifications.length === 0 ? (\n                  <div className=\"p-4 text-center text-gray-500\">\n                    <Bell className=\"w-8 h-8 mx-auto mb-2 text-gray-300\" />\n                    <p className=\"text-sm\">No notifications yet</p>\n                  </div>\n                ) : (\n                  notifications.slice(0, 10).map((notification, index) => (\n                    <DropdownMenuItem\n                      key={index}\n                      className=\"p-3 cursor-pointer hover:bg-gray-50\"\n                      onClick={() => handleNotificationClick(index)}\n                    >\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-gray-900\">\n                          {notification.message}\n                        </p>\n                        <p className=\"text-xs text-gray-500 mt-1\">\n                          {new Date(notification.timestamp).toLocaleString()}\n                        </p>\n                      </div>\n                      {index < unreadCount && (\n                        <div className=\"w-2 h-2 bg-blue-600 rounded-full ml-2\" />\n                      )}\n                    </DropdownMenuItem>\n                  ))\n                )}\n              </div>\n\n              {notifications.length > 10 && (\n                <div className=\"p-3 border-t border-gray-200 text-center\">\n                  <Button variant=\"ghost\" size=\"sm\" className=\"text-blue-600\">\n                    View all notifications\n                  </Button>\n                </div>\n              )}\n            </DropdownMenuContent>\n          </DropdownMenu>\n\n          {/* Mobile search button */}\n          <Button variant=\"ghost\" size=\"sm\" className=\"md:hidden\">\n            <Search className=\"w-5 h-5\" />\n          </Button>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AACA;AAbA;;;;;;;;;AAoBe,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAe;IAC7D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,0BAA0B,CAAC;QAC/B,WAAW;IACb;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAGlB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;gCACpD,0BACC,8OAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;8BAM5C,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;;;;;;sCAMhB,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;;0DAC1C,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,cAAc,mBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,OAAO;gDAAE;gDACpB,SAAS;oDAAE,OAAO;gDAAE;gDACpB,WAAU;0DAEV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,WAAU;8DAET,cAAc,IAAI,OAAO;;;;;;;;;;;;;;;;;;;;;;8CAOpC,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8B;;;;;;gDAC3C,cAAc,mBACb,8OAAC;oDAAE,WAAU;;wDAAwB;wDACzB;wDAAY;wDAAqB,gBAAgB,IAAI,MAAM;;;;;;;;;;;;;sDAK3E,8OAAC;4CAAI,WAAU;sDACZ,cAAc,MAAM,KAAK,kBACxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAE,WAAU;kEAAU;;;;;;;;;;;uDAGzB,cAAc,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,cAAc,sBAC5C,8OAAC,4IAAA,CAAA,mBAAgB;oDAEf,WAAU;oDACV,SAAS,IAAM,wBAAwB;;sEAEvC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EACV,aAAa,OAAO;;;;;;8EAEvB,8OAAC;oEAAE,WAAU;8EACV,IAAI,KAAK,aAAa,SAAS,EAAE,cAAc;;;;;;;;;;;;wDAGnD,QAAQ,6BACP,8OAAC;4DAAI,WAAU;;;;;;;mDAbZ;;;;;;;;;;wCAoBZ,cAAc,MAAM,GAAG,oBACtB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;sCASpE,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,WAAU;sCAC1C,cAAA,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}, {"offset": {"line": 1244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport Sidebar from './Sidebar';\nimport Header from './Header';\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport { useUIStore } from '@/lib/store';\nimport { cn } from '@/lib/utils';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  title: string;\n  subtitle?: string;\n}\n\nexport default function DashboardLayout({\n  children,\n  title,\n  subtitle\n}: DashboardLayoutProps) {\n  const router = useRouter();\n  const { user, isLoading, isAuthenticated } = useAuth();\n  const { sidebarOpen } = useUIStore();\n\n  useEffect(() => {\n    if (isLoading) return;\n\n    if (!isAuthenticated || !user) {\n      router.push('/login');\n      return;\n    }\n  }, [user, isLoading, isAuthenticated, router]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated || !user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar />\n      \n      <div\n        className={cn(\n          'transition-all duration-300 ease-in-out',\n          sidebarOpen ? 'lg:ml-64' : 'lg:ml-0'\n        )}\n      >\n        <Header title={title} subtitle={subtitle} />\n        \n        <main className=\"p-4 lg:p-6\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            {children}\n          </motion.div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAiBe,SAAS,gBAAgB,EACtC,QAAQ,EACR,KAAK,EACL,QAAQ,EACa;IACrB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD;IACnD,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;IAEjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;QAEf,IAAI,CAAC,mBAAmB,CAAC,MAAM;YAC7B,OAAO,IAAI,CAAC;YACZ;QACF;IACF,GAAG;QAAC;QAAM;QAAW;QAAiB;KAAO;IAE7C,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,mBAAmB,CAAC,MAAM;QAC7B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,UAAO;;;;;0BAER,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2CACA,cAAc,aAAa;;kCAG7B,8OAAC,sIAAA,CAAA,UAAM;wBAAC,OAAO;wBAAO,UAAU;;;;;;kCAEhC,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;sCAE3B;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1474, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1500, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1528, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 1778, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/patient/profile/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport DashboardLayout from '@/components/layout/DashboardLayout';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { toast } from 'sonner';\nimport { Loader2, Save, User, Heart, Phone, Mail, Calendar } from 'lucide-react';\n\ninterface PatientProfile {\n  firstName: string;\n  lastName: string;\n  phone: string;\n  email: string;\n  dateOfBirth: string;\n  gender: string;\n  address: string;\n  city: string;\n  state: string;\n  zipCode: string;\n  emergencyContact: {\n    name: string;\n    relationship: string;\n    phone: string;\n  };\n  medicalHistory: {\n    allergies: string;\n    medications: string;\n    conditions: string;\n    surgeries: string;\n  };\n  insurance: {\n    provider: string;\n    policyNumber: string;\n    groupNumber: string;\n  };\n}\n\nexport default function PatientProfilePage() {\n  const [profile, setProfile] = useState<PatientProfile>({\n    firstName: '',\n    lastName: '',\n    phone: '',\n    email: '',\n    dateOfBirth: '',\n    gender: '',\n    address: '',\n    city: '',\n    state: '',\n    zipCode: '',\n    emergencyContact: {\n      name: '',\n      relationship: '',\n      phone: ''\n    },\n    medicalHistory: {\n      allergies: '',\n      medications: '',\n      conditions: '',\n      surgeries: ''\n    },\n    insurance: {\n      provider: '',\n      policyNumber: '',\n      groupNumber: ''\n    }\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const router = useRouter();\n  const { user, isLoading: authLoading, isAuthenticated } = useAuth();\n\n  useEffect(() => {\n    if (authLoading) return;\n\n    if (!isAuthenticated || !user) {\n      router.push('/login');\n      return;\n    }\n\n    if (user.role !== 'patient') {\n      router.push('/dashboard');\n      return;\n    }\n\n    fetchPatientProfile();\n  }, [user, isAuthenticated, authLoading, router]);\n\n  const fetchPatientProfile = async () => {\n    try {\n      const response = await fetch('/api/patients/me');\n      const result = await response.json();\n\n      if (result.success) {\n        // Merge user data with patient profile data\n        setProfile({\n          firstName: user?.firstName || '',\n          lastName: user?.lastName || '',\n          phone: user?.phone || '',\n          email: user?.email || '',\n          dateOfBirth: result.data?.dateOfBirth || '',\n          gender: result.data?.gender || '',\n          address: result.data?.address || '',\n          city: result.data?.city || '',\n          state: result.data?.state || '',\n          zipCode: result.data?.zipCode || '',\n          emergencyContact: result.data?.emergencyContact || {\n            name: '',\n            relationship: '',\n            phone: ''\n          },\n          medicalHistory: result.data?.medicalHistory || {\n            allergies: '',\n            medications: '',\n            conditions: '',\n            surgeries: ''\n          },\n          insurance: result.data?.insurance || {\n            provider: '',\n            policyNumber: '',\n            groupNumber: ''\n          }\n        });\n      } else {\n        // If no patient profile exists, just use user data\n        setProfile(prev => ({\n          ...prev,\n          firstName: user?.firstName || '',\n          lastName: user?.lastName || '',\n          phone: user?.phone || '',\n          email: user?.email || ''\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching patient profile:', error);\n      // Use user data as fallback\n      setProfile(prev => ({\n        ...prev,\n        firstName: user?.firstName || '',\n        lastName: user?.lastName || '',\n        phone: user?.phone || '',\n        email: user?.email || ''\n      }));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field: string, value: string) => {\n    if (field.includes('.')) {\n      const [parent, child] = field.split('.');\n      setProfile(prev => ({\n        ...prev,\n        [parent]: {\n          ...(prev as any)[parent],\n          [child]: value\n        }\n      }));\n    } else {\n      setProfile(prev => ({\n        ...prev,\n        [field]: value\n      }));\n    }\n  };\n\n  const handleSave = async () => {\n    setSaving(true);\n    try {\n      const response = await fetch('/api/patients/me', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,\n        },\n        body: JSON.stringify(profile),\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        toast.success('Profile updated successfully');\n      } else {\n        toast.error(result.error || 'Failed to update profile');\n      }\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      toast.error('Failed to update profile');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  if (authLoading || loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"Profile\" subtitle=\"Manage your personal and medical information\">\n      <div className=\"max-w-4xl mx-auto space-y-6\">\n        {/* Personal Information */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <User className=\"w-5 h-5 mr-2\" />\n              Personal Information\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"firstName\">First Name</Label>\n                <Input\n                  id=\"firstName\"\n                  value={profile.firstName}\n                  onChange={(e) => handleInputChange('firstName', e.target.value)}\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"lastName\">Last Name</Label>\n                <Input\n                  id=\"lastName\"\n                  value={profile.lastName}\n                  onChange={(e) => handleInputChange('lastName', e.target.value)}\n                />\n              </div>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"email\">Email</Label>\n                <Input\n                  id=\"email\"\n                  type=\"email\"\n                  value={profile.email}\n                  onChange={(e) => handleInputChange('email', e.target.value)}\n                  disabled\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"phone\">Phone Number</Label>\n                <Input\n                  id=\"phone\"\n                  value={profile.phone}\n                  onChange={(e) => handleInputChange('phone', e.target.value)}\n                  placeholder=\"+****************\"\n                />\n              </div>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"dateOfBirth\">Date of Birth</Label>\n                <Input\n                  id=\"dateOfBirth\"\n                  type=\"date\"\n                  value={profile.dateOfBirth}\n                  onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"gender\">Gender</Label>\n                <Select\n                  value={profile.gender}\n                  onValueChange={(value) => handleInputChange('gender', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select gender\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"male\">Male</SelectItem>\n                    <SelectItem value=\"female\">Female</SelectItem>\n                    <SelectItem value=\"other\">Other</SelectItem>\n                    <SelectItem value=\"prefer-not-to-say\">Prefer not to say</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Address Information */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Address Information</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div>\n              <Label htmlFor=\"address\">Street Address</Label>\n              <Input\n                id=\"address\"\n                value={profile.address}\n                onChange={(e) => handleInputChange('address', e.target.value)}\n                placeholder=\"123 Main Street\"\n              />\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <Label htmlFor=\"city\">City</Label>\n                <Input\n                  id=\"city\"\n                  value={profile.city}\n                  onChange={(e) => handleInputChange('city', e.target.value)}\n                  placeholder=\"New York\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"state\">State</Label>\n                <Input\n                  id=\"state\"\n                  value={profile.state}\n                  onChange={(e) => handleInputChange('state', e.target.value)}\n                  placeholder=\"NY\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"zipCode\">ZIP Code</Label>\n                <Input\n                  id=\"zipCode\"\n                  value={profile.zipCode}\n                  onChange={(e) => handleInputChange('zipCode', e.target.value)}\n                  placeholder=\"10001\"\n                />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Emergency Contact */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <Phone className=\"w-5 h-5 mr-2\" />\n              Emergency Contact\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"emergencyName\">Contact Name</Label>\n                <Input\n                  id=\"emergencyName\"\n                  value={profile.emergencyContact.name}\n                  onChange={(e) => handleInputChange('emergencyContact.name', e.target.value)}\n                  placeholder=\"John Doe\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"emergencyRelationship\">Relationship</Label>\n                <Input\n                  id=\"emergencyRelationship\"\n                  value={profile.emergencyContact.relationship}\n                  onChange={(e) => handleInputChange('emergencyContact.relationship', e.target.value)}\n                  placeholder=\"Spouse, Parent, Sibling, etc.\"\n                />\n              </div>\n            </div>\n            <div>\n              <Label htmlFor=\"emergencyPhone\">Emergency Phone</Label>\n              <Input\n                id=\"emergencyPhone\"\n                value={profile.emergencyContact.phone}\n                onChange={(e) => handleInputChange('emergencyContact.phone', e.target.value)}\n                placeholder=\"+****************\"\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Medical History */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <Heart className=\"w-5 h-5 mr-2\" />\n              Medical History\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div>\n              <Label htmlFor=\"allergies\">Allergies</Label>\n              <Textarea\n                id=\"allergies\"\n                rows={2}\n                value={profile.medicalHistory.allergies}\n                onChange={(e) => handleInputChange('medicalHistory.allergies', e.target.value)}\n                placeholder=\"List any known allergies to medications, foods, or other substances...\"\n              />\n            </div>\n            <div>\n              <Label htmlFor=\"medications\">Current Medications</Label>\n              <Textarea\n                id=\"medications\"\n                rows={2}\n                value={profile.medicalHistory.medications}\n                onChange={(e) => handleInputChange('medicalHistory.medications', e.target.value)}\n                placeholder=\"List current medications and dosages...\"\n              />\n            </div>\n            <div>\n              <Label htmlFor=\"conditions\">Medical Conditions</Label>\n              <Textarea\n                id=\"conditions\"\n                rows={2}\n                value={profile.medicalHistory.conditions}\n                onChange={(e) => handleInputChange('medicalHistory.conditions', e.target.value)}\n                placeholder=\"List any chronic conditions or ongoing health issues...\"\n              />\n            </div>\n            <div>\n              <Label htmlFor=\"surgeries\">Previous Surgeries</Label>\n              <Textarea\n                id=\"surgeries\"\n                rows={2}\n                value={profile.medicalHistory.surgeries}\n                onChange={(e) => handleInputChange('medicalHistory.surgeries', e.target.value)}\n                placeholder=\"List any previous surgeries or major medical procedures...\"\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Insurance Information */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Insurance Information</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div>\n              <Label htmlFor=\"insuranceProvider\">Insurance Provider</Label>\n              <Input\n                id=\"insuranceProvider\"\n                value={profile.insurance.provider}\n                onChange={(e) => handleInputChange('insurance.provider', e.target.value)}\n                placeholder=\"Blue Cross Blue Shield, Aetna, etc.\"\n              />\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"policyNumber\">Policy Number</Label>\n                <Input\n                  id=\"policyNumber\"\n                  value={profile.insurance.policyNumber}\n                  onChange={(e) => handleInputChange('insurance.policyNumber', e.target.value)}\n                  placeholder=\"Policy number\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"groupNumber\">Group Number</Label>\n                <Input\n                  id=\"groupNumber\"\n                  value={profile.insurance.groupNumber}\n                  onChange={(e) => handleInputChange('insurance.groupNumber', e.target.value)}\n                  placeholder=\"Group number\"\n                />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Save Button */}\n        <div className=\"flex justify-end\">\n          <Button onClick={handleSave} disabled={saving} size=\"lg\">\n            {saving ? (\n              <>\n                <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                Saving...\n              </>\n            ) : (\n              <>\n                <Save className=\"w-4 h-4 mr-2\" />\n                Save Changes\n              </>\n            )}\n          </Button>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAbA;;;;;;;;;;;;;;AA4Ce,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACrD,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,aAAa;QACb,QAAQ;QACR,SAAS;QACT,MAAM;QACN,OAAO;QACP,SAAS;QACT,kBAAkB;YAChB,MAAM;YACN,cAAc;YACd,OAAO;QACT;QACA,gBAAgB;YACd,WAAW;YACX,aAAa;YACb,YAAY;YACZ,WAAW;QACb;QACA,WAAW;YACT,UAAU;YACV,cAAc;YACd,aAAa;QACf;IACF;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,WAAW,WAAW,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;QAEjB,IAAI,CAAC,mBAAmB,CAAC,MAAM;YAC7B,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,KAAK,IAAI,KAAK,WAAW;YAC3B,OAAO,IAAI,CAAC;YACZ;QACF;QAEA;IACF,GAAG;QAAC;QAAM;QAAiB;QAAa;KAAO;IAE/C,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,4CAA4C;gBAC5C,WAAW;oBACT,WAAW,MAAM,aAAa;oBAC9B,UAAU,MAAM,YAAY;oBAC5B,OAAO,MAAM,SAAS;oBACtB,OAAO,MAAM,SAAS;oBACtB,aAAa,OAAO,IAAI,EAAE,eAAe;oBACzC,QAAQ,OAAO,IAAI,EAAE,UAAU;oBAC/B,SAAS,OAAO,IAAI,EAAE,WAAW;oBACjC,MAAM,OAAO,IAAI,EAAE,QAAQ;oBAC3B,OAAO,OAAO,IAAI,EAAE,SAAS;oBAC7B,SAAS,OAAO,IAAI,EAAE,WAAW;oBACjC,kBAAkB,OAAO,IAAI,EAAE,oBAAoB;wBACjD,MAAM;wBACN,cAAc;wBACd,OAAO;oBACT;oBACA,gBAAgB,OAAO,IAAI,EAAE,kBAAkB;wBAC7C,WAAW;wBACX,aAAa;wBACb,YAAY;wBACZ,WAAW;oBACb;oBACA,WAAW,OAAO,IAAI,EAAE,aAAa;wBACnC,UAAU;wBACV,cAAc;wBACd,aAAa;oBACf;gBACF;YACF,OAAO;gBACL,mDAAmD;gBACnD,WAAW,CAAA,OAAQ,CAAC;wBAClB,GAAG,IAAI;wBACP,WAAW,MAAM,aAAa;wBAC9B,UAAU,MAAM,YAAY;wBAC5B,OAAO,MAAM,SAAS;wBACtB,OAAO,MAAM,SAAS;oBACxB,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,4BAA4B;YAC5B,WAAW,CAAA,OAAQ,CAAC;oBAClB,GAAG,IAAI;oBACP,WAAW,MAAM,aAAa;oBAC9B,UAAU,MAAM,YAAY;oBAC5B,OAAO,MAAM,SAAS;oBACtB,OAAO,MAAM,SAAS;gBACxB,CAAC;QACH,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,IAAI,MAAM,QAAQ,CAAC,MAAM;YACvB,MAAM,CAAC,QAAQ,MAAM,GAAG,MAAM,KAAK,CAAC;YACpC,WAAW,CAAA,OAAQ,CAAC;oBAClB,GAAG,IAAI;oBACP,CAAC,OAAO,EAAE;wBACR,GAAG,AAAC,IAAY,CAAC,OAAO;wBACxB,CAAC,MAAM,EAAE;oBACX;gBACF,CAAC;QACH,OAAO;YACL,WAAW,CAAA,OAAQ,CAAC;oBAClB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE;gBACX,CAAC;QACH;IACF;IAEA,MAAM,aAAa;QACjB,UAAU;QACV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,cAAc;gBAChE;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,UAAU;QACZ;IACF;IAEA,IAAI,eAAe,SAAS;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC,+IAAA,CAAA,UAAe;QAAC,OAAM;QAAU,UAAS;kBACxC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAIrC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,QAAQ,SAAS;oDACxB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sDAGlE,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,QAAQ,QAAQ;oDACvB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;8CAInE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAQ;;;;;;8DACvB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,QAAQ,KAAK;oDACpB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1D,QAAQ;;;;;;;;;;;;sDAGZ,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAQ;;;;;;8DACvB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,QAAQ,KAAK;oDACpB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1D,aAAY;;;;;;;;;;;;;;;;;;8CAIlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,QAAQ,WAAW;oDAC1B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sDAGpE,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAS;;;;;;8DACxB,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO,QAAQ,MAAM;oDACrB,eAAe,CAAC,QAAU,kBAAkB,UAAU;;sEAEtD,8OAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8EACZ,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAO;;;;;;8EACzB,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;8EAC3B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAQ;;;;;;8EAC1B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASlD,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,QAAQ,OAAO;4CACtB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC5D,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAO;;;;;;8DACtB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,QAAQ,IAAI;oDACnB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACzD,aAAY;;;;;;;;;;;;sDAGhB,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAQ;;;;;;8DACvB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,QAAQ,KAAK;oDACpB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1D,aAAY;;;;;;;;;;;;sDAGhB,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,QAAQ,OAAO;oDACtB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQtB,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAItC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgB;;;;;;8DAC/B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,QAAQ,gBAAgB,CAAC,IAAI;oDACpC,UAAU,CAAC,IAAM,kBAAkB,yBAAyB,EAAE,MAAM,CAAC,KAAK;oDAC1E,aAAY;;;;;;;;;;;;sDAGhB,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAwB;;;;;;8DACvC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,QAAQ,gBAAgB,CAAC,YAAY;oDAC5C,UAAU,CAAC,IAAM,kBAAkB,iCAAiC,EAAE,MAAM,CAAC,KAAK;oDAClF,aAAY;;;;;;;;;;;;;;;;;;8CAIlB,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAiB;;;;;;sDAChC,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,QAAQ,gBAAgB,CAAC,KAAK;4CACrC,UAAU,CAAC,IAAM,kBAAkB,0BAA0B,EAAE,MAAM,CAAC,KAAK;4CAC3E,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8BAOpB,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAItC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;sDAC3B,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,MAAM;4CACN,OAAO,QAAQ,cAAc,CAAC,SAAS;4CACvC,UAAU,CAAC,IAAM,kBAAkB,4BAA4B,EAAE,MAAM,CAAC,KAAK;4CAC7E,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAc;;;;;;sDAC7B,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,MAAM;4CACN,OAAO,QAAQ,cAAc,CAAC,WAAW;4CACzC,UAAU,CAAC,IAAM,kBAAkB,8BAA8B,EAAE,MAAM,CAAC,KAAK;4CAC/E,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAa;;;;;;sDAC5B,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,MAAM;4CACN,OAAO,QAAQ,cAAc,CAAC,UAAU;4CACxC,UAAU,CAAC,IAAM,kBAAkB,6BAA6B,EAAE,MAAM,CAAC,KAAK;4CAC9E,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;sDAC3B,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,MAAM;4CACN,OAAO,QAAQ,cAAc,CAAC,SAAS;4CACvC,UAAU,CAAC,IAAM,kBAAkB,4BAA4B,EAAE,MAAM,CAAC,KAAK;4CAC7E,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8BAOpB,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAoB;;;;;;sDACnC,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,QAAQ,SAAS,CAAC,QAAQ;4CACjC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;4CACvE,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,QAAQ,SAAS,CAAC,YAAY;oDACrC,UAAU,CAAC,IAAM,kBAAkB,0BAA0B,EAAE,MAAM,CAAC,KAAK;oDAC3E,aAAY;;;;;;;;;;;;sDAGhB,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,QAAQ,SAAS,CAAC,WAAW;oDACpC,UAAU,CAAC,IAAM,kBAAkB,yBAAyB,EAAE,MAAM,CAAC,KAAK;oDAC1E,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQtB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAY,UAAU;wBAAQ,MAAK;kCACjD,uBACC;;8CACE,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA8B;;yDAInD;;8CACE,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}]}