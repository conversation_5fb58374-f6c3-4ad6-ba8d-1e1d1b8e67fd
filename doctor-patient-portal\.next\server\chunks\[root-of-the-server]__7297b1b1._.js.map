{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/db.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/doctor-patient-portal';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\n// Global is used here to maintain a cached connection across hot reloads in development\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached!.conn) {\n    return cached!.conn;\n  }\n\n  if (!cached!.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached!.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Connected to MongoDB');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached!.conn = await cached!.promise;\n  } catch (e) {\n    cached!.promise = null;\n    throw e;\n  }\n\n  return cached!.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAYA,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAQ,IAAI,EAAE;QAChB,OAAO,OAAQ,IAAI;IACrB;IAEA,IAAI,CAAC,OAAQ,OAAO,EAAE;QACpB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAQ,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YAC1D,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAQ,IAAI,GAAG,MAAM,OAAQ,OAAO;IACtC,EAAE,OAAO,GAAG;QACV,OAAQ,OAAO,GAAG;QAClB,MAAM;IACR;IAEA,OAAO,OAAQ,IAAI;AACrB;uCAEe", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\nexport interface UserDocument extends Document {\n  email: string;\n  password: string;\n  role: 'doctor' | 'patient';\n  isEmailVerified: boolean;\n  resetPasswordToken?: string;\n  resetPasswordExpiry?: Date;\n  emailVerificationToken?: string;\n  emailVerificationExpiry?: Date;\n  comparePassword(candidatePassword: string): Promise<boolean>;\n}\n\n\n\nconst UserSchema = new Schema<UserDocument>({\n  email: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true,\n  },\n  password: {\n    type: String,\n    required: true,\n    minlength: 6,\n  },\n  role: {\n    type: String,\n    enum: ['doctor', 'patient'],\n    required: true,\n  },\n  isEmailVerified: {\n    type: Boolean,\n    default: false,\n  },\n  emailVerificationToken: {\n    type: String,\n  },\n  emailVerificationExpiry: {\n    type: Date,\n  },\n  resetPasswordToken: {\n    type: String,\n  },\n  resetPasswordExpiry: {\n    type: Date,\n  },\n}, {\n  timestamps: true,\n});\n\n// Hash password before saving\nUserSchema.pre('save', async function (next) {\n  if (!this.isModified('password')) return next();\n\n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error as Error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Generate email verification token\nUserSchema.methods.generateEmailVerificationToken = function (): string {\n  const crypto = require('crypto');\n  const token = crypto.randomBytes(32).toString('hex');\n\n  this.emailVerificationToken = token;\n  this.emailVerificationExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours\n\n  return token;\n};\n\n// Generate password reset token\nUserSchema.methods.generatePasswordResetToken = function (): string {\n  const crypto = require('crypto');\n  const token = crypto.randomBytes(32).toString('hex');\n\n  this.resetPasswordToken = token;\n  this.resetPasswordExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour\n\n  return token;\n};\n\n// Verify email verification token\nUserSchema.methods.verifyEmailToken = function (token: string): boolean {\n  return this.emailVerificationToken === token &&\n         this.emailVerificationExpiry &&\n         this.emailVerificationExpiry > new Date();\n};\n\n// Verify password reset token\nUserSchema.methods.verifyPasswordResetToken = function (token: string): boolean {\n  return this.resetPasswordToken === token &&\n         this.resetPasswordExpiry &&\n         this.resetPasswordExpiry > new Date();\n};\n\n// Clean JSON output (remove sensitive fields)\nUserSchema.methods.toJSON = function () {\n  const userObject = this.toObject();\n  delete userObject.password;\n  delete userObject.resetPasswordToken;\n  delete userObject.resetPasswordExpiry;\n  delete userObject.emailVerificationToken;\n  delete userObject.emailVerificationExpiry;\n  delete userObject.__v;\n  return userObject;\n};\n\n// Indexes for better query performance\n// Note: email index is already created by unique: true\nUserSchema.index({ role: 1 });\n\nexport default mongoose.models.User || mongoose.model<UserDocument>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAgBA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAe;IAC1C,OAAO;QACL,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAU;SAAU;QAC3B,UAAU;IACZ;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,wBAAwB;QACtB,MAAM;IACR;IACA,yBAAyB;QACvB,MAAM;IACR;IACA,oBAAoB;QAClB,MAAM;IACR;IACA,qBAAqB;QACnB,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAgB,IAAI;IACzC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAgB,iBAAyB;IAC5E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,oCAAoC;AACpC,WAAW,OAAO,CAAC,8BAA8B,GAAG;IAClD,MAAM;IACN,MAAM,QAAQ,OAAO,WAAW,CAAC,IAAI,QAAQ,CAAC;IAE9C,IAAI,CAAC,sBAAsB,GAAG;IAC9B,IAAI,CAAC,uBAAuB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,OAAO,WAAW;IAEtF,OAAO;AACT;AAEA,gCAAgC;AAChC,WAAW,OAAO,CAAC,0BAA0B,GAAG;IAC9C,MAAM;IACN,MAAM,QAAQ,OAAO,WAAW,CAAC,IAAI,QAAQ,CAAC;IAE9C,IAAI,CAAC,kBAAkB,GAAG;IAC1B,IAAI,CAAC,mBAAmB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,OAAO,SAAS;IAE3E,OAAO;AACT;AAEA,kCAAkC;AAClC,WAAW,OAAO,CAAC,gBAAgB,GAAG,SAAU,KAAa;IAC3D,OAAO,IAAI,CAAC,sBAAsB,KAAK,SAChC,IAAI,CAAC,uBAAuB,IAC5B,IAAI,CAAC,uBAAuB,GAAG,IAAI;AAC5C;AAEA,8BAA8B;AAC9B,WAAW,OAAO,CAAC,wBAAwB,GAAG,SAAU,KAAa;IACnE,OAAO,IAAI,CAAC,kBAAkB,KAAK,SAC5B,IAAI,CAAC,mBAAmB,IACxB,IAAI,CAAC,mBAAmB,GAAG,IAAI;AACxC;AAEA,8CAA8C;AAC9C,WAAW,OAAO,CAAC,MAAM,GAAG;IAC1B,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO,WAAW,kBAAkB;IACpC,OAAO,WAAW,mBAAmB;IACrC,OAAO,WAAW,sBAAsB;IACxC,OAAO,WAAW,uBAAuB;IACzC,OAAO,WAAW,GAAG;IACrB,OAAO;AACT;AAEA,uCAAuC;AACvC,uDAAuD;AACvD,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;uCAEZ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAe,QAAQ", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/Patient.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { Patient as IPatient } from '@/types';\n\nexport interface PatientDocument extends Document, Omit<IPatient, '_id'> {}\n\nconst EmergencyContactSchema = new Schema({\n  name: {\n    type: String,\n    trim: true,\n  },\n  relationship: {\n    type: String,\n    trim: true,\n  },\n  phone: {\n    type: String,\n    trim: true,\n  },\n});\n\nconst MedicalHistorySchema = new Schema({\n  allergies: {\n    type: String,\n    trim: true,\n  },\n  medications: {\n    type: String,\n    trim: true,\n  },\n  conditions: {\n    type: String,\n    trim: true,\n  },\n  surgeries: {\n    type: String,\n    trim: true,\n  },\n});\n\nconst InsuranceSchema = new Schema({\n  provider: {\n    type: String,\n    trim: true,\n  },\n  policyNumber: {\n    type: String,\n    trim: true,\n  },\n  groupNumber: {\n    type: String,\n    trim: true,\n  },\n});\n\nconst PatientSchema = new Schema<PatientDocument>({\n  userId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: true,\n    unique: true,\n  },\n  firstName: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  lastName: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  phone: {\n    type: String,\n    trim: true,\n  },\n  profileImage: {\n    type: String,\n  },\n  dateOfBirth: {\n    type: Date,\n  },\n  gender: {\n    type: String,\n    enum: ['male', 'female', 'other', 'prefer-not-to-say'],\n  },\n  address: {\n    type: String,\n    trim: true,\n  },\n  city: {\n    type: String,\n    trim: true,\n  },\n  state: {\n    type: String,\n    trim: true,\n  },\n  zipCode: {\n    type: String,\n    trim: true,\n  },\n  emergencyContact: {\n    type: EmergencyContactSchema,\n  },\n  medicalHistory: {\n    type: MedicalHistorySchema,\n  },\n  insurance: {\n    type: InsuranceSchema,\n  },\n}, {\n  timestamps: true,\n});\n\n// Indexes for better query performance\n// Note: userId index is already created by unique: true\nPatientSchema.index({ firstName: 1, lastName: 1 });\nPatientSchema.index({ city: 1 });\nPatientSchema.index({ state: 1 });\n\nexport default mongoose.models.Patient || mongoose.model<PatientDocument>('Patient', PatientSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAKA,MAAM,yBAAyB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACxC,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;AACF;AAEA,MAAM,uBAAuB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACtC,WAAW;QACT,MAAM;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,MAAM;IACR;IACA,YAAY;QACV,MAAM;QACN,MAAM;IACR;IACA,WAAW;QACT,MAAM;QACN,MAAM;IACR;AACF;AAEA,MAAM,kBAAkB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACjC,UAAU;QACR,MAAM;QACN,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,MAAM;IACR;AACF;AAEA,MAAM,gBAAgB,IAAI,yGAAA,CAAA,SAAM,CAAkB;IAChD,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;QACV,QAAQ;IACV;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,cAAc;QACZ,MAAM;IACR;IACA,aAAa;QACX,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAQ;YAAU;YAAS;SAAoB;IACxD;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,kBAAkB;QAChB,MAAM;IACR;IACA,gBAAgB;QACd,MAAM;IACR;IACA,WAAW;QACT,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,uCAAuC;AACvC,wDAAwD;AACxD,cAAc,KAAK,CAAC;IAAE,WAAW;IAAG,UAAU;AAAE;AAChD,cAAc,KAAK,CAAC;IAAE,MAAM;AAAE;AAC9B,cAAc,KAAK,CAAC;IAAE,OAAO;AAAE;uCAEhB,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,OAAO,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAkB,WAAW", "debugId": null}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/Doctor.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { Doctor as IDoctor, AvailabilitySlot } from '@/types';\n\nexport interface DoctorDocument extends Document, Omit<IDoctor, '_id'> {}\n\nconst AvailabilitySlotSchema = new Schema<AvailabilitySlot>({\n  dayOfWeek: {\n    type: Number,\n    required: true,\n    min: 0,\n    max: 6,\n  },\n  startTime: {\n    type: String,\n    required: true,\n    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,\n  },\n  endTime: {\n    type: String,\n    required: true,\n    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,\n  },\n  isAvailable: {\n    type: Boolean,\n    default: true,\n  },\n});\n\nconst LocationSchema = new Schema({\n  address: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  city: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  state: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  zipCode: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n});\n\nconst DoctorSchema = new Schema<DoctorDocument>({\n  userId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: true,\n    unique: true,\n  },\n  specialty: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  bio: {\n    type: String,\n    required: true,\n    maxlength: 1000,\n  },\n  experience: {\n    type: Number,\n    required: true,\n    min: 0,\n  },\n  rating: {\n    type: Number,\n    default: 0,\n    min: 0,\n    max: 5,\n  },\n  totalRatings: {\n    type: Number,\n    default: 0,\n    min: 0,\n  },\n  profileImage: {\n    type: String,\n  },\n  availability: [AvailabilitySlotSchema],\n  consultationFee: {\n    type: Number,\n    required: true,\n    min: 0,\n  },\n  location: {\n    type: LocationSchema,\n    required: true,\n  },\n}, {\n  timestamps: true,\n});\n\n// Indexes for better query performance\n// Note: userId index is already created by unique: true\nDoctorSchema.index({ specialty: 1 });\nDoctorSchema.index({ rating: -1 });\nDoctorSchema.index({ 'location.city': 1 });\nDoctorSchema.index({ 'location.state': 1 });\n\n// Compound indexes for common queries\nDoctorSchema.index({ specialty: 1, rating: -1 });\nDoctorSchema.index({ 'location.city': 1, specialty: 1 });\n\nexport default mongoose.models.Doctor || mongoose.model<DoctorDocument>('Doctor', DoctorSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAKA,MAAM,yBAAyB,IAAI,yGAAA,CAAA,SAAM,CAAmB;IAC1D,WAAW;QACT,MAAM;QACN,UAAU;QACV,KAAK;QACL,KAAK;IACP;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;AACF;AAEA,MAAM,iBAAiB,IAAI,yGAAA,CAAA,SAAM,CAAC;IAChC,SAAS;QACP,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,MAAM;IACR;AACF;AAEA,MAAM,eAAe,IAAI,yGAAA,CAAA,SAAM,CAAiB;IAC9C,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;QACV,QAAQ;IACV;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,KAAK;QACH,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,YAAY;QACV,MAAM;QACN,UAAU;QACV,KAAK;IACP;IACA,QAAQ;QACN,MAAM;QACN,SAAS;QACT,KAAK;QACL,KAAK;IACP;IACA,cAAc;QACZ,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,cAAc;QACZ,MAAM;IACR;IACA,cAAc;QAAC;KAAuB;IACtC,iBAAiB;QACf,MAAM;QACN,UAAU;QACV,KAAK;IACP;IACA,UAAU;QACR,MAAM;QACN,UAAU;IACZ;AACF,GAAG;IACD,YAAY;AACd;AAEA,uCAAuC;AACvC,wDAAwD;AACxD,aAAa,KAAK,CAAC;IAAE,WAAW;AAAE;AAClC,aAAa,KAAK,CAAC;IAAE,QAAQ,CAAC;AAAE;AAChC,aAAa,KAAK,CAAC;IAAE,iBAAiB;AAAE;AACxC,aAAa,KAAK,CAAC;IAAE,kBAAkB;AAAE;AAEzC,sCAAsC;AACtC,aAAa,KAAK,CAAC;IAAE,WAAW;IAAG,QAAQ,CAAC;AAAE;AAC9C,aAAa,KAAK,CAAC;IAAE,iBAAiB;IAAG,WAAW;AAAE;uCAEvC,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAiB,UAAU", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/api/fix-database/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/db';\nimport User from '@/models/User';\nimport Patient from '@/models/Patient';\nimport Doctor from '@/models/Doctor';\nimport { ApiResponse } from '@/types';\n\nexport async function POST(request: NextRequest) {\n  try {\n    await connectDB();\n\n    const { action } = await request.json();\n\n    if (action === 'clear-doctors') {\n      // Clear all doctor records and let them be recreated\n      await Doctor.deleteMany({});\n      \n      // Drop the doctors collection to remove all indexes\n      try {\n        await Doctor.collection.drop();\n      } catch (error) {\n        console.log('Collection might not exist, continuing...');\n      }\n\n      return NextResponse.json<ApiResponse>({\n        success: true,\n        message: 'Doctor collection cleared and indexes reset',\n      });\n    }\n\n    if (action === 'fix-indexes') {\n      // Drop problematic indexes\n      try {\n        await Doctor.collection.dropIndex('user_1');\n      } catch (error) {\n        console.log('Index user_1 might not exist:', error);\n      }\n\n      // Recreate proper indexes\n      await Doctor.collection.createIndex({ userId: 1 }, { unique: true });\n      await Doctor.collection.createIndex({ specialty: 1 });\n      await Doctor.collection.createIndex({ rating: -1 });\n\n      return NextResponse.json<ApiResponse>({\n        success: true,\n        message: 'Indexes fixed successfully',\n      });\n    }\n\n    if (action === 'create-test-doctor') {\n      // Create a test doctor profile\n      const testUser = await User.findOne({ email: '<EMAIL>' });\n      \n      if (!testUser) {\n        return NextResponse.json<ApiResponse>({\n          success: false,\n          error: 'Test user not found',\n        }, { status: 404 });\n      }\n\n      // Check if doctor profile already exists\n      const existingDoctor = await Doctor.findOne({ userId: testUser._id });\n      if (existingDoctor) {\n        return NextResponse.json<ApiResponse>({\n          success: true,\n          data: existingDoctor,\n          message: 'Doctor profile already exists',\n        });\n      }\n\n      const doctor = new Doctor({\n        userId: testUser._id,\n        firstName: 'Dr. Test',\n        lastName: 'Doctor',\n        specialty: 'General Practice',\n        bio: 'Experienced general practitioner dedicated to providing quality healthcare.',\n        experience: 5,\n        consultationFee: 100,\n        location: {\n          address: '123 Medical Center Dr',\n          city: 'Healthcare City',\n          state: 'Medical State',\n          zipCode: '12345'\n        },\n        availability: [],\n      });\n\n      await doctor.save();\n\n      return NextResponse.json<ApiResponse>({\n        success: true,\n        data: doctor,\n        message: 'Test doctor profile created successfully',\n      });\n    }\n\n    if (action === 'check-status') {\n      const userCount = await User.countDocuments();\n      const patientCount = await Patient.countDocuments();\n      const doctorCount = await Doctor.countDocuments();\n\n      // Check for test user\n      const testUser = await User.findOne({ email: '<EMAIL>' });\n      const testDoctor = testUser ? await Doctor.findOne({ userId: testUser._id }) : null;\n\n      return NextResponse.json<ApiResponse>({\n        success: true,\n        data: {\n          users: userCount,\n          patients: patientCount,\n          doctors: doctorCount,\n          testUser: !!testUser,\n          testDoctor: !!testDoctor,\n          testUserRole: testUser?.role,\n        },\n        message: 'Database status retrieved',\n      });\n    }\n\n    return NextResponse.json<ApiResponse>({\n      success: false,\n      error: 'Invalid action',\n    }, { status: 400 });\n\n  } catch (error) {\n    console.error('Database fix error:', error);\n    return NextResponse.json<ApiResponse>({\n      success: false,\n      error: 'Internal server error',\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ,IAAI;QAErC,IAAI,WAAW,iBAAiB;YAC9B,qDAAqD;YACrD,MAAM,yHAAA,CAAA,UAAM,CAAC,UAAU,CAAC,CAAC;YAEzB,oDAAoD;YACpD,IAAI;gBACF,MAAM,yHAAA,CAAA,UAAM,CAAC,UAAU,CAAC,IAAI;YAC9B,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC;YACd;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,SAAS;YACX;QACF;QAEA,IAAI,WAAW,eAAe;YAC5B,2BAA2B;YAC3B,IAAI;gBACF,MAAM,yHAAA,CAAA,UAAM,CAAC,UAAU,CAAC,SAAS,CAAC;YACpC,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,iCAAiC;YAC/C;YAEA,0BAA0B;YAC1B,MAAM,yHAAA,CAAA,UAAM,CAAC,UAAU,CAAC,WAAW,CAAC;gBAAE,QAAQ;YAAE,GAAG;gBAAE,QAAQ;YAAK;YAClE,MAAM,yHAAA,CAAA,UAAM,CAAC,UAAU,CAAC,WAAW,CAAC;gBAAE,WAAW;YAAE;YACnD,MAAM,yHAAA,CAAA,UAAM,CAAC,UAAU,CAAC,WAAW,CAAC;gBAAE,QAAQ,CAAC;YAAE;YAEjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,SAAS;YACX;QACF;QAEA,IAAI,WAAW,sBAAsB;YACnC,+BAA+B;YAC/B,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;gBAAE,OAAO;YAAsB;YAEnE,IAAI,CAAC,UAAU;gBACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;oBACpC,SAAS;oBACT,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB;YAEA,yCAAyC;YACzC,MAAM,iBAAiB,MAAM,yHAAA,CAAA,UAAM,CAAC,OAAO,CAAC;gBAAE,QAAQ,SAAS,GAAG;YAAC;YACnE,IAAI,gBAAgB;gBAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;oBACpC,SAAS;oBACT,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,MAAM,SAAS,IAAI,yHAAA,CAAA,UAAM,CAAC;gBACxB,QAAQ,SAAS,GAAG;gBACpB,WAAW;gBACX,UAAU;gBACV,WAAW;gBACX,KAAK;gBACL,YAAY;gBACZ,iBAAiB;gBACjB,UAAU;oBACR,SAAS;oBACT,MAAM;oBACN,OAAO;oBACP,SAAS;gBACX;gBACA,cAAc,EAAE;YAClB;YAEA,MAAM,OAAO,IAAI;YAEjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,MAAM;gBACN,SAAS;YACX;QACF;QAEA,IAAI,WAAW,gBAAgB;YAC7B,MAAM,YAAY,MAAM,uHAAA,CAAA,UAAI,CAAC,cAAc;YAC3C,MAAM,eAAe,MAAM,0HAAA,CAAA,UAAO,CAAC,cAAc;YACjD,MAAM,cAAc,MAAM,yHAAA,CAAA,UAAM,CAAC,cAAc;YAE/C,sBAAsB;YACtB,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;gBAAE,OAAO;YAAsB;YACnE,MAAM,aAAa,WAAW,MAAM,yHAAA,CAAA,UAAM,CAAC,OAAO,CAAC;gBAAE,QAAQ,SAAS,GAAG;YAAC,KAAK;YAE/E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;gBACpC,SAAS;gBACT,MAAM;oBACJ,OAAO;oBACP,UAAU;oBACV,SAAS;oBACT,UAAU,CAAC,CAAC;oBACZ,YAAY,CAAC,CAAC;oBACd,cAAc,UAAU;gBAC1B;gBACA,SAAS;YACX;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAc;YACpC,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}