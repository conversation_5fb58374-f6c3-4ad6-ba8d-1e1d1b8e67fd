{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/logo.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { Stethoscope } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg';\n  showText?: boolean;\n  className?: string;\n  clickable?: boolean;\n  variant?: 'default' | 'compact';\n}\n\nexport default function Logo({ \n  size = 'md', \n  showText = true, \n  className,\n  clickable = true,\n  variant = 'default'\n}: LogoProps) {\n  const router = useRouter();\n\n  const sizeClasses = {\n    sm: {\n      icon: 'w-4 h-4',\n      container: 'p-1.5',\n      text: 'text-sm',\n      subtext: 'text-xs'\n    },\n    md: {\n      icon: 'w-6 h-6',\n      container: 'p-2',\n      text: 'text-lg',\n      subtext: 'text-xs'\n    },\n    lg: {\n      icon: 'w-8 h-8',\n      container: 'p-3',\n      text: 'text-xl',\n      subtext: 'text-sm'\n    }\n  };\n\n  const handleClick = () => {\n    if (clickable) {\n      router.push('/');\n    }\n  };\n\n  const logoContent = (\n    <div className={cn(\n      'flex items-center space-x-2',\n      clickable && 'cursor-pointer group',\n      className\n    )}>\n      <motion.div \n        className={cn(\n          'bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg shadow-sm',\n          sizeClasses[size].container,\n          clickable && 'group-hover:shadow-md transition-shadow duration-200'\n        )}\n        whileHover={clickable ? { scale: 1.05 } : {}}\n        whileTap={clickable ? { scale: 0.95 } : {}}\n      >\n        <Stethoscope className={cn('text-white', sizeClasses[size].icon)} />\n      </motion.div>\n      \n      {showText && (\n        <div className={cn(\n          'flex flex-col',\n          variant === 'compact' && 'hidden sm:flex'\n        )}>\n          <h1 className={cn(\n            'font-bold text-gray-900 leading-tight',\n            sizeClasses[size].text,\n            clickable && 'group-hover:text-blue-600 transition-colors duration-200'\n          )}>\n            HealthCare\n          </h1>\n          {variant === 'default' && (\n            <p className={cn(\n              'text-gray-500 leading-tight',\n              sizeClasses[size].subtext\n            )}>\n              Portal\n            </p>\n          )}\n        </div>\n      )}\n    </div>\n  );\n\n  if (clickable) {\n    return (\n      <motion.div\n        onClick={handleClick}\n        whileHover={{ scale: 1.02 }}\n        whileTap={{ scale: 0.98 }}\n        className=\"inline-block\"\n      >\n        {logoContent}\n      </motion.div>\n    );\n  }\n\n  return logoContent;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAee,SAAS,KAAK,EAC3B,OAAO,IAAI,EACX,WAAW,IAAI,EACf,SAAS,EACT,YAAY,IAAI,EAChB,UAAU,SAAS,EACT;;IACV,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,cAAc;QAClB,IAAI;YACF,MAAM;YACN,WAAW;YACX,MAAM;YACN,SAAS;QACX;QACA,IAAI;YACF,MAAM;YACN,WAAW;YACX,MAAM;YACN,SAAS;QACX;QACA,IAAI;YACF,MAAM;YACN,WAAW;YACX,MAAM;YACN,SAAS;QACX;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,WAAW;YACb,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,4BACJ,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,+BACA,aAAa,wBACb;;0BAEA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA,WAAW,CAAC,KAAK,CAAC,SAAS,EAC3B,aAAa;gBAEf,YAAY,YAAY;oBAAE,OAAO;gBAAK,IAAI,CAAC;gBAC3C,UAAU,YAAY;oBAAE,OAAO;gBAAK,IAAI,CAAC;0BAEzC,cAAA,6LAAC,mNAAA,CAAA,cAAW;oBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc,WAAW,CAAC,KAAK,CAAC,IAAI;;;;;;;;;;;YAGhE,0BACC,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,iBACA,YAAY,aAAa;;kCAEzB,6LAAC;wBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,yCACA,WAAW,CAAC,KAAK,CAAC,IAAI,EACtB,aAAa;kCACZ;;;;;;oBAGF,YAAY,2BACX,6LAAC;wBAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,+BACA,WAAW,CAAC,KAAK,CAAC,OAAO;kCACxB;;;;;;;;;;;;;;;;;;IASb,IAAI,WAAW;QACb,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;YACT,YAAY;gBAAE,OAAO;YAAK;YAC1B,UAAU;gBAAE,OAAO;YAAK;YACxB,WAAU;sBAET;;;;;;IAGP;IAEA,OAAO;AACT;GA7FwB;;QAOP,qIAAA,CAAA,YAAS;;;KAPF", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Calendar, Users, Shield, Clock, Star, Stethoscope } from 'lucide-react';\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport Logo from '@/components/ui/logo';\n\nexport default function Home() {\n  const router = useRouter();\n  const { isAuthenticated, user, isLoading } = useAuth();\n\n  // Debug authentication state\n  console.log('Home page - Auth state:', { isAuthenticated, user: user?.email, isLoading });\n\n  const handleGetStarted = () => {\n    if (isAuthenticated && user) {\n      const dashboardPath = user.role === 'doctor' ? '/doctor/dashboard' : '/patient/dashboard';\n      router.push(dashboardPath);\n    } else {\n      router.push('/register');\n    }\n  };\n\n  const handleSignIn = () => {\n    console.log('Sign In clicked - Auth state:', { isAuthenticated, user: user?.email });\n    console.log('LocalStorage authToken:', localStorage.getItem('authToken'));\n    router.push('/login');\n  };\n\n  const clearAuthState = async () => {\n    try {\n      // Call logout API to clear server-side cookie\n      await fetch('/api/auth/logout', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,\n        },\n      });\n    } catch (error) {\n      console.error('Error calling logout API:', error);\n    }\n\n    // Clear client-side storage\n    localStorage.removeItem('authToken');\n\n    // Reload to reset all state\n    window.location.reload();\n  };\n\n  const features = [\n    {\n      icon: Calendar,\n      title: 'Easy Appointment Booking',\n      description: 'Book appointments with your preferred doctors in just a few clicks.',\n    },\n    {\n      icon: Users,\n      title: 'Qualified Doctors',\n      description: 'Connect with experienced and certified healthcare professionals.',\n    },\n    {\n      icon: Shield,\n      title: 'Secure & Private',\n      description: 'Your health information is protected with enterprise-grade security.',\n    },\n    {\n      icon: Clock,\n      title: 'Real-time Updates',\n      description: 'Get instant notifications about your appointment status and updates.',\n    },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Navigation */}\n      <nav className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <Logo size=\"lg\" variant=\"compact\" />\n\n            <div className=\"flex items-center space-x-2 sm:space-x-4\">\n              <Button\n                variant=\"ghost\"\n                onClick={() => router.push('/doctors')}\n                className=\"hidden sm:inline-flex hover:bg-blue-50 hover:text-blue-600 transition-colors\"\n              >\n                Find Doctors\n              </Button>\n\n              {isAuthenticated ? (\n                <Button\n                  onClick={handleGetStarted}\n                  className=\"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-sm\"\n                >\n                  <span className=\"hidden sm:inline\">Go to Dashboard</span>\n                  <span className=\"sm:hidden\">Dashboard</span>\n                </Button>\n              ) : (\n                <>\n                  <Button\n                    variant=\"ghost\"\n                    onClick={handleSignIn}\n                    className=\"hover:bg-blue-50 hover:text-blue-600 transition-colors\"\n                  >\n                    <span className=\"hidden sm:inline\">Sign In</span>\n                    <span className=\"sm:hidden\">Login</span>\n                  </Button>\n                  <Button\n                    onClick={() => router.push('/register')}\n                    className=\"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-sm\"\n                  >\n                    <span className=\"hidden sm:inline\">Sign Up</span>\n                    <span className=\"sm:hidden\">Join</span>\n                  </Button>\n                </>\n              )}\n\n              {/* Debug button - remove in production */}\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={clearAuthState}\n                className=\"ml-2 text-xs\"\n              >\n                Clear Auth\n              </Button>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n        <div className=\"text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n          >\n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n              Your Health,{' '}\n              <span className=\"text-blue-600\">Our Priority</span>\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n              Connect with qualified healthcare professionals, book appointments seamlessly,\n              and manage your health journey with our comprehensive doctor-patient portal.\n            </p>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button\n                size=\"lg\"\n                onClick={handleGetStarted}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg\"\n              >\n                Get Started\n              </Button>\n              <Button\n                size=\"lg\"\n                variant=\"outline\"\n                onClick={() => router.push('/doctors')}\n                className=\"px-8 py-3 text-lg\"\n              >\n                Find Doctors\n              </Button>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n      {/* Features Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Why Choose Our Platform?\n          </h2>\n          <p className=\"text-gray-600 max-w-2xl mx-auto\">\n            We provide a comprehensive healthcare platform that connects patients\n            with doctors, making healthcare accessible and convenient.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {features.map((feature, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n            >\n              <Card className=\"h-full text-center hover:shadow-lg transition-shadow\">\n                <CardHeader>\n                  <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                    <feature.icon className=\"w-6 h-6 text-blue-600\" />\n                  </div>\n                  <CardTitle className=\"text-lg\">{feature.title}</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <CardDescription className=\"text-gray-600\">\n                    {feature.description}\n                  </CardDescription>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n\n      {/* CTA Section */}\n      <div className=\"bg-blue-600 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold mb-4\">\n              Ready to Get Started?\n            </h2>\n            <p className=\"text-blue-100 mb-8 max-w-2xl mx-auto\">\n              Join thousands of patients and doctors who trust our platform\n              for their healthcare needs.\n            </p>\n            <Button\n              size=\"lg\"\n              variant=\"secondary\"\n              onClick={handleGetStarted}\n              className=\"bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 text-lg\"\n            >\n              Join Now\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center space-x-2 mb-4\">\n              <div className=\"p-2 bg-blue-600 rounded-lg\">\n                <Stethoscope className=\"w-6 h-6 text-white\" />\n              </div>\n              <h3 className=\"text-xl font-bold\">HealthCare Portal</h3>\n            </div>\n            <p className=\"text-gray-400\">\n              Connecting patients with healthcare professionals for better health outcomes.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;IAEnD,6BAA6B;IAC7B,QAAQ,GAAG,CAAC,2BAA2B;QAAE;QAAiB,MAAM,MAAM;QAAO;IAAU;IAEvF,MAAM,mBAAmB;QACvB,IAAI,mBAAmB,MAAM;YAC3B,MAAM,gBAAgB,KAAK,IAAI,KAAK,WAAW,sBAAsB;YACrE,OAAO,IAAI,CAAC;QACd,OAAO;YACL,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ,GAAG,CAAC,iCAAiC;YAAE;YAAiB,MAAM,MAAM;QAAM;QAClF,QAAQ,GAAG,CAAC,2BAA2B,aAAa,OAAO,CAAC;QAC5D,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,8CAA8C;YAC9C,MAAM,MAAM,oBAAoB;gBAC9B,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,cAAc;gBAChE;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;QAEA,4BAA4B;QAC5B,aAAa,UAAU,CAAC;QAExB,4BAA4B;QAC5B,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,MAAM,WAAW;QACf;YACE,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,UAAI;gCAAC,MAAK;gCAAK,SAAQ;;;;;;0CAExB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;oCAIA,gCACC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAmB;;;;;;0DACnC,6LAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;6DAG9B;;0DACE,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS;gDACT,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAmB;;;;;;kEACnC,6LAAC;wDAAK,WAAU;kEAAY;;;;;;;;;;;;0DAE9B,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAmB;;;;;;kEACnC,6LAAC;wDAAK,WAAU;kEAAY;;;;;;;;;;;;;;kDAMlC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,6LAAC;gCAAG,WAAU;;oCAAoD;oCACnD;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAK5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQT,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAAkC;;;;;;;;;;;;kCAMjD,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;0CAEhD,cAAA,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAE1B,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAW,QAAQ,KAAK;;;;;;;;;;;;sDAE/C,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,QAAQ,WAAW;;;;;;;;;;;;;;;;;+BAdrB;;;;;;;;;;;;;;;;0BAwBb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAGxC,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,6LAAC;wCAAG,WAAU;kDAAoB;;;;;;;;;;;;0CAEpC,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;GAhPwB;;QACP,qIAAA,CAAA,YAAS;QACqB,kJAAA,CAAA,UAAO;;;KAF9B", "debugId": null}}]}