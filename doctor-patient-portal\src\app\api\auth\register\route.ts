import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import connectDB from '@/lib/db';
import User from '@/models/User';
import Patient from '@/models/Patient';
import Doctor from '@/models/Doctor';
import { sendEmail } from '@/lib/email';
import { ApiResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const { email, password, firstName, lastName, role, phone } = await request.json();

    // Validation
    if (!email || !password || !firstName || !lastName || !role) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Email, password, first name, last name, and role are required',
      }, { status: 400 });
    }

    if (password.length < 6) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Password must be at least 6 characters long',
      }, { status: 400 });
    }

    if (!['doctor', 'patient'].includes(role)) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Role must be either doctor or patient',
      }, { status: 400 });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'User with this email already exists',
      }, { status: 400 });
    }

    // Create new user (basic auth info only)
    const user = new User({
      email: email.toLowerCase(),
      password,
      role,
      isEmailVerified: false, // In production, you'd send a verification email
    });

    // Generate email verification token
    const verificationToken = user.generateEmailVerificationToken();
    await user.save();

    // Create role-specific profile
    let profileData;
    if (role === 'patient') {
      const patient = new Patient({
        userId: user._id,
        firstName,
        lastName,
        phone: phone || '',
      });
      await patient.save();
      profileData = patient;
    } else if (role === 'doctor') {
      const doctor = new Doctor({
        userId: user._id,
        specialty: 'General Practice', // Default specialty
        bio: 'Welcome to my practice. I am dedicated to providing quality healthcare.',
        experience: 0,
        consultationFee: 50,
        location: {
          address: 'To be updated',
          city: 'To be updated',
          state: 'To be updated',
          zipCode: '00000'
        },
        availability: [],
      });
      await doctor.save();
      profileData = doctor;
    }

    // Send email verification
    try {
      const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/verify-email?token=${verificationToken}`;

      await sendEmail({
        to: user.email,
        subject: 'Verify Your Email - Doctor Patient Portal',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2563eb;">Welcome to Doctor Patient Portal!</h2>
            <p>Hi ${firstName},</p>
            <p>Thank you for registering with our healthcare platform. Please verify your email address to complete your registration.</p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${verificationUrl}"
                 style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Verify Email Address
              </a>
            </div>
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #6b7280;">${verificationUrl}</p>
            <p>This link will expire in 24 hours.</p>
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; font-size: 14px;">
              If you didn't create an account, please ignore this email.
            </p>
          </div>
        `,
      });

      console.log(`✅ Email verification sent to ${user.email}`);
    } catch (emailError) {
      console.error('❌ Failed to send verification email:', emailError);
      // Continue with registration even if email fails
    }

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: user._id,
        email: user.email,
        role: user.role
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '7d' }
    );

    // Create response with user data
    const userData = {
      id: user._id,
      email: user.email,
      firstName: profileData?.firstName || firstName,
      lastName: profileData?.lastName || lastName,
      role: user.role,
      phone: profileData?.phone || phone,
      profileImage: profileData?.profileImage,
      isEmailVerified: user.isEmailVerified,
    };

    const response = NextResponse.json<ApiResponse>({
      success: true,
      data: {
        user: userData,
        token,
      },
    });

    // Set HTTP-only cookie for additional security
    response.cookies.set('authToken', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60, // 7 days
    });

    return response;

  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Internal server error',
    }, { status: 500 });
  }
}
