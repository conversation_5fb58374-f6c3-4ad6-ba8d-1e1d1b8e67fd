export interface User {
  _id: string;
  clerkId?: string;
  email: string;
  role: 'doctor' | 'patient';
  profile: UserProfile;
  createdAt: Date;
  updatedAt: Date;
}

export interface EmergencyContact {
  name?: string;
  relationship?: string;
  phone?: string;
}

export interface MedicalHistory {
  allergies?: string;
  medications?: string;
  conditions?: string;
  surgeries?: string;
}

export interface Insurance {
  provider?: string;
  policyNumber?: string;
  groupNumber?: string;
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  phone?: string;
  profileImage?: string;
  dateOfBirth?: Date;
  gender?: 'male' | 'female' | 'other' | 'prefer-not-to-say';
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  emergencyContact?: EmergencyContact;
  medicalHistory?: MedicalHistory;
  insurance?: Insurance;
}

export interface Doctor {
  _id: string;
  userId: string;
  specialty: string;
  bio: string;
  experience: number;
  rating: number;
  totalRatings: number;
  profileImage?: string;
  availability: AvailabilitySlot[];
  consultationFee: number;
  location: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface AvailabilitySlot {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
  isAvailable: boolean;
}

export interface Appointment {
  _id: string;
  patientId: string;
  doctorId: string;
  dateTime: Date;
  status: AppointmentStatus;
  notes?: string;
  prescription?: string;
  symptoms?: string;
  diagnosis?: string;
  createdAt: Date;
  updatedAt: Date;
}

export type AppointmentStatus = 
  | 'pending' 
  | 'approved' 
  | 'rejected' 
  | 'completed' 
  | 'cancelled';

export interface AppointmentWithDetails extends Appointment {
  patient: User;
  doctor: Doctor & { user: User };
}

export interface DoctorWithUser extends Doctor {
  user: User;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  role: 'doctor' | 'patient';
  profile: {
    firstName: string;
    lastName: string;
    phone?: string;
  };
  doctorInfo?: {
    specialty: string;
    bio: string;
    experience: number;
    consultationFee: number;
    location: {
      address: string;
      city: string;
      state: string;
      zipCode: string;
    };
  };
}

export interface SearchFilters {
  specialty?: string;
  location?: string;
  rating?: number;
  availability?: boolean;
  sortBy?: 'rating' | 'experience' | 'fee' | 'name';
  sortOrder?: 'asc' | 'desc';
}

export interface BookingData {
  doctorId: string;
  dateTime: Date;
  symptoms?: string;
  notes?: string;
}

export interface NotificationData {
  type: 'appointment_approved' | 'appointment_rejected' | 'appointment_cancelled' | 'new_appointment';
  appointmentId: string;
  message: string;
  timestamp: Date;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}
