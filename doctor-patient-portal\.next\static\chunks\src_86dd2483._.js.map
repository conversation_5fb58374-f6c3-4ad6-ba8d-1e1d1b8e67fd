{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,6LAAC,2IAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf;GAjBM;;QACyB,mJAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/store.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { User, <PERSON><PERSON><PERSON>User, Appointment, NotificationData } from '@/types';\n\ninterface AuthState {\n  user: User | DoctorWithUser | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  login: (user: User | DoctorWithUser) => void;\n  logout: () => void;\n  updateUser: (user: Partial<User | DoctorWithUser>) => void;\n  setLoading: (loading: boolean) => void;\n}\n\ninterface AppointmentState {\n  appointments: Appointment[];\n  isLoading: boolean;\n  setAppointments: (appointments: Appointment[]) => void;\n  addAppointment: (appointment: Appointment) => void;\n  updateAppointment: (id: string, updates: Partial<Appointment>) => void;\n  removeAppointment: (id: string) => void;\n  setLoading: (loading: boolean) => void;\n}\n\ninterface NotificationState {\n  notifications: NotificationData[];\n  unreadCount: number;\n  addNotification: (notification: NotificationData) => void;\n  markAsRead: (index: number) => void;\n  clearNotifications: () => void;\n}\n\ninterface UIState {\n  sidebarOpen: boolean;\n  theme: 'light' | 'dark';\n  toggleSidebar: () => void;\n  setSidebarOpen: (open: boolean) => void;\n  setTheme: (theme: 'light' | 'dark') => void;\n}\n\n// Auth Store\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      login: (user) => set({ user, isAuthenticated: true }),\n      logout: () => set({ user: null, isAuthenticated: false }),\n      updateUser: (updates) => {\n        const currentUser = get().user;\n        if (currentUser) {\n          set({ user: { ...currentUser, ...updates } });\n        }\n      },\n      setLoading: (loading) => set({ isLoading: loading }),\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({ \n        user: state.user, \n        isAuthenticated: state.isAuthenticated \n      }),\n    }\n  )\n);\n\n// Appointment Store\nexport const useAppointmentStore = create<AppointmentState>((set, get) => ({\n  appointments: [],\n  isLoading: false,\n  setAppointments: (appointments) => set({ appointments }),\n  addAppointment: (appointment) => {\n    const appointments = get().appointments;\n    set({ appointments: [appointment, ...appointments] });\n  },\n  updateAppointment: (id, updates) => {\n    const appointments = get().appointments;\n    const updatedAppointments = appointments.map(apt => \n      apt._id === id ? { ...apt, ...updates } : apt\n    );\n    set({ appointments: updatedAppointments });\n  },\n  removeAppointment: (id) => {\n    const appointments = get().appointments;\n    const filteredAppointments = appointments.filter(apt => apt._id !== id);\n    set({ appointments: filteredAppointments });\n  },\n  setLoading: (loading) => set({ isLoading: loading }),\n}));\n\n// Notification Store\nexport const useNotificationStore = create<NotificationState>((set, get) => ({\n  notifications: [],\n  unreadCount: 0,\n  addNotification: (notification) => {\n    const notifications = get().notifications;\n    set({ \n      notifications: [notification, ...notifications],\n      unreadCount: get().unreadCount + 1\n    });\n  },\n  markAsRead: (index) => {\n    const notifications = get().notifications;\n    notifications[index] = { ...notifications[index] };\n    set({ \n      notifications: [...notifications],\n      unreadCount: Math.max(0, get().unreadCount - 1)\n    });\n  },\n  clearNotifications: () => set({ notifications: [], unreadCount: 0 }),\n}));\n\n// UI Store\nexport const useUIStore = create<UIState>()(\n  persist(\n    (set) => ({\n      sidebarOpen: true,\n      theme: 'light',\n      toggleSidebar: () => set((state) => ({ sidebarOpen: !state.sidebarOpen })),\n      setSidebarOpen: (open) => set({ sidebarOpen: open }),\n      setTheme: (theme) => set({ theme }),\n    }),\n    {\n      name: 'ui-storage',\n    }\n  )\n);\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAwCO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,OAAO,CAAC,OAAS,IAAI;gBAAE;gBAAM,iBAAiB;YAAK;QACnD,QAAQ,IAAM,IAAI;gBAAE,MAAM;gBAAM,iBAAiB;YAAM;QACvD,YAAY,CAAC;YACX,MAAM,cAAc,MAAM,IAAI;YAC9B,IAAI,aAAa;gBACf,IAAI;oBAAE,MAAM;wBAAE,GAAG,WAAW;wBAAE,GAAG,OAAO;oBAAC;gBAAE;YAC7C;QACF;QACA,YAAY,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;IACpD,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AAKG,MAAM,sBAAsB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAoB,CAAC,KAAK,MAAQ,CAAC;QACzE,cAAc,EAAE;QAChB,WAAW;QACX,iBAAiB,CAAC,eAAiB,IAAI;gBAAE;YAAa;QACtD,gBAAgB,CAAC;YACf,MAAM,eAAe,MAAM,YAAY;YACvC,IAAI;gBAAE,cAAc;oBAAC;uBAAgB;iBAAa;YAAC;QACrD;QACA,mBAAmB,CAAC,IAAI;YACtB,MAAM,eAAe,MAAM,YAAY;YACvC,MAAM,sBAAsB,aAAa,GAAG,CAAC,CAAA,MAC3C,IAAI,GAAG,KAAK,KAAK;oBAAE,GAAG,GAAG;oBAAE,GAAG,OAAO;gBAAC,IAAI;YAE5C,IAAI;gBAAE,cAAc;YAAoB;QAC1C;QACA,mBAAmB,CAAC;YAClB,MAAM,eAAe,MAAM,YAAY;YACvC,MAAM,uBAAuB,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK;YACpE,IAAI;gBAAE,cAAc;YAAqB;QAC3C;QACA,YAAY,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;IACpD,CAAC;AAGM,MAAM,uBAAuB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAqB,CAAC,KAAK,MAAQ,CAAC;QAC3E,eAAe,EAAE;QACjB,aAAa;QACb,iBAAiB,CAAC;YAChB,MAAM,gBAAgB,MAAM,aAAa;YACzC,IAAI;gBACF,eAAe;oBAAC;uBAAiB;iBAAc;gBAC/C,aAAa,MAAM,WAAW,GAAG;YACnC;QACF;QACA,YAAY,CAAC;YACX,MAAM,gBAAgB,MAAM,aAAa;YACzC,aAAa,CAAC,MAAM,GAAG;gBAAE,GAAG,aAAa,CAAC,MAAM;YAAC;YACjD,IAAI;gBACF,eAAe;uBAAI;iBAAc;gBACjC,aAAa,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG;YAC/C;QACF;QACA,oBAAoB,IAAM,IAAI;gBAAE,eAAe,EAAE;gBAAE,aAAa;YAAE;IACpE,CAAC;AAGM,MAAM,aAAa,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC7B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,aAAa;QACb,OAAO;QACP,eAAe,IAAM,IAAI,CAAC,QAAU,CAAC;oBAAE,aAAa,CAAC,MAAM,WAAW;gBAAC,CAAC;QACxE,gBAAgB,CAAC,OAAS,IAAI;gBAAE,aAAa;YAAK;QAClD,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;IACnC,CAAC,GACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/hooks/useSocket.ts"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport { io, Socket } from 'socket.io-client';\nimport { useAuthStore, useNotificationStore } from '@/lib/store';\nimport { NotificationPayload } from '@/lib/socket';\nimport { toast } from 'sonner';\n\ninterface UseSocketReturn {\n  socket: Socket | null;\n  isConnected: boolean;\n  joinAppointment: (appointmentId: string) => void;\n  leaveAppointment: (appointmentId: string) => void;\n  emitAppointmentUpdate: (data: {\n    appointmentId: string;\n    status: string;\n    recipientId: string;\n    message: string;\n  }) => void;\n  emitNewAppointment: (data: {\n    appointmentId: string;\n    doctorId: string;\n    patientId: string;\n    message: string;\n  }) => void;\n}\n\nexport const useSocket = (): UseSocketReturn => {\n  const [socket, setSocket] = useState<Socket | null>(null);\n  const [isConnected, setIsConnected] = useState(false);\n  const { user, isAuthenticated } = useAuthStore();\n  const { addNotification } = useNotificationStore();\n  const socketRef = useRef<Socket | null>(null);\n\n  useEffect(() => {\n    if (!isAuthenticated || !user) {\n      return;\n    }\n\n    // Get token from cookies or localStorage\n    const getToken = () => {\n      // Check if we're on the client side\n      if (typeof window === 'undefined') {\n        return '';\n      }\n      // In a real app, you might get this from a secure cookie\n      // For now, we'll assume it's available in the auth store or make an API call\n      return localStorage.getItem('token') || '';\n    };\n\n    const token = getToken();\n    if (!token) {\n      console.warn('No token available for Socket.IO connection');\n      return;\n    }\n\n    // Initialize socket connection\n    const socketInstance = io(process.env.NODE_ENV === 'production' \n      ? process.env.NEXT_PUBLIC_SOCKET_URL || '' \n      : 'http://localhost:3000', {\n      path: '/api/socket',\n      auth: {\n        token,\n      },\n      transports: ['websocket', 'polling'],\n    });\n\n    socketRef.current = socketInstance;\n    setSocket(socketInstance);\n\n    // Connection event handlers\n    socketInstance.on('connect', () => {\n      console.log('Connected to Socket.IO server');\n      setIsConnected(true);\n    });\n\n    socketInstance.on('disconnect', (reason) => {\n      console.log('Disconnected from Socket.IO server:', reason);\n      setIsConnected(false);\n    });\n\n    socketInstance.on('connect_error', (error) => {\n      console.error('Socket.IO connection error:', error);\n      setIsConnected(false);\n    });\n\n    // Welcome message\n    socketInstance.on('connected', (data) => {\n      console.log('Socket.IO welcome message:', data);\n      toast.success('Connected to real-time updates');\n    });\n\n    // Notification handler\n    socketInstance.on('notification', (notification: NotificationPayload) => {\n      console.log('Received notification:', notification);\n      \n      // Add to notification store\n      addNotification(notification);\n      \n      // Show toast notification\n      toast.info(notification.message, {\n        duration: 5000,\n        action: {\n          label: 'View',\n          onClick: () => {\n            // Navigate to appointment or relevant page\n            if (typeof window !== 'undefined') {\n              window.location.href = `/appointments/${notification.appointmentId}`;\n            }\n          },\n        },\n      });\n    });\n\n    // Appointment update handler\n    socketInstance.on('appointment_updated', (data) => {\n      console.log('Appointment updated:', data);\n      \n      // You can emit a custom event or update local state here\n      if (typeof window !== 'undefined') {\n        window.dispatchEvent(new CustomEvent('appointmentUpdated', {\n          detail: data,\n        }));\n      }\n    });\n\n    // Typing indicators (for future chat feature)\n    socketInstance.on('user_typing', (data) => {\n      console.log('User typing:', data);\n    });\n\n    socketInstance.on('user_stopped_typing', (data) => {\n      console.log('User stopped typing:', data);\n    });\n\n    // Cleanup on unmount\n    return () => {\n      if (socketRef.current) {\n        socketRef.current.disconnect();\n        socketRef.current = null;\n      }\n      setSocket(null);\n      setIsConnected(false);\n    };\n  }, [isAuthenticated, user, addNotification]);\n\n  // Helper functions\n  const joinAppointment = (appointmentId: string) => {\n    if (socket) {\n      socket.emit('join_appointment', appointmentId);\n    }\n  };\n\n  const leaveAppointment = (appointmentId: string) => {\n    if (socket) {\n      socket.emit('leave_appointment', appointmentId);\n    }\n  };\n\n  const emitAppointmentUpdate = (data: {\n    appointmentId: string;\n    status: string;\n    recipientId: string;\n    message: string;\n  }) => {\n    if (socket) {\n      socket.emit('appointment_status_update', data);\n    }\n  };\n\n  const emitNewAppointment = (data: {\n    appointmentId: string;\n    doctorId: string;\n    patientId: string;\n    message: string;\n  }) => {\n    if (socket) {\n      socket.emit('new_appointment', data);\n    }\n  };\n\n  return {\n    socket,\n    isConnected,\n    joinAppointment,\n    leaveAppointment,\n    emitAppointmentUpdate,\n    emitNewAppointment,\n  };\n};\n\n// Custom hook for appointment-specific socket events\nexport const useAppointmentSocket = (appointmentId: string) => {\n  const { socket, joinAppointment, leaveAppointment } = useSocket();\n  const [appointmentUpdates, setAppointmentUpdates] = useState<any[]>([]);\n\n  useEffect(() => {\n    if (socket && appointmentId) {\n      joinAppointment(appointmentId);\n\n      const handleAppointmentUpdate = (data: any) => {\n        if (data.appointmentId === appointmentId) {\n          setAppointmentUpdates(prev => [...prev, data]);\n        }\n      };\n\n      socket.on('appointment_updated', handleAppointmentUpdate);\n\n      return () => {\n        leaveAppointment(appointmentId);\n        socket.off('appointment_updated', handleAppointmentUpdate);\n      };\n    }\n  }, [socket, appointmentId, joinAppointment, leaveAppointment]);\n\n  return {\n    appointmentUpdates,\n  };\n};\n"], "names": [], "mappings": ";;;;AAyD8B;AAvD9B;AACA;AAAA;AACA;AAEA;;AANA;;;;;AA2BO,MAAM,YAAY;;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;IAC7C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;IAC/C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IAExC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B;YACF;YAEA,yCAAyC;YACzC,MAAM;gDAAW;oBACf,oCAAoC;oBACpC,uCAAmC;;oBAEnC;oBACA,yDAAyD;oBACzD,6EAA6E;oBAC7E,OAAO,aAAa,OAAO,CAAC,YAAY;gBAC1C;;YAEA,MAAM,QAAQ;YACd,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb;YACF;YAEA,+BAA+B;YAC/B,MAAM,iBAAiB,CAAA,GAAA,kLAAA,CAAA,KAAE,AAAD,EAAE,6EAEtB,yBAAyB;gBAC3B,MAAM;gBACN,MAAM;oBACJ;gBACF;gBACA,YAAY;oBAAC;oBAAa;iBAAU;YACtC;YAEA,UAAU,OAAO,GAAG;YACpB,UAAU;YAEV,4BAA4B;YAC5B,eAAe,EAAE,CAAC;uCAAW;oBAC3B,QAAQ,GAAG,CAAC;oBACZ,eAAe;gBACjB;;YAEA,eAAe,EAAE,CAAC;uCAAc,CAAC;oBAC/B,QAAQ,GAAG,CAAC,uCAAuC;oBACnD,eAAe;gBACjB;;YAEA,eAAe,EAAE,CAAC;uCAAiB,CAAC;oBAClC,QAAQ,KAAK,CAAC,+BAA+B;oBAC7C,eAAe;gBACjB;;YAEA,kBAAkB;YAClB,eAAe,EAAE,CAAC;uCAAa,CAAC;oBAC9B,QAAQ,GAAG,CAAC,8BAA8B;oBAC1C,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;;YAEA,uBAAuB;YACvB,eAAe,EAAE,CAAC;uCAAgB,CAAC;oBACjC,QAAQ,GAAG,CAAC,0BAA0B;oBAEtC,4BAA4B;oBAC5B,gBAAgB;oBAEhB,0BAA0B;oBAC1B,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,aAAa,OAAO,EAAE;wBAC/B,UAAU;wBACV,QAAQ;4BACN,OAAO;4BACP,OAAO;uDAAE;oCACP,2CAA2C;oCAC3C,wCAAmC;wCACjC,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,cAAc,EAAE,aAAa,aAAa,EAAE;oCACtE;gCACF;;wBACF;oBACF;gBACF;;YAEA,6BAA6B;YAC7B,eAAe,EAAE,CAAC;uCAAuB,CAAC;oBACxC,QAAQ,GAAG,CAAC,wBAAwB;oBAEpC,yDAAyD;oBACzD,wCAAmC;wBACjC,OAAO,aAAa,CAAC,IAAI,YAAY,sBAAsB;4BACzD,QAAQ;wBACV;oBACF;gBACF;;YAEA,8CAA8C;YAC9C,eAAe,EAAE,CAAC;uCAAe,CAAC;oBAChC,QAAQ,GAAG,CAAC,gBAAgB;gBAC9B;;YAEA,eAAe,EAAE,CAAC;uCAAuB,CAAC;oBACxC,QAAQ,GAAG,CAAC,wBAAwB;gBACtC;;YAEA,qBAAqB;YACrB;uCAAO;oBACL,IAAI,UAAU,OAAO,EAAE;wBACrB,UAAU,OAAO,CAAC,UAAU;wBAC5B,UAAU,OAAO,GAAG;oBACtB;oBACA,UAAU;oBACV,eAAe;gBACjB;;QACF;8BAAG;QAAC;QAAiB;QAAM;KAAgB;IAE3C,mBAAmB;IACnB,MAAM,kBAAkB,CAAC;QACvB,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,oBAAoB;QAClC;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,qBAAqB;QACnC;IACF;IAEA,MAAM,wBAAwB,CAAC;QAM7B,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,6BAA6B;QAC3C;IACF;IAEA,MAAM,qBAAqB,CAAC;QAM1B,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,mBAAmB;QACjC;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAlKa;;QAGuB,sHAAA,CAAA,eAAY;QAClB,sHAAA,CAAA,uBAAoB;;;AAiK3C,MAAM,uBAAuB,CAAC;;IACnC,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAAG;IACtD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAEtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,UAAU,eAAe;gBAC3B,gBAAgB;gBAEhB,MAAM;8EAA0B,CAAC;wBAC/B,IAAI,KAAK,aAAa,KAAK,eAAe;4BACxC;0FAAsB,CAAA,OAAQ;2CAAI;wCAAM;qCAAK;;wBAC/C;oBACF;;gBAEA,OAAO,EAAE,CAAC,uBAAuB;gBAEjC;sDAAO;wBACL,iBAAiB;wBACjB,OAAO,GAAG,CAAC,uBAAuB;oBACpC;;YACF;QACF;yCAAG;QAAC;QAAQ;QAAe;QAAiB;KAAiB;IAE7D,OAAO;QACL;IACF;AACF;IA1Ba;;QAC2C", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/providers/SocketProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState } from 'react';\nimport { Socket } from 'socket.io-client';\nimport { useSocket } from '@/hooks/useSocket';\n\ninterface SocketContextType {\n  socket: Socket | null;\n  isConnected: boolean;\n  joinAppointment: (appointmentId: string) => void;\n  leaveAppointment: (appointmentId: string) => void;\n  emitAppointmentUpdate: (data: {\n    appointmentId: string;\n    status: string;\n    recipientId: string;\n    message: string;\n  }) => void;\n  emitNewAppointment: (data: {\n    appointmentId: string;\n    doctorId: string;\n    patientId: string;\n    message: string;\n  }) => void;\n}\n\nconst SocketContext = createContext<SocketContextType | undefined>(undefined);\n\nexport const useSocketContext = () => {\n  const context = useContext(SocketContext);\n  if (context === undefined) {\n    throw new Error('useSocketContext must be used within a SocketProvider');\n  }\n  return context;\n};\n\ninterface SocketProviderProps {\n  children: React.ReactNode;\n}\n\nexport default function SocketProvider({ children }: SocketProviderProps) {\n  const socketData = useSocket();\n\n  return (\n    <SocketContext.Provider value={socketData}>\n      {children}\n    </SocketContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAyBA,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAiC;AAE5D,MAAM,mBAAmB;;IAC9B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAYE,SAAS,eAAe,EAAE,QAAQ,EAAuB;;IACtE,MAAM,aAAa,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IAE3B,qBACE,6LAAC,cAAc,QAAQ;QAAC,OAAO;kBAC5B;;;;;;AAGP;IARwB;;QACH,4HAAA,CAAA,YAAS;;;KADN", "debugId": null}}, {"offset": {"line": 474, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/providers/AuthProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport { useRouter } from 'next/navigation';\n\ninterface User {\n  id: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  role: 'patient' | 'doctor';\n  phone?: string;\n  profileImage?: string;\n  isEmailVerified: boolean;\n}\n\ninterface AuthContextType {\n  user: User | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;\n  register: (userData: RegisterData) => Promise<{ success: boolean; error?: string }>;\n  logout: () => Promise<void>;\n  updateUser: (userData: Partial<User>) => void;\n}\n\ninterface RegisterData {\n  email: string;\n  password: string;\n  firstName: string;\n  lastName: string;\n  role: 'patient' | 'doctor';\n  phone?: string;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport default function AuthProvider({ children }: AuthProviderProps) {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const router = useRouter();\n\n  // Check if user is authenticated on app load\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n\n  const checkAuthStatus = async () => {\n    try {\n      // Check if we're on the client side\n      if (typeof window === 'undefined') {\n        setIsLoading(false);\n        return;\n      }\n\n      const token = localStorage.getItem('authToken');\n      console.log('AuthProvider: Checking auth status, localStorage token exists:', !!token);\n\n      // Try to authenticate with localStorage token first\n      let response;\n      if (token) {\n        response = await fetch('/api/auth/me', {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n          },\n        });\n      } else {\n        // If no localStorage token, try with cookies (server-side token)\n        console.log('AuthProvider: No localStorage token, checking server-side cookie');\n        response = await fetch('/api/auth/me');\n      }\n\n      if (response.ok) {\n        const userData = await response.json();\n        console.log('AuthProvider: User authenticated:', userData.data.user.email);\n        setUser(userData.data.user);\n\n        // If we authenticated via cookie but don't have localStorage token,\n        // this means localStorage was cleared but cookie is still valid\n        if (!token) {\n          console.log('AuthProvider: Authenticated via cookie but no localStorage token - this explains the redirect issue');\n        }\n      } else {\n        // Authentication failed\n        console.log('AuthProvider: Authentication failed, clearing auth state');\n        localStorage.removeItem('authToken');\n\n        // Also call logout to clear server-side cookie\n        try {\n          await fetch('/api/auth/logout', {\n            method: 'POST',\n            headers: token ? {\n              'Authorization': `Bearer ${token}`,\n            } : {},\n          });\n        } catch (logoutError) {\n          console.error('Failed to clear server-side auth:', logoutError);\n        }\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      localStorage.removeItem('authToken');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const login = async (email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        localStorage.setItem('authToken', data.data.token);\n        setUser(data.data.user);\n        return { success: true };\n      } else {\n        return { success: false, error: data.error || 'Login failed' };\n      }\n    } catch (error) {\n      return { success: false, error: 'Network error. Please try again.' };\n    }\n  };\n\n  const register = async (userData: RegisterData) => {\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(userData),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        localStorage.setItem('authToken', data.data.token);\n        setUser(data.data.user);\n        return { success: true };\n      } else {\n        return { success: false, error: data.error || 'Registration failed' };\n      }\n    } catch (error) {\n      return { success: false, error: 'Network error. Please try again.' };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      // Check if we're on the client side\n      if (typeof window === 'undefined') {\n        return;\n      }\n\n      // Clear client-side state first\n      localStorage.removeItem('authToken');\n      setUser(null);\n\n      const token = localStorage.getItem('authToken');\n      if (token) {\n        await fetch('/api/auth/logout', {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n          },\n        });\n      } else {\n        // Call logout API without token to clear server-side cookie\n        await fetch('/api/auth/logout', {\n          method: 'POST',\n        });\n      }\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Ensure client-side state is cleared\n      localStorage.removeItem('authToken');\n      setUser(null);\n\n      // Force a hard redirect to home page to bypass any middleware issues\n      window.location.href = '/';\n    }\n  };\n\n  const updateUser = (userData: Partial<User>) => {\n    if (user) {\n      setUser({ ...user, ...userData });\n    }\n  };\n\n  const value: AuthContextType = {\n    user,\n    isLoading,\n    isAuthenticated: !!user,\n    login,\n    register,\n    logout,\n    updateUser,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAmCA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAYD,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAClE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,oCAAoC;YACpC,uCAAmC;;YAGnC;YAEA,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,QAAQ,GAAG,CAAC,kEAAkE,CAAC,CAAC;YAEhF,oDAAoD;YACpD,IAAI;YACJ,IAAI,OAAO;gBACT,WAAW,MAAM,MAAM,gBAAgB;oBACrC,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBACpC;gBACF;YACF,OAAO;gBACL,iEAAiE;gBACjE,QAAQ,GAAG,CAAC;gBACZ,WAAW,MAAM,MAAM;YACzB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,WAAW,MAAM,SAAS,IAAI;gBACpC,QAAQ,GAAG,CAAC,qCAAqC,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;gBACzE,QAAQ,SAAS,IAAI,CAAC,IAAI;gBAE1B,oEAAoE;gBACpE,gEAAgE;gBAChE,IAAI,CAAC,OAAO;oBACV,QAAQ,GAAG,CAAC;gBACd;YACF,OAAO;gBACL,wBAAwB;gBACxB,QAAQ,GAAG,CAAC;gBACZ,aAAa,UAAU,CAAC;gBAExB,+CAA+C;gBAC/C,IAAI;oBACF,MAAM,MAAM,oBAAoB;wBAC9B,QAAQ;wBACR,SAAS,QAAQ;4BACf,iBAAiB,CAAC,OAAO,EAAE,OAAO;wBACpC,IAAI,CAAC;oBACP;gBACF,EAAE,OAAO,aAAa;oBACpB,QAAQ,KAAK,CAAC,qCAAqC;gBACrD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,aAAa,UAAU,CAAC;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,aAAa,OAAO,CAAC,aAAa,KAAK,IAAI,CAAC,KAAK;gBACjD,QAAQ,KAAK,IAAI,CAAC,IAAI;gBACtB,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK,IAAI;gBAAe;YAC/D;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAmC;QACrE;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,aAAa,OAAO,CAAC,aAAa,KAAK,IAAI,CAAC,KAAK;gBACjD,QAAQ,KAAK,IAAI,CAAC,IAAI;gBACtB,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK,IAAI;gBAAsB;YACtE;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAmC;QACrE;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,oCAAoC;YACpC,uCAAmC;;YAEnC;YAEA,gCAAgC;YAChC,aAAa,UAAU,CAAC;YACxB,QAAQ;YAER,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,MAAM,MAAM,oBAAoB;oBAC9B,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBACpC;gBACF;YACF,OAAO;gBACL,4DAA4D;gBAC5D,MAAM,MAAM,oBAAoB;oBAC9B,QAAQ;gBACV;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,sCAAsC;YACtC,aAAa,UAAU,CAAC;YACxB,QAAQ;YAER,qEAAqE;YACrE,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,MAAM;YACR,QAAQ;gBAAE,GAAG,IAAI;gBAAE,GAAG,QAAQ;YAAC;QACjC;IACF;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IAhLwB;;QAGP,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}