'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function SignUpRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Immediate redirect to the new register page
    router.replace('/register');
  }, [router]);

  // Also use a meta refresh as backup
  useEffect(() => {
    const timer = setTimeout(() => {
      window.location.href = '/register';
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      <meta httpEquiv="refresh" content="0;url=/register" />
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting to registration...</p>
          <p className="text-sm text-gray-500 mt-2">
            If you're not redirected automatically, <a href="/register" className="text-blue-600 hover:underline">click here</a>
          </p>
        </div>
      </div>
    </>
  );
}
