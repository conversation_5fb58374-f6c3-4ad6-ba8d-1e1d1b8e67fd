'use client';

import { useAuth } from '@/components/providers/AuthProvider';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function TestProfilePage() {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    console.log('TestProfile - Auth state:', { user, isLoading, isAuthenticated });
  }, [user, isLoading, isAuthenticated]);

  if (isLoading) {
    return <div>Loading authentication...</div>;
  }

  if (!isAuthenticated) {
    return <div>Not authenticated. <button onClick={() => router.push('/login')}>Go to Login</button></div>;
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Profile Test Page</h1>
      <div className="space-y-2">
        <p><strong>User ID:</strong> {user?.id}</p>
        <p><strong>Email:</strong> {user?.email}</p>
        <p><strong>Name:</strong> {user?.firstName} {user?.lastName}</p>
        <p><strong>Role:</strong> {user?.role}</p>
        <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
      </div>
      
      <div className="mt-6 space-x-4">
        <button 
          onClick={() => router.push(user?.role === 'doctor' ? '/doctor/profile' : '/patient/profile')}
          className="bg-blue-500 text-white px-4 py-2 rounded"
        >
          Go to Profile Page
        </button>
        <button 
          onClick={() => router.push(user?.role === 'doctor' ? '/doctor/dashboard' : '/patient/dashboard')}
          className="bg-green-500 text-white px-4 py-2 rounded"
        >
          Go to Dashboard
        </button>
      </div>
    </div>
  );
}
