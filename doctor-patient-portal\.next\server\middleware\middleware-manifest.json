{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_758e4a24._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4db2b2d0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*){(\\\\.json)}?", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(api|trpc)(.*){(\\\\.json)}?", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pvy1GiWnl9dxH35kCJNqLxyBSkMc6eTE+5YJ8NcLVqE=", "__NEXT_PREVIEW_MODE_ID": "5be2a147189bb042072344ba7f120b8e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0ab7d964a7a70a1642ca66007b1eb6d29dcbab7980e3772c33fd0b632a3af4f9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b4a04897d5ff8ec19dd85418d8f16a26913fa76019401bdcf571d4c620b3f79b"}}}, "instrumentation": null, "functions": {}}