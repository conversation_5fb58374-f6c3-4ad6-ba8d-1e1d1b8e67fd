import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import connectDB from '@/lib/db';
import User from '@/models/User';
import Patient from '@/models/Patient';
import Doctor from '@/models/Doctor';
import { ApiResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const { email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Email and password are required',
      }, { status: 400 });
    }

    // Find user by email
    const user = await User.findOne({ email: email.toLowerCase() });

    if (!user) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Invalid credentials',
      }, { status: 401 });
    }

    console.log('User found:', user.email);
    console.log('User has password:', !!user.password);
    console.log('Password field type:', typeof user.password);

    // Check if user has a password
    if (!user.password) {
      console.log('User has no password field - this user might have been created without a password');
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Account setup incomplete. Please use the "Forgot Password" option to set up your password.',
        needsPasswordReset: true,
      }, { status: 422 }); // 422 Unprocessable Entity - indicates account needs setup
    }

    // Compare password
    let isPasswordValid = false;
    try {
      isPasswordValid = await user.comparePassword(password);
    } catch (error) {
      console.log('Password comparison failed:', error);
      // If password comparison fails due to missing password, treat as invalid
      isPasswordValid = false;
    }

    if (!isPasswordValid) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Invalid credentials',
      }, { status: 401 });
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user._id,
        email: user.email,
        role: user.role 
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '7d' }
    );

    // Fetch profile data from appropriate collection
    let profileData;
    if (user.role === 'patient') {
      profileData = await Patient.findOne({ userId: user._id });
    } else if (user.role === 'doctor') {
      profileData = await Doctor.findOne({ userId: user._id });
    }

    // Create response with user data
    const userData = {
      id: user._id,
      email: user.email,
      firstName: profileData?.firstName || '',
      lastName: profileData?.lastName || '',
      role: user.role,
      phone: profileData?.phone || '',
      profileImage: profileData?.profileImage,
      isEmailVerified: user.isEmailVerified,
    };

    const response = NextResponse.json<ApiResponse>({
      success: true,
      data: {
        user: userData,
        token,
      },
    });

    // Set HTTP-only cookie for additional security
    response.cookies.set('authToken', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60, // 7 days
    });

    return response;

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Internal server error',
    }, { status: 500 });
  }
}
