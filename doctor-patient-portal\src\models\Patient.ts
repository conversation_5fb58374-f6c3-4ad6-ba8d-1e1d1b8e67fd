import mongoose, { Document, Schema } from 'mongoose';
import { Patient as IPatient } from '@/types';

export interface PatientDocument extends Document, Omit<IPatient, '_id'> {}

const EmergencyContactSchema = new Schema({
  name: {
    type: String,
    trim: true,
  },
  relationship: {
    type: String,
    trim: true,
  },
  phone: {
    type: String,
    trim: true,
  },
});

const MedicalHistorySchema = new Schema({
  allergies: {
    type: String,
    trim: true,
  },
  medications: {
    type: String,
    trim: true,
  },
  conditions: {
    type: String,
    trim: true,
  },
  surgeries: {
    type: String,
    trim: true,
  },
});

const InsuranceSchema = new Schema({
  provider: {
    type: String,
    trim: true,
  },
  policyNumber: {
    type: String,
    trim: true,
  },
  groupNumber: {
    type: String,
    trim: true,
  },
});

const PatientSchema = new Schema<PatientDocument>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
  },
  firstName: {
    type: String,
    required: true,
    trim: true,
  },
  lastName: {
    type: String,
    required: true,
    trim: true,
  },
  phone: {
    type: String,
    trim: true,
  },
  profileImage: {
    type: String,
  },
  dateOfBirth: {
    type: Date,
  },
  gender: {
    type: String,
    enum: ['male', 'female', 'other', 'prefer-not-to-say'],
  },
  address: {
    type: String,
    trim: true,
  },
  city: {
    type: String,
    trim: true,
  },
  state: {
    type: String,
    trim: true,
  },
  zipCode: {
    type: String,
    trim: true,
  },
  emergencyContact: {
    type: EmergencyContactSchema,
  },
  medicalHistory: {
    type: MedicalHistorySchema,
  },
  insurance: {
    type: InsuranceSchema,
  },
}, {
  timestamps: true,
});

// Indexes for better query performance
// Note: userId index is already created by unique: true
PatientSchema.index({ firstName: 1, lastName: 1 });
PatientSchema.index({ city: 1 });
PatientSchema.index({ state: 1 });

export default mongoose.models.Patient || mongoose.model<PatientDocument>('Patient', PatientSchema);
