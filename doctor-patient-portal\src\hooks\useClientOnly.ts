import { useState, useEffect } from 'react';

/**
 * Custom hook to handle client-side only state to prevent hydration mismatches
 * Returns true only after the component has mounted on the client side
 */
export function useClientOnly() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Custom hook to handle responsive breakpoints in a SSR-safe way
 */
export function useResponsive() {
  const [screenSize, setScreenSize] = useState({
    isSmall: false,
    isMedium: false,
    isLarge: false,
    isXLarge: false,
  });
  const isClient = useClientOnly();

  useEffect(() => {
    if (!isClient) return;

    const updateScreenSize = () => {
      const width = window.innerWidth;
      setScreenSize({
        isSmall: width >= 640,
        isMedium: width >= 768,
        isLarge: width >= 1024,
        isXLarge: width >= 1280,
      });
    };

    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);

    return () => window.removeEventListener('resize', updateScreenSize);
  }, [isClient]);

  return { ...screenSize, isClient };
}
