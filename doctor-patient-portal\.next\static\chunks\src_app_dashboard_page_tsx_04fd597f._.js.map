{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/components/providers/AuthProvider';\n\nexport default function DashboardRedirect() {\n  const { user, isLoading, isAuthenticated } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (isLoading) return;\n\n    if (!isAuthenticated || !user) {\n      router.push('/login');\n      return;\n    }\n\n    // Redirect based on role\n    if (user.role === 'doctor') {\n      router.push('/doctor/dashboard');\n    } else if (user.role === 'patient') {\n      router.push('/patient/dashboard');\n    } else {\n      router.push('/onboarding');\n    }\n  }, [user, isLoading, isAuthenticated, router]);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n        <p className=\"text-gray-600\">Redirecting to your dashboard...</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,WAAW;YAEf,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,yBAAyB;YACzB,IAAI,KAAK,IAAI,KAAK,UAAU;gBAC1B,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,KAAK,IAAI,KAAK,WAAW;gBAClC,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF;sCAAG;QAAC;QAAM;QAAW;QAAiB;KAAO;IAE7C,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAIrC;GA9BwB;;QACuB,kJAAA,CAAA,UAAO;QACrC,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}