{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/logout/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/components/providers/AuthProvider';\n\nexport default function LogoutPage() {\n  const router = useRouter();\n  const { logout } = useAuth();\n\n  useEffect(() => {\n    const performLogout = async () => {\n      try {\n        await logout();\n        // Clear localStorage as well\n        localStorage.removeItem('authToken');\n        // Redirect to home page\n        router.push('/');\n      } catch (error) {\n        console.error('Logout error:', error);\n        // Force redirect anyway\n        localStorage.removeItem('authToken');\n        router.push('/');\n      }\n    };\n\n    performLogout();\n  }, [logout, router]);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n        <p className=\"text-gray-600\">Logging out...</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD;IAEzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,MAAM;gBACN,6BAA6B;gBAC7B,aAAa,UAAU,CAAC;gBACxB,wBAAwB;gBACxB,OAAO,IAAI,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;gBAC/B,wBAAwB;gBACxB,aAAa,UAAU,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd;QACF;QAEA;IACF,GAAG;QAAC;QAAQ;KAAO;IAEnB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAIrC", "debugId": null}}]}