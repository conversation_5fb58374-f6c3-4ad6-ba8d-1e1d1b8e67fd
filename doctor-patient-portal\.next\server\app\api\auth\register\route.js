const CHUNK_PUBLIC_PATH = "server/app/api/auth/register/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_acd3fb9b._.js");
runtime.loadChunk("server/chunks/node_modules_nodemailer_a9f338b9._.js");
runtime.loadChunk("server/chunks/node_modules_752bd2c4._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__8fae0da8._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/auth/register/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/auth/register/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/auth/register/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
