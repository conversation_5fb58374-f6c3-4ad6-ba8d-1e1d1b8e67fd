import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import User from '@/models/User';
import { authenticateUser } from '@/lib/auth';
import { ApiResponse } from '@/types';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate user
    let user;
    try {
      user = await authenticateUser(request);
    } catch (error) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    // Check if user is a patient
    if (user.role !== 'patient') {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Access denied. Only patients can access this endpoint.',
      }, { status: 403 });
    }

    // Return user profile data
    const patientData = {
      id: user._id,
      email: user.email,
      firstName: user.profile?.firstName,
      lastName: user.profile?.lastName,
      phone: user.profile?.phone,
      dateOfBirth: user.profile?.dateOfBirth,
      gender: user.profile?.gender,
      address: user.profile?.address,
      city: user.profile?.city,
      state: user.profile?.state,
      zipCode: user.profile?.zipCode,
      emergencyContact: user.profile?.emergencyContact,
      medicalHistory: user.profile?.medicalHistory,
      insurance: user.profile?.insurance,
    };

    return NextResponse.json<ApiResponse>({
      success: true,
      data: patientData,
    });

  } catch (error) {
    console.error('Get patient profile error:', error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to fetch patient profile',
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate user
    let user;
    try {
      user = await authenticateUser(request);
    } catch (error) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    // Check if user is a patient
    if (user.role !== 'patient') {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Access denied. Only patients can access this endpoint.',
      }, { status: 403 });
    }

    const body = await request.json();
    const {
      firstName,
      lastName,
      phone,
      dateOfBirth,
      gender,
      address,
      city,
      state,
      zipCode,
      emergencyContact,
      medicalHistory,
      insurance
    } = body;

    // Update user profile
    const updatedUser = await User.findByIdAndUpdate(
      user._id,
      {
        $set: {
          'profile.firstName': firstName,
          'profile.lastName': lastName,
          'profile.phone': phone,
          'profile.dateOfBirth': dateOfBirth,
          'profile.gender': gender,
          'profile.address': address,
          'profile.city': city,
          'profile.state': state,
          'profile.zipCode': zipCode,
          'profile.emergencyContact': emergencyContact,
          'profile.medicalHistory': medicalHistory,
          'profile.insurance': insurance,
        }
      },
      { new: true, runValidators: true }
    );

    if (!updatedUser) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Failed to update patient profile',
      }, { status: 500 });
    }

    const patientData = {
      id: updatedUser._id,
      email: updatedUser.email,
      firstName: updatedUser.profile?.firstName,
      lastName: updatedUser.profile?.lastName,
      phone: updatedUser.profile?.phone,
      dateOfBirth: updatedUser.profile?.dateOfBirth,
      gender: updatedUser.profile?.gender,
      address: updatedUser.profile?.address,
      city: updatedUser.profile?.city,
      state: updatedUser.profile?.state,
      zipCode: updatedUser.profile?.zipCode,
      emergencyContact: updatedUser.profile?.emergencyContact,
      medicalHistory: updatedUser.profile?.medicalHistory,
      insurance: updatedUser.profile?.insurance,
    };

    return NextResponse.json<ApiResponse>({
      success: true,
      data: patientData,
      message: 'Patient profile updated successfully',
    });

  } catch (error) {
    console.error('Update patient profile error:', error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to update patient profile',
    }, { status: 500 });
  }
}
