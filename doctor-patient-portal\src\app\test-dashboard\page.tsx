'use client';

import { useAuth } from '@/components/providers/AuthProvider';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function TestDashboardPage() {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();
  const [appointments, setAppointments] = useState([]);
  const [appointmentsLoading, setAppointmentsLoading] = useState(false);

  useEffect(() => {
    console.log('TestDashboard - Auth state:', { user, isLoading, isAuthenticated });
  }, [user, isLoading, isAuthenticated]);

  const testAppointmentsAPI = async () => {
    setAppointmentsLoading(true);
    try {
      const response = await fetch('/api/appointments?limit=5');
      const result = await response.json();
      console.log('Appointments API result:', result);
      if (result.success) {
        setAppointments(result.data);
      }
    } catch (error) {
      console.error('Appointments API error:', error);
    } finally {
      setAppointmentsLoading(false);
    }
  };

  if (isLoading) {
    return <div className="p-8">Loading authentication...</div>;
  }

  if (!isAuthenticated) {
    return (
      <div className="p-8">
        <p>Not authenticated.</p>
        <button 
          onClick={() => router.push('/login')}
          className="bg-blue-500 text-white px-4 py-2 rounded mt-4"
        >
          Go to Login
        </button>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Dashboard Test Page</h1>
      
      <div className="space-y-4 mb-6">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-semibold mb-2">Authentication Status</h2>
          <p><strong>User ID:</strong> {user?.id}</p>
          <p><strong>Email:</strong> {user?.email}</p>
          <p><strong>Name:</strong> {user?.firstName} {user?.lastName}</p>
          <p><strong>Role:</strong> {user?.role}</p>
          <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-semibold mb-2">API Test</h2>
          <button 
            onClick={testAppointmentsAPI}
            disabled={appointmentsLoading}
            className="bg-green-500 text-white px-4 py-2 rounded disabled:opacity-50"
          >
            {appointmentsLoading ? 'Loading...' : 'Test Appointments API'}
          </button>
          
          {appointments.length > 0 && (
            <div className="mt-4">
              <p><strong>Appointments found:</strong> {appointments.length}</p>
              <pre className="bg-white p-2 rounded mt-2 text-sm overflow-auto">
                {JSON.stringify(appointments, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
      
      <div className="space-x-4">
        <button 
          onClick={() => router.push(user?.role === 'doctor' ? '/doctor/dashboard' : '/patient/dashboard')}
          className="bg-blue-500 text-white px-4 py-2 rounded"
        >
          Go to Real Dashboard
        </button>
        
        <button 
          onClick={() => router.push(user?.role === 'doctor' ? '/doctor/profile' : '/patient/profile')}
          className="bg-purple-500 text-white px-4 py-2 rounded"
        >
          Go to Profile
        </button>

        <button 
          onClick={() => router.push('/dashboard')}
          className="bg-gray-500 text-white px-4 py-2 rounded"
        >
          Go to Dashboard Redirect
        </button>
      </div>
    </div>
  );
}
