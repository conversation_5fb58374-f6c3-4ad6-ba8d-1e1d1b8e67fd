{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/debug-auth/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useAuth } from '@/components/providers/AuthProvider';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\n\nexport default function DebugAuthPage() {\n  const { user, isAuthenticated, isLoading } = useAuth();\n  const [localStorageToken, setLocalStorageToken] = useState<string | null>(null);\n  const [apiResponse, setApiResponse] = useState<any>(null);\n\n  useEffect(() => {\n    // Check localStorage token\n    const token = localStorage.getItem('authToken');\n    setLocalStorageToken(token);\n\n    // Test API call\n    const testApi = async () => {\n      try {\n        const response = await fetch('/api/auth/me');\n        const data = await response.json();\n        setApiResponse({ status: response.status, data });\n      } catch (error) {\n        setApiResponse({ error: error.message });\n      }\n    };\n\n    testApi();\n  }, []);\n\n  const clearAllAuth = async () => {\n    try {\n      // Call logout API to clear server-side cookie\n      await fetch('/api/auth/logout', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,\n        },\n      });\n    } catch (error) {\n      console.error('Error calling logout API:', error);\n    }\n    \n    // Clear client-side storage\n    localStorage.removeItem('authToken');\n    \n    // Reload to reset all state\n    window.location.href = '/';\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-12\">\n      <div className=\"max-w-4xl mx-auto px-4\">\n        <Card>\n          <CardHeader>\n            <CardTitle>Authentication Debug Page</CardTitle>\n            <CardDescription>\n              This page helps diagnose authentication state issues\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            {/* AuthProvider State */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-2\">AuthProvider State</h3>\n              <div className=\"bg-gray-100 p-4 rounded-lg\">\n                <p><strong>isLoading:</strong> {isLoading.toString()}</p>\n                <p><strong>isAuthenticated:</strong> {isAuthenticated.toString()}</p>\n                <p><strong>user:</strong> {user ? JSON.stringify(user, null, 2) : 'null'}</p>\n              </div>\n            </div>\n\n            {/* localStorage Token */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-2\">localStorage Token</h3>\n              <div className=\"bg-gray-100 p-4 rounded-lg\">\n                <p><strong>Token exists:</strong> {!!localStorageToken}</p>\n                <p><strong>Token preview:</strong> {localStorageToken ? `${localStorageToken.substring(0, 50)}...` : 'null'}</p>\n              </div>\n            </div>\n\n            {/* API Response */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-2\">API /auth/me Response</h3>\n              <div className=\"bg-gray-100 p-4 rounded-lg\">\n                <pre className=\"text-sm overflow-auto\">\n                  {JSON.stringify(apiResponse, null, 2)}\n                </pre>\n              </div>\n            </div>\n\n            {/* Actions */}\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-semibold\">Actions</h3>\n              <div className=\"flex space-x-4\">\n                <Button onClick={clearAllAuth} variant=\"destructive\">\n                  Clear All Authentication\n                </Button>\n                <Button onClick={() => window.location.reload()} variant=\"outline\">\n                  Reload Page\n                </Button>\n                <Button onClick={() => window.location.href = '/'} variant=\"outline\">\n                  Go to Home\n                </Button>\n              </div>\n            </div>\n\n            {/* Explanation */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-2\">Issue Explanation</h3>\n              <div className=\"bg-blue-50 p-4 rounded-lg\">\n                <p className=\"text-sm\">\n                  If you're experiencing the \"Sign In redirects to dashboard\" issue, it's likely because:\n                </p>\n                <ul className=\"list-disc list-inside text-sm mt-2 space-y-1\">\n                  <li>There's a valid authentication cookie on the server-side (middleware sees you as authenticated)</li>\n                  <li>But no token in localStorage on the client-side (AuthProvider sees you as not authenticated)</li>\n                  <li>When you click \"Sign In\", the middleware redirects you to dashboard because it thinks you're authenticated</li>\n                  <li>But the client-side doesn't show you as authenticated because localStorage is empty</li>\n                </ul>\n                <p className=\"text-sm mt-2\">\n                  <strong>Solution:</strong> Click \"Clear All Authentication\" to sync both client and server states.\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAEpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,2BAA2B;YAC3B,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,qBAAqB;YAErB,gBAAgB;YAChB,MAAM;mDAAU;oBACd,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,eAAe;4BAAE,QAAQ,SAAS,MAAM;4BAAE;wBAAK;oBACjD,EAAE,OAAO,OAAO;wBACd,eAAe;4BAAE,OAAO,MAAM,OAAO;wBAAC;oBACxC;gBACF;;YAEA;QACF;kCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,8CAA8C;YAC9C,MAAM,MAAM,oBAAoB;gBAC9B,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,cAAc;gBAChE;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;QAEA,4BAA4B;QAC5B,aAAa,UAAU,CAAC;QAExB,4BAA4B;QAC5B,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAmB;oDAAE,UAAU,QAAQ;;;;;;;0DAClD,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAyB;oDAAE,gBAAgB,QAAQ;;;;;;;0DAC9D,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAc;oDAAE,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM,KAAK;;;;;;;;;;;;;;;;;;;0CAKtE,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAsB;oDAAE,CAAC,CAAC;;;;;;;0DACrC,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAuB;oDAAE,oBAAoB,GAAG,kBAAkB,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;;;;;;;;;;;;;;;;;;;0CAKzG,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ,KAAK,SAAS,CAAC,aAAa,MAAM;;;;;;;;;;;;;;;;;0CAMzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAS;gDAAc,SAAQ;0DAAc;;;;;;0DAGrD,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;gDAAI,SAAQ;0DAAU;;;;;;0DAGnE,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;gDAAK,SAAQ;0DAAU;;;;;;;;;;;;;;;;;;0CAOzE,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAU;;;;;;0DAGvB,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;;;;;;;0DAEN,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;kEAAO;;;;;;oDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C;GA3HwB;;QACuB,kJAAA,CAAA,UAAO;;;KAD9B", "debugId": null}}]}