{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/db.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/doctor-patient-portal';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\n// Global is used here to maintain a cached connection across hot reloads in development\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached!.conn) {\n    return cached!.conn;\n  }\n\n  if (!cached!.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached!.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Connected to MongoDB');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached!.conn = await cached!.promise;\n  } catch (e) {\n    cached!.promise = null;\n    throw e;\n  }\n\n  return cached!.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAYA,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAQ,IAAI,EAAE;QAChB,OAAO,OAAQ,IAAI;IACrB;IAEA,IAAI,CAAC,OAAQ,OAAO,EAAE;QACpB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAQ,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YAC1D,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAQ,IAAI,GAAG,MAAM,OAAQ,OAAO;IACtC,EAAE,OAAO,GAAG;QACV,OAAQ,OAAO,GAAG;QAClB,MAAM;IACR;IAEA,OAAO,OAAQ,IAAI;AACrB;uCAEe", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport bcrypt from 'bcryptjs';\nimport { User as IUser, UserProfile } from '@/types';\n\nexport interface UserDocument extends Document, Omit<IUser, '_id'> {\n  password: string;\n  resetPasswordToken?: string;\n  resetPasswordExpiry?: Date;\n  isEmailVerified: boolean;\n  emailVerificationToken?: string;\n  emailVerificationExpiry?: Date;\n  comparePassword(candidatePassword: string): Promise<boolean>;\n}\n\nconst UserProfileSchema = new Schema<UserProfile>({\n  firstName: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  lastName: {\n    type: String,\n    required: true,\n    trim: true,\n  },\n  phone: {\n    type: String,\n    trim: true,\n  },\n  profileImage: {\n    type: String,\n  },\n  dateOfBirth: {\n    type: Date,\n  },\n});\n\nconst UserSchema = new Schema<UserDocument>({\n  email: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true,\n  },\n  password: {\n    type: String,\n    required: true,\n    minlength: 6,\n  },\n  role: {\n    type: String,\n    enum: ['doctor', 'patient'],\n    required: true,\n  },\n  profile: {\n    type: UserProfileSchema,\n    required: true,\n  },\n  isEmailVerified: {\n    type: Boolean,\n    default: false,\n  },\n  emailVerificationToken: {\n    type: String,\n  },\n  emailVerificationExpiry: {\n    type: Date,\n  },\n  resetPasswordToken: {\n    type: String,\n  },\n  resetPasswordExpiry: {\n    type: Date,\n  },\n}, {\n  timestamps: true,\n});\n\n// Hash password before saving\nUserSchema.pre('save', async function (next) {\n  if (!this.isModified('password')) return next();\n\n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error as Error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Clean JSON output (remove sensitive fields)\nUserSchema.methods.toJSON = function () {\n  const userObject = this.toObject();\n  delete userObject.password;\n  delete userObject.resetPasswordToken;\n  delete userObject.resetPasswordExpiry;\n  delete userObject.emailVerificationToken;\n  delete userObject.emailVerificationExpiry;\n  delete userObject.__v;\n  return userObject;\n};\n\n// Indexes for better query performance\n// Note: email index is already created by unique: true\nUserSchema.index({ role: 1 });\n\nexport default mongoose.models.User || mongoose.model<UserDocument>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAaA,MAAM,oBAAoB,IAAI,yGAAA,CAAA,SAAM,CAAc;IAChD,WAAW;QACT,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,cAAc;QACZ,MAAM;IACR;IACA,aAAa;QACX,MAAM;IACR;AACF;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAe;IAC1C,OAAO;QACL,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAU;SAAU;QAC3B,UAAU;IACZ;IACA,SAAS;QACP,MAAM;QACN,UAAU;IACZ;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,wBAAwB;QACtB,MAAM;IACR;IACA,yBAAyB;QACvB,MAAM;IACR;IACA,oBAAoB;QAClB,MAAM;IACR;IACA,qBAAqB;QACnB,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAgB,IAAI;IACzC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAgB,iBAAyB;IAC5E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,8CAA8C;AAC9C,WAAW,OAAO,CAAC,MAAM,GAAG;IAC1B,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO,WAAW,kBAAkB;IACpC,OAAO,WAAW,mBAAmB;IACrC,OAAO,WAAW,sBAAsB;IACxC,OAAO,WAAW,uBAAuB;IACzC,OAAO,WAAW,GAAG;IACrB,OAAO;AACT;AAEA,uCAAuC;AACvC,uDAAuD;AACvD,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;uCAEZ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAe,QAAQ", "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/api/test-auth/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/db';\nimport User from '@/models/User';\nimport bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\n\nexport async function POST(request: NextRequest) {\n  try {\n    console.log('🧪 Testing authentication system...');\n    await connectDB();\n    \n    const { action } = await request.json();\n    \n    if (action === 'create-test-user') {\n      // Create a test user\n      const testUser = {\n        email: '<EMAIL>',\n        password: 'password123',\n        firstName: 'Test',\n        lastName: 'User',\n        role: 'patient'\n      };\n      \n      // Check if user already exists\n      const existingUser = await User.findOne({ email: testUser.email });\n      if (existingUser) {\n        await User.deleteOne({ email: testUser.email });\n        console.log('🗑️ Deleted existing test user');\n      }\n      \n      // Create new user\n      const user = new User({\n        email: testUser.email,\n        password: testUser.password,\n        role: testUser.role,\n        profile: {\n          firstName: testUser.firstName,\n          lastName: testUser.lastName,\n        },\n        isEmailVerified: false,\n      });\n      \n      await user.save();\n      console.log('✅ Test user created successfully');\n      \n      // Test password comparison\n      const isPasswordValid = await user.comparePassword(testUser.password);\n      console.log('🔐 Password comparison test:', isPasswordValid ? 'PASSED' : 'FAILED');\n      \n      // Test JWT generation\n      const token = jwt.sign(\n        { \n          userId: user._id,\n          email: user.email,\n          role: user.role \n        },\n        process.env.JWT_SECRET || 'your-secret-key',\n        { expiresIn: '7d' }\n      );\n      console.log('🎫 JWT token generated successfully');\n      \n      // Test JWT verification\n      try {\n        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');\n        console.log('✅ JWT verification test: PASSED');\n      } catch (error) {\n        console.log('❌ JWT verification test: FAILED');\n      }\n      \n      return NextResponse.json({\n        success: true,\n        message: 'Test user created and authentication tested successfully',\n        data: {\n          user: user.toJSON(),\n          passwordTest: isPasswordValid,\n          tokenGenerated: !!token,\n        }\n      });\n    }\n    \n    if (action === 'test-login') {\n      // Test login functionality\n      const testCredentials = {\n        email: '<EMAIL>',\n        password: 'password123'\n      };\n      \n      const user = await User.findOne({ email: testCredentials.email });\n      if (!user) {\n        return NextResponse.json({\n          success: false,\n          message: 'Test user not found. Create test user first.'\n        }, { status: 404 });\n      }\n      \n      const isPasswordValid = await user.comparePassword(testCredentials.password);\n      if (!isPasswordValid) {\n        return NextResponse.json({\n          success: false,\n          message: 'Password validation failed'\n        }, { status: 401 });\n      }\n      \n      const token = jwt.sign(\n        { \n          userId: user._id,\n          email: user.email,\n          role: user.role \n        },\n        process.env.JWT_SECRET || 'your-secret-key',\n        { expiresIn: '7d' }\n      );\n      \n      return NextResponse.json({\n        success: true,\n        message: 'Login test successful',\n        data: {\n          user: user.toJSON(),\n          token: token\n        }\n      });\n    }\n    \n    return NextResponse.json({\n      success: false,\n      message: 'Invalid action. Use \"create-test-user\" or \"test-login\"'\n    }, { status: 400 });\n    \n  } catch (error) {\n    console.error('❌ Authentication test failed:', error);\n    \n    return NextResponse.json({\n      success: false,\n      message: 'Authentication test failed',\n      error: error instanceof Error ? error.message : 'Unknown error',\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAEA;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ,IAAI;QAErC,IAAI,WAAW,oBAAoB;YACjC,qBAAqB;YACrB,MAAM,WAAW;gBACf,OAAO;gBACP,UAAU;gBACV,WAAW;gBACX,UAAU;gBACV,MAAM;YACR;YAEA,+BAA+B;YAC/B,MAAM,eAAe,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;gBAAE,OAAO,SAAS,KAAK;YAAC;YAChE,IAAI,cAAc;gBAChB,MAAM,uHAAA,CAAA,UAAI,CAAC,SAAS,CAAC;oBAAE,OAAO,SAAS,KAAK;gBAAC;gBAC7C,QAAQ,GAAG,CAAC;YACd;YAEA,kBAAkB;YAClB,MAAM,OAAO,IAAI,uHAAA,CAAA,UAAI,CAAC;gBACpB,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,MAAM,SAAS,IAAI;gBACnB,SAAS;oBACP,WAAW,SAAS,SAAS;oBAC7B,UAAU,SAAS,QAAQ;gBAC7B;gBACA,iBAAiB;YACnB;YAEA,MAAM,KAAK,IAAI;YACf,QAAQ,GAAG,CAAC;YAEZ,2BAA2B;YAC3B,MAAM,kBAAkB,MAAM,KAAK,eAAe,CAAC,SAAS,QAAQ;YACpE,QAAQ,GAAG,CAAC,gCAAgC,kBAAkB,WAAW;YAEzE,sBAAsB;YACtB,MAAM,QAAQ,uIAAA,CAAA,UAAG,CAAC,IAAI,CACpB;gBACE,QAAQ,KAAK,GAAG;gBAChB,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI;YACjB,GACA,QAAQ,GAAG,CAAC,UAAU,IAAI,mBAC1B;gBAAE,WAAW;YAAK;YAEpB,QAAQ,GAAG,CAAC;YAEZ,wBAAwB;YACxB,IAAI;gBACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;gBAC5D,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC;YACd;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,MAAM;oBACJ,MAAM,KAAK,MAAM;oBACjB,cAAc;oBACd,gBAAgB,CAAC,CAAC;gBACpB;YACF;QACF;QAEA,IAAI,WAAW,cAAc;YAC3B,2BAA2B;YAC3B,MAAM,kBAAkB;gBACtB,OAAO;gBACP,UAAU;YACZ;YAEA,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;gBAAE,OAAO,gBAAgB,KAAK;YAAC;YAC/D,IAAI,CAAC,MAAM;gBACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS;gBACX,GAAG;oBAAE,QAAQ;gBAAI;YACnB;YAEA,MAAM,kBAAkB,MAAM,KAAK,eAAe,CAAC,gBAAgB,QAAQ;YAC3E,IAAI,CAAC,iBAAiB;gBACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS;gBACX,GAAG;oBAAE,QAAQ;gBAAI;YACnB;YAEA,MAAM,QAAQ,uIAAA,CAAA,UAAG,CAAC,IAAI,CACpB;gBACE,QAAQ,KAAK,GAAG;gBAChB,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI;YACjB,GACA,QAAQ,GAAG,CAAC,UAAU,IAAI,mBAC1B;gBAAE,WAAW;YAAK;YAGpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,MAAM;oBACJ,MAAM,KAAK,MAAM;oBACjB,OAAO;gBACT;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAE/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}