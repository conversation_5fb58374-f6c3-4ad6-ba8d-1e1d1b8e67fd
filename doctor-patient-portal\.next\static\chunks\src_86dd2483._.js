(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ui/sonner.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Toaster": (()=>Toaster)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
const Toaster = ({ ...props })=>{
    _s();
    const { theme = "system" } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Toaster"], {
        theme: theme,
        className: "toaster group",
        style: {
            "--normal-bg": "var(--popover)",
            "--normal-text": "var(--popover-foreground)",
            "--normal-border": "var(--border)"
        },
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/sonner.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
};
_s(Toaster, "EriOrahfenYKDCErPq+L6926Dw4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"]
    ];
});
_c = Toaster;
;
var _c;
__turbopack_context__.k.register(_c, "Toaster");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/store.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAppointmentStore": (()=>useAppointmentStore),
    "useAuthStore": (()=>useAuthStore),
    "useNotificationStore": (()=>useNotificationStore),
    "useUIStore": (()=>useUIStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
;
;
const useAuthStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        login: (user)=>set({
                user,
                isAuthenticated: true
            }),
        logout: ()=>set({
                user: null,
                isAuthenticated: false
            }),
        updateUser: (updates)=>{
            const currentUser = get().user;
            if (currentUser) {
                set({
                    user: {
                        ...currentUser,
                        ...updates
                    }
                });
            }
        },
        setLoading: (loading)=>set({
                isLoading: loading
            })
    }), {
    name: 'auth-storage',
    partialize: (state)=>({
            user: state.user,
            isAuthenticated: state.isAuthenticated
        })
}));
const useAppointmentStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])((set, get)=>({
        appointments: [],
        isLoading: false,
        setAppointments: (appointments)=>set({
                appointments
            }),
        addAppointment: (appointment)=>{
            const appointments = get().appointments;
            set({
                appointments: [
                    appointment,
                    ...appointments
                ]
            });
        },
        updateAppointment: (id, updates)=>{
            const appointments = get().appointments;
            const updatedAppointments = appointments.map((apt)=>apt._id === id ? {
                    ...apt,
                    ...updates
                } : apt);
            set({
                appointments: updatedAppointments
            });
        },
        removeAppointment: (id)=>{
            const appointments = get().appointments;
            const filteredAppointments = appointments.filter((apt)=>apt._id !== id);
            set({
                appointments: filteredAppointments
            });
        },
        setLoading: (loading)=>set({
                isLoading: loading
            })
    }));
const useNotificationStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])((set, get)=>({
        notifications: [],
        unreadCount: 0,
        addNotification: (notification)=>{
            const notifications = get().notifications;
            set({
                notifications: [
                    notification,
                    ...notifications
                ],
                unreadCount: get().unreadCount + 1
            });
        },
        markAsRead: (index)=>{
            const notifications = get().notifications;
            notifications[index] = {
                ...notifications[index]
            };
            set({
                notifications: [
                    ...notifications
                ],
                unreadCount: Math.max(0, get().unreadCount - 1)
            });
        },
        clearNotifications: ()=>set({
                notifications: [],
                unreadCount: 0
            })
    }));
const useUIStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set)=>({
        sidebarOpen: true,
        theme: 'light',
        toggleSidebar: ()=>set((state)=>({
                    sidebarOpen: !state.sidebarOpen
                })),
        setSidebarOpen: (open)=>set({
                sidebarOpen: open
            }),
        setTheme: (theme)=>set({
                theme
            })
    }), {
    name: 'ui-storage'
}));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useSocket.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAppointmentSocket": (()=>useAppointmentSocket),
    "useSocket": (()=>useSocket)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
const useSocket = ()=>{
    _s();
    const [socket, setSocket] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isConnected, setIsConnected] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { user, isAuthenticated } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])();
    const { addNotification } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotificationStore"])();
    const socketRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useSocket.useEffect": ()=>{
            if (!isAuthenticated || !user) {
                return;
            }
            // Get token from cookies or localStorage
            const getToken = {
                "useSocket.useEffect.getToken": ()=>{
                    // Check if we're on the client side
                    if ("TURBOPACK compile-time falsy", 0) {
                        "TURBOPACK unreachable";
                    }
                    // In a real app, you might get this from a secure cookie
                    // For now, we'll assume it's available in the auth store or make an API call
                    return localStorage.getItem('token') || '';
                }
            }["useSocket.useEffect.getToken"];
            const token = getToken();
            if (!token) {
                console.warn('No token available for Socket.IO connection');
                return;
            }
            // Initialize socket connection
            const socketInstance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["io"])(("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 'http://localhost:3000', {
                path: '/api/socket',
                auth: {
                    token
                },
                transports: [
                    'websocket',
                    'polling'
                ]
            });
            socketRef.current = socketInstance;
            setSocket(socketInstance);
            // Connection event handlers
            socketInstance.on('connect', {
                "useSocket.useEffect": ()=>{
                    console.log('Connected to Socket.IO server');
                    setIsConnected(true);
                }
            }["useSocket.useEffect"]);
            socketInstance.on('disconnect', {
                "useSocket.useEffect": (reason)=>{
                    console.log('Disconnected from Socket.IO server:', reason);
                    setIsConnected(false);
                }
            }["useSocket.useEffect"]);
            socketInstance.on('connect_error', {
                "useSocket.useEffect": (error)=>{
                    console.error('Socket.IO connection error:', error);
                    setIsConnected(false);
                }
            }["useSocket.useEffect"]);
            // Welcome message
            socketInstance.on('connected', {
                "useSocket.useEffect": (data)=>{
                    console.log('Socket.IO welcome message:', data);
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Connected to real-time updates');
                }
            }["useSocket.useEffect"]);
            // Notification handler
            socketInstance.on('notification', {
                "useSocket.useEffect": (notification)=>{
                    console.log('Received notification:', notification);
                    // Add to notification store
                    addNotification(notification);
                    // Show toast notification
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info(notification.message, {
                        duration: 5000,
                        action: {
                            label: 'View',
                            onClick: {
                                "useSocket.useEffect": ()=>{
                                    // Navigate to appointment or relevant page
                                    if ("TURBOPACK compile-time truthy", 1) {
                                        window.location.href = `/appointments/${notification.appointmentId}`;
                                    }
                                }
                            }["useSocket.useEffect"]
                        }
                    });
                }
            }["useSocket.useEffect"]);
            // Appointment update handler
            socketInstance.on('appointment_updated', {
                "useSocket.useEffect": (data)=>{
                    console.log('Appointment updated:', data);
                    // You can emit a custom event or update local state here
                    if ("TURBOPACK compile-time truthy", 1) {
                        window.dispatchEvent(new CustomEvent('appointmentUpdated', {
                            detail: data
                        }));
                    }
                }
            }["useSocket.useEffect"]);
            // Typing indicators (for future chat feature)
            socketInstance.on('user_typing', {
                "useSocket.useEffect": (data)=>{
                    console.log('User typing:', data);
                }
            }["useSocket.useEffect"]);
            socketInstance.on('user_stopped_typing', {
                "useSocket.useEffect": (data)=>{
                    console.log('User stopped typing:', data);
                }
            }["useSocket.useEffect"]);
            // Cleanup on unmount
            return ({
                "useSocket.useEffect": ()=>{
                    if (socketRef.current) {
                        socketRef.current.disconnect();
                        socketRef.current = null;
                    }
                    setSocket(null);
                    setIsConnected(false);
                }
            })["useSocket.useEffect"];
        }
    }["useSocket.useEffect"], [
        isAuthenticated,
        user,
        addNotification
    ]);
    // Helper functions
    const joinAppointment = (appointmentId)=>{
        if (socket) {
            socket.emit('join_appointment', appointmentId);
        }
    };
    const leaveAppointment = (appointmentId)=>{
        if (socket) {
            socket.emit('leave_appointment', appointmentId);
        }
    };
    const emitAppointmentUpdate = (data)=>{
        if (socket) {
            socket.emit('appointment_status_update', data);
        }
    };
    const emitNewAppointment = (data)=>{
        if (socket) {
            socket.emit('new_appointment', data);
        }
    };
    return {
        socket,
        isConnected,
        joinAppointment,
        leaveAppointment,
        emitAppointmentUpdate,
        emitNewAppointment
    };
};
_s(useSocket, "9Wg0U0pRi4tHn30cQSJb6XdmrJM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotificationStore"]
    ];
});
const useAppointmentSocket = (appointmentId)=>{
    _s1();
    const { socket, joinAppointment, leaveAppointment } = useSocket();
    const [appointmentUpdates, setAppointmentUpdates] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useAppointmentSocket.useEffect": ()=>{
            if (socket && appointmentId) {
                joinAppointment(appointmentId);
                const handleAppointmentUpdate = {
                    "useAppointmentSocket.useEffect.handleAppointmentUpdate": (data)=>{
                        if (data.appointmentId === appointmentId) {
                            setAppointmentUpdates({
                                "useAppointmentSocket.useEffect.handleAppointmentUpdate": (prev)=>[
                                        ...prev,
                                        data
                                    ]
                            }["useAppointmentSocket.useEffect.handleAppointmentUpdate"]);
                        }
                    }
                }["useAppointmentSocket.useEffect.handleAppointmentUpdate"];
                socket.on('appointment_updated', handleAppointmentUpdate);
                return ({
                    "useAppointmentSocket.useEffect": ()=>{
                        leaveAppointment(appointmentId);
                        socket.off('appointment_updated', handleAppointmentUpdate);
                    }
                })["useAppointmentSocket.useEffect"];
            }
        }
    }["useAppointmentSocket.useEffect"], [
        socket,
        appointmentId,
        joinAppointment,
        leaveAppointment
    ]);
    return {
        appointmentUpdates
    };
};
_s1(useAppointmentSocket, "5h8y4G6UAxJ/dXooiNX5Jw2dZdI=", false, function() {
    return [
        useSocket
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/providers/SocketProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SocketProvider),
    "useSocketContext": (()=>useSocketContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useSocket$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useSocket.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const SocketContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useSocketContext = ()=>{
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(SocketContext);
    if (context === undefined) {
        throw new Error('useSocketContext must be used within a SocketProvider');
    }
    return context;
};
_s(useSocketContext, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function SocketProvider({ children }) {
    _s1();
    const socketData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useSocket$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSocket"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SocketContext.Provider, {
        value: socketData,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/providers/SocketProvider.tsx",
        lineNumber: 44,
        columnNumber: 5
    }, this);
}
_s1(SocketProvider, "CmoKYCG4EI/q3LJBWJfcUSv9j04=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useSocket$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSocket"]
    ];
});
_c = SocketProvider;
var _c;
__turbopack_context__.k.register(_c, "SocketProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/providers/AuthProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function useAuth() {
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
_s(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function AuthProvider({ children }) {
    _s1();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    // Check if user is authenticated on app load
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            checkAuthStatus();
        }
    }["AuthProvider.useEffect"], []);
    const checkAuthStatus = async ()=>{
        try {
            // Check if we're on the client side
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            const token = localStorage.getItem('authToken');
            console.log('AuthProvider: Checking auth status, localStorage token exists:', !!token);
            // Try to authenticate with localStorage token first
            let response;
            if (token) {
                response = await fetch('/api/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
            } else {
                // If no localStorage token, try with cookies (server-side token)
                console.log('AuthProvider: No localStorage token, checking server-side cookie');
                response = await fetch('/api/auth/me');
            }
            if (response.ok) {
                const userData = await response.json();
                console.log('AuthProvider: User authenticated:', userData.data.user.email);
                setUser(userData.data.user);
                // If we authenticated via cookie but don't have localStorage token,
                // this means localStorage was cleared but cookie is still valid
                if (!token) {
                    console.log('AuthProvider: Authenticated via cookie but no localStorage token - this explains the redirect issue');
                }
            } else {
                // Authentication failed
                console.log('AuthProvider: Authentication failed, clearing auth state');
                localStorage.removeItem('authToken');
                // Also call logout to clear server-side cookie
                try {
                    await fetch('/api/auth/logout', {
                        method: 'POST',
                        headers: token ? {
                            'Authorization': `Bearer ${token}`
                        } : {}
                    });
                } catch (logoutError) {
                    console.error('Failed to clear server-side auth:', logoutError);
                }
            }
        } catch (error) {
            console.error('Auth check failed:', error);
            localStorage.removeItem('authToken');
        } finally{
            setIsLoading(false);
        }
    };
    const login = async (email, password)=>{
        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email,
                    password
                })
            });
            const data = await response.json();
            if (response.ok) {
                localStorage.setItem('authToken', data.data.token);
                setUser(data.data.user);
                return {
                    success: true
                };
            } else {
                return {
                    success: false,
                    error: data.error || 'Login failed'
                };
            }
        } catch (error) {
            return {
                success: false,
                error: 'Network error. Please try again.'
            };
        }
    };
    const register = async (userData)=>{
        try {
            const response = await fetch('/api/auth/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });
            const data = await response.json();
            if (response.ok) {
                localStorage.setItem('authToken', data.data.token);
                setUser(data.data.user);
                return {
                    success: true
                };
            } else {
                return {
                    success: false,
                    error: data.error || 'Registration failed'
                };
            }
        } catch (error) {
            return {
                success: false,
                error: 'Network error. Please try again.'
            };
        }
    };
    const logout = async ()=>{
        try {
            // Check if we're on the client side
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            // Clear client-side state first
            localStorage.removeItem('authToken');
            setUser(null);
            const token = localStorage.getItem('authToken');
            if (token) {
                await fetch('/api/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
            } else {
                // Call logout API without token to clear server-side cookie
                await fetch('/api/auth/logout', {
                    method: 'POST'
                });
            }
        } catch (error) {
            console.error('Logout error:', error);
        } finally{
            // Ensure client-side state is cleared
            localStorage.removeItem('authToken');
            setUser(null);
            // Force a hard redirect to home page to bypass any middleware issues
            window.location.href = '/';
        }
    };
    const updateUser = (userData)=>{
        if (user) {
            setUser({
                ...user,
                ...userData
            });
        }
    };
    const value = {
        user,
        isLoading,
        isAuthenticated: !!user,
        login,
        register,
        logout,
        updateUser
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/providers/AuthProvider.tsx",
        lineNumber: 222,
        columnNumber: 5
    }, this);
}
_s1(AuthProvider, "8WEfEbosx3NfLBPRVajZSQS3udc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = AuthProvider;
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_86dd2483._.js.map