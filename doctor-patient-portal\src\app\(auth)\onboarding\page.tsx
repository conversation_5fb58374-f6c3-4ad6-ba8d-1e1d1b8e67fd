'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/providers/AuthProvider';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Stethoscope, User, ArrowRight } from 'lucide-react';
import { toast } from 'sonner';

const specialties = [
  'General Practice',
  'Cardiology',
  'Dermatology',
  'Endocrinology',
  'Gastroenterology',
  'Neurology',
  'Oncology',
  'Orthopedics',
  'Pediatrics',
  'Psychiatry',
  'Radiology',
  'Surgery',
  'Urology',
];

export default function OnboardingPage() {
  const [selectedRole, setSelectedRole] = useState<'doctor' | 'patient' | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [doctorData, setDoctorData] = useState({
    specialty: '',
    bio: '',
    experience: '',
    consultationFee: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
  });

  const [patientData, setPatientData] = useState({
    dateOfBirth: '',
    gender: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    emergencyContact: '',
    emergencyPhone: '',
    medicalHistory: '',
    allergies: '',
    currentMedications: '',
  });

  const { user, isLoading: authLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  // Check authentication and redirect if needed
  useEffect(() => {
    if (authLoading) return;

    if (!isAuthenticated || !user) {
      router.push('/login');
      return;
    }

    // Check URL parameters to see if this is a profile setup request
    const urlParams = new URLSearchParams(window.location.search);
    const isSetup = urlParams.get('setup') === 'true';
    const roleParam = urlParams.get('role');

    // Check if user needs profile setup
    // For new users, we should always show the onboarding flow
    // Only redirect to dashboard if they explicitly have a complete profile

    // Set the role based on user's existing role if not already set
    if (user.role && !selectedRole) {
      setSelectedRole(user.role);
    }

    // If this is a setup request, set the role from URL parameter
    if (isSetup && roleParam && (roleParam === 'doctor' || roleParam === 'patient')) {
      setSelectedRole(roleParam);
    }
  }, [authLoading, isAuthenticated, user, router]);

  const handleRoleSelection = (role: 'doctor' | 'patient') => {
    setSelectedRole(role);
  };

  const handleDoctorDataChange = (field: string, value: string) => {
    setDoctorData(prev => ({ ...prev, [field]: value }));
  };

  const handlePatientDataChange = (field: string, value: string) => {
    setPatientData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    if (!selectedRole || !user) return;

    // Validate required fields
    if (selectedRole === 'patient') {
      if (!patientData.dateOfBirth || !patientData.gender || !patientData.phone) {
        toast.error('Please fill in all required fields (Date of Birth, Gender, and Phone Number)');
        return;
      }
    }

    if (selectedRole === 'doctor') {
      if (!doctorData.specialty || !doctorData.experience || !doctorData.consultationFee) {
        toast.error('Please fill in all required fields (Specialty, Experience, and Consultation Fee)');
        return;
      }
    }

    setIsLoading(true);

    try {
      // Update user profile with additional information
      const profileData = {
        userId: user.id,
        role: selectedRole,
        profile: {
          firstName: user.firstName,
          lastName: user.lastName,
          phone: selectedRole === 'patient' ? patientData.phone || user.phone || '' : user.phone || '',
          profileImage: user.profileImage || '',
          ...(selectedRole === 'patient' && {
            dateOfBirth: patientData.dateOfBirth,
            gender: patientData.gender,
            address: patientData.address,
            city: patientData.city,
            state: patientData.state,
            zipCode: patientData.zipCode,
            emergencyContact: patientData.emergencyContact,
            emergencyPhone: patientData.emergencyPhone,
            medicalHistory: patientData.medicalHistory,
            allergies: patientData.allergies,
            currentMedications: patientData.currentMedications,
          }),
        },
        ...(selectedRole === 'doctor' && {
          doctorInfo: {
            specialty: doctorData.specialty,
            bio: doctorData.bio,
            experience: parseInt(doctorData.experience) || 0,
            consultationFee: parseFloat(doctorData.consultationFee) || 0,
            location: {
              address: doctorData.address,
              city: doctorData.city,
              state: doctorData.state,
              zipCode: doctorData.zipCode,
            },
          },
        }),
      };

      console.log('Sending profile data:', profileData);

      const response = await fetch('/api/auth/setup-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError);
          errorData = { error: `HTTP ${response.status}: Failed to setup profile` };
        }

        console.error('Setup profile error:', errorData);
        console.error('Response status:', response.status);
        console.error('Response headers:', Object.fromEntries(response.headers.entries()));

        // Handle specific error cases
        if (response.status === 400 && errorData.error === 'User profile already exists') {
          console.log('✅ User profile already exists! Redirecting to dashboard...');
          toast.success('Welcome back! Redirecting to your dashboard...');

          // Since we know the user exists, redirect based on the selected role or fallback to patient dashboard
          const dashboardPath = selectedRole === 'doctor' ? '/doctor/dashboard' : '/patient/dashboard';
          router.push(dashboardPath);
          return;
        }

        const errorMessage = errorData.error || errorData.message || `HTTP ${response.status}: Failed to setup profile`;
        throw new Error(errorMessage);
      }

      const responseData = await response.json();
      console.log('✅ Profile setup successful:', responseData);

      toast.success('Profile setup completed!');

      // Redirect based on role
      const dashboardPath = selectedRole === 'doctor' ? '/doctor/dashboard' : '/patient/dashboard';
      router.push(dashboardPath);

    } catch (error) {
      console.error('❌ Profile setup error:', error);

      // More detailed error logging
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
        toast.error(`Failed to setup profile: ${error.message}`);
      } else {
        console.error('Unknown error:', error);
        toast.error('Failed to setup profile. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (authLoading || !isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 px-4 py-12">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome, {user.firstName}!
          </h1>
          <p className="text-gray-600">
            Let's set up your profile to get started
          </p>
        </div>

        {!selectedRole ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card 
                className="cursor-pointer border-2 hover:border-blue-500 transition-colors"
                onClick={() => handleRoleSelection('patient')}
              >
                <CardHeader className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <User className="w-8 h-8 text-green-600" />
                  </div>
                  <CardTitle>I'm a Patient</CardTitle>
                  <CardDescription>
                    Looking for healthcare services and want to book appointments with doctors
                  </CardDescription>
                </CardHeader>
              </Card>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card 
                className="cursor-pointer border-2 hover:border-blue-500 transition-colors"
                onClick={() => handleRoleSelection('doctor')}
              >
                <CardHeader className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Stethoscope className="w-8 h-8 text-blue-600" />
                  </div>
                  <CardTitle>I'm a Doctor</CardTitle>
                  <CardDescription>
                    Healthcare professional wanting to manage appointments and connect with patients
                  </CardDescription>
                </CardHeader>
              </Card>
            </motion.div>
          </div>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>
                {selectedRole === 'doctor' ? 'Doctor Profile Setup' : 'Patient Profile Setup'}
              </CardTitle>
              <CardDescription>
                {selectedRole === 'doctor'
                  ? 'Please provide your professional information'
                  : 'Please provide your personal and medical information to help us serve you better.'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {selectedRole === 'patient' && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="dateOfBirth">Date of Birth <span className="text-red-500">*</span></Label>
                      <Input
                        id="dateOfBirth"
                        type="date"
                        value={patientData.dateOfBirth}
                        onChange={(e) => handlePatientDataChange('dateOfBirth', e.target.value)}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="gender">Gender <span className="text-red-500">*</span></Label>
                      <Select
                        value={patientData.gender}
                        onValueChange={(value) => handlePatientDataChange('gender', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select gender" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="male">Male</SelectItem>
                          <SelectItem value="female">Female</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                          <SelectItem value="prefer-not-to-say">Prefer not to say</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="phone">Phone Number <span className="text-red-500">*</span></Label>
                    <Input
                      id="phone"
                      type="tel"
                      placeholder="(*************"
                      value={patientData.phone}
                      onChange={(e) => handlePatientDataChange('phone', e.target.value)}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="address">Address</Label>
                    <Input
                      id="address"
                      placeholder="Enter your home address"
                      value={patientData.address}
                      onChange={(e) => handlePatientDataChange('address', e.target.value)}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        placeholder="City"
                        value={patientData.city}
                        onChange={(e) => handlePatientDataChange('city', e.target.value)}
                      />
                    </div>

                    <div>
                      <Label htmlFor="state">State</Label>
                      <Input
                        id="state"
                        placeholder="State"
                        value={patientData.state}
                        onChange={(e) => handlePatientDataChange('state', e.target.value)}
                      />
                    </div>

                    <div>
                      <Label htmlFor="zipCode">Zip Code</Label>
                      <Input
                        id="zipCode"
                        placeholder="12345"
                        value={patientData.zipCode}
                        onChange={(e) => handlePatientDataChange('zipCode', e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="emergencyContact">Emergency Contact Name</Label>
                      <Input
                        id="emergencyContact"
                        placeholder="Full name"
                        value={patientData.emergencyContact}
                        onChange={(e) => handlePatientDataChange('emergencyContact', e.target.value)}
                      />
                    </div>

                    <div>
                      <Label htmlFor="emergencyPhone">Emergency Contact Phone</Label>
                      <Input
                        id="emergencyPhone"
                        type="tel"
                        placeholder="(*************"
                        value={patientData.emergencyPhone}
                        onChange={(e) => handlePatientDataChange('emergencyPhone', e.target.value)}
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="medicalHistory">Medical History</Label>
                    <Textarea
                      id="medicalHistory"
                      placeholder="Please describe any significant medical conditions, surgeries, or chronic illnesses (optional)"
                      value={patientData.medicalHistory}
                      onChange={(e) => handlePatientDataChange('medicalHistory', e.target.value)}
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor="allergies">Allergies</Label>
                    <Textarea
                      id="allergies"
                      placeholder="List any known allergies to medications, foods, or other substances (optional)"
                      value={patientData.allergies}
                      onChange={(e) => handlePatientDataChange('allergies', e.target.value)}
                      rows={2}
                    />
                  </div>

                  <div>
                    <Label htmlFor="currentMedications">Current Medications</Label>
                    <Textarea
                      id="currentMedications"
                      placeholder="List any medications you are currently taking (optional)"
                      value={patientData.currentMedications}
                      onChange={(e) => handlePatientDataChange('currentMedications', e.target.value)}
                      rows={2}
                    />
                  </div>
                </>
              )}

              {selectedRole === 'doctor' && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="specialty">Specialty <span className="text-red-500">*</span></Label>
                      <Select
                        value={doctorData.specialty}
                        onValueChange={(value) => handleDoctorDataChange('specialty', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select your specialty" />
                        </SelectTrigger>
                        <SelectContent>
                          {specialties.map((specialty) => (
                            <SelectItem key={specialty} value={specialty}>
                              {specialty}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="experience">Years of Experience <span className="text-red-500">*</span></Label>
                      <Input
                        id="experience"
                        type="number"
                        min="0"
                        placeholder="0"
                        value={doctorData.experience}
                        onChange={(e) => handleDoctorDataChange('experience', e.target.value)}
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="bio">Bio</Label>
                    <Textarea
                      id="bio"
                      placeholder="Tell us about yourself and your experience"
                      value={doctorData.bio}
                      onChange={(e) => handleDoctorDataChange('bio', e.target.value)}
                    />
                  </div>

                  <div>
                    <Label htmlFor="consultationFee">Consultation Fee ($) <span className="text-red-500">*</span></Label>
                    <Input
                      id="consultationFee"
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                      value={doctorData.consultationFee}
                      onChange={(e) => handleDoctorDataChange('consultationFee', e.target.value)}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="address">Clinic Address</Label>
                    <Input
                      id="address"
                      placeholder="Enter your clinic address"
                      value={doctorData.address}
                      onChange={(e) => handleDoctorDataChange('address', e.target.value)}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        placeholder="City"
                        value={doctorData.city}
                        onChange={(e) => handleDoctorDataChange('city', e.target.value)}
                      />
                    </div>

                    <div>
                      <Label htmlFor="state">State</Label>
                      <Input
                        id="state"
                        placeholder="State"
                        value={doctorData.state}
                        onChange={(e) => handleDoctorDataChange('state', e.target.value)}
                      />
                    </div>

                    <div>
                      <Label htmlFor="zipCode">Zip Code</Label>
                      <Input
                        id="zipCode"
                        placeholder="12345"
                        value={doctorData.zipCode}
                        onChange={(e) => handleDoctorDataChange('zipCode', e.target.value)}
                      />
                    </div>
                  </div>
                </>
              )}

              <div className="flex space-x-4 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setSelectedRole(null)}
                  className="flex-1"
                >
                  Back
                </Button>
                <Button
                  onClick={handleSubmit}
                  disabled={isLoading}
                  className="flex-1"
                >
                  {isLoading ? (
                    'Setting up...'
                  ) : (
                    <>
                      Continue
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
