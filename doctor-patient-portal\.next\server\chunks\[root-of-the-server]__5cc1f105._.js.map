{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/lib/db.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/doctor-patient-portal';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\n// Global is used here to maintain a cached connection across hot reloads in development\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached!.conn) {\n    return cached!.conn;\n  }\n\n  if (!cached!.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached!.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Connected to MongoDB');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached!.conn = await cached!.promise;\n  } catch (e) {\n    cached!.promise = null;\n    throw e;\n  }\n\n  return cached!.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAYA,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAQ,IAAI,EAAE;QAChB,OAAO,OAAQ,IAAI;IACrB;IAEA,IAAI,CAAC,OAAQ,OAAO,EAAE;QACpB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAQ,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YAC1D,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAQ,IAAI,GAAG,MAAM,OAAQ,OAAO;IACtC,EAAE,OAAO,GAAG;QACV,OAAQ,OAAO,GAAG;QAClB,MAAM;IACR;IAEA,OAAO,OAAQ,IAAI;AACrB;uCAEe", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/doc%20pa/doctor-patient-portal/src/app/api/test-db/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/db';\nimport mongoose from 'mongoose';\n\nexport async function GET(request: NextRequest) {\n  try {\n    console.log('🔍 Testing database connection...');\n    \n    // Test the connection\n    await connectDB();\n    \n    // Check connection state\n    const connectionState = mongoose.connection.readyState;\n    const states = {\n      0: 'disconnected',\n      1: 'connected',\n      2: 'connecting',\n      3: 'disconnecting'\n    };\n    \n    const dbInfo = {\n      status: 'success',\n      connectionState: states[connectionState as keyof typeof states],\n      dbName: mongoose.connection.db?.databaseName,\n      host: mongoose.connection.host,\n      port: mongoose.connection.port,\n      collections: await mongoose.connection.db?.listCollections().toArray(),\n    };\n    \n    console.log('✅ Database connection test successful:', dbInfo);\n    \n    return NextResponse.json({\n      success: true,\n      message: 'Database connection successful',\n      data: dbInfo,\n    });\n    \n  } catch (error) {\n    console.error('❌ Database connection test failed:', error);\n    \n    return NextResponse.json({\n      success: false,\n      message: 'Database connection failed',\n      error: error instanceof Error ? error.message : 'Unknown error',\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,sBAAsB;QACtB,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,yBAAyB;QACzB,MAAM,kBAAkB,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,UAAU;QACtD,MAAM,SAAS;YACb,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;QACL;QAEA,MAAM,SAAS;YACb,QAAQ;YACR,iBAAiB,MAAM,CAAC,gBAAuC;YAC/D,QAAQ,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,EAAE;YAChC,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,IAAI;YAC9B,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,IAAI;YAC9B,aAAa,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,EAAE,kBAAkB;QAC/D;QAEA,QAAQ,GAAG,CAAC,0CAA0C;QAEtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QAEpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}