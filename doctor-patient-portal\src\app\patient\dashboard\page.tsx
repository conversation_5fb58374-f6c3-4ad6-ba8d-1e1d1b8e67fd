'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/providers/AuthProvider';
import { motion } from 'framer-motion';
import { Calendar, Clock, Users, Plus, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import DashboardLayout from '@/components/layout/DashboardLayout';
import AppointmentCard from '@/components/dashboard/AppointmentCard';
import { AppointmentWithDetails } from '@/types';
import { toast } from 'sonner';

export default function PatientDashboard() {
  const [upcomingAppointments, setUpcomingAppointments] = useState<AppointmentWithDetails[]>([]);
  const [recentAppointments, setRecentAppointments] = useState<AppointmentWithDetails[]>([]);
  const [appointments, setAppointments] = useState<AppointmentWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { user, isLoading: authLoading, isAuthenticated } = useAuth();

  const fetchAppointments = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/appointments?limit=20');
      const result = await response.json();

      if (result.success) {
        const allAppointments = result.data;
        setAppointments(allAppointments);

        // Separate upcoming and recent appointments
        const now = new Date();
        const upcoming = allAppointments.filter((apt: AppointmentWithDetails) => 
          new Date(apt.dateTime) > now && ['pending', 'approved'].includes(apt.status)
        );
        const recent = allAppointments.filter((apt: AppointmentWithDetails) => 
          new Date(apt.dateTime) <= now || ['completed', 'cancelled', 'rejected'].includes(apt.status)
        ).slice(0, 5);

        setUpcomingAppointments(upcoming);
        setRecentAppointments(recent);
      } else {
        toast.error('Failed to fetch appointments');
      }
    } catch (error) {
      toast.error('Failed to fetch appointments');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (appointmentId: string, status: string) => {
    try {
      const response = await fetch(`/api/appointments/${appointmentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      if (response.ok) {
        fetchAppointments(); // Refresh appointments
      } else {
        throw new Error('Failed to update appointment');
      }
    } catch (error) {
      throw error;
    }
  };

  useEffect(() => {
    if (authLoading) return;

    if (!isAuthenticated || !user) {
      router.push('/login');
      return;
    }

    if (user.role !== 'patient') {
      router.push('/dashboard'); // Will redirect to appropriate dashboard
      return;
    }

    fetchAppointments();
  }, [authLoading, isAuthenticated, user, router]);

  // Show loading state while authentication is loading
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Show loading state while user data is being verified
  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Verifying authentication...</p>
        </div>
      </div>
    );
  }

  const stats = [
    {
      title: 'Upcoming Appointments',
      value: upcomingAppointments.length,
      icon: Calendar,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Total Appointments',
      value: appointments.length,
      icon: Clock,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Doctors Consulted',
      value: new Set(appointments.map(apt => apt.doctorId)).size,
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ];

  return (
    <DashboardLayout
      title={`Welcome back, ${user?.firstName || user?.email?.split('@')[0] || 'User'}!`}
      subtitle="Manage your appointments and health records"
    >
      <div className="space-y-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                      <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                    </div>
                    <div className={`p-3 rounded-full ${stat.bgColor}`}>
                      <stat.icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                onClick={() => router.push('/doctors')}
                className="h-16 text-left justify-start bg-blue-600 hover:bg-blue-700"
              >
                <Search className="w-6 h-6 mr-3" />
                <div>
                  <div className="font-semibold">Find Doctors</div>
                  <div className="text-sm opacity-90">Search and book appointments</div>
                </div>
              </Button>
              
              <Button
                onClick={() => router.push('/patient/appointments')}
                variant="outline"
                className="h-16 text-left justify-start"
              >
                <Calendar className="w-6 h-6 mr-3" />
                <div>
                  <div className="font-semibold">View All Appointments</div>
                  <div className="text-sm text-gray-600">Manage your appointments</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Appointments */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Upcoming Appointments</CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/doctors')}
            >
              <Plus className="w-4 h-4 mr-2" />
              Book New
            </Button>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="animate-pulse">
                    <div className="h-32 bg-gray-200 rounded-lg"></div>
                  </div>
                ))}
              </div>
            ) : upcomingAppointments.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No upcoming appointments
                </h3>
                <p className="text-gray-600 mb-4">
                  Book an appointment with a doctor to get started
                </p>
                <Button onClick={() => router.push('/doctors')}>
                  Find Doctors
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {upcomingAppointments.slice(0, 3).map((appointment) => (
                  <AppointmentCard
                    key={appointment._id}
                    appointment={appointment}
                    onStatusUpdate={handleStatusUpdate}
                  />
                ))}
                {upcomingAppointments.length > 3 && (
                  <div className="text-center pt-4">
                    <Button
                      variant="outline"
                      onClick={() => router.push('/patient/appointments')}
                    >
                      View All Appointments
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Appointments */}
        {recentAppointments.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Recent Appointments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentAppointments.map((appointment) => (
                  <AppointmentCard
                    key={appointment._id}
                    appointment={appointment}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}
