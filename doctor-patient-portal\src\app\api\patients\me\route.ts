import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import User from '@/models/User';
import Patient from '@/models/Patient';
import { authenticateUser } from '@/lib/auth';
import { ApiResponse } from '@/types';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate user
    let user;
    try {
      user = await authenticateUser(request);
    } catch (error) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    // Check if user is a patient
    if (user.role !== 'patient') {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Access denied. Only patients can access this endpoint.',
      }, { status: 403 });
    }

    // Fetch patient profile data
    const patient = await Patient.findOne({ userId: user._id });

    if (!patient) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Patient profile not found',
      }, { status: 404 });
    }

    // Return patient profile data
    const patientData = {
      id: patient._id,
      userId: user._id,
      email: user.email,
      firstName: patient.firstName,
      lastName: patient.lastName,
      phone: patient.phone,
      dateOfBirth: patient.dateOfBirth,
      gender: patient.gender,
      address: patient.address,
      city: patient.city,
      state: patient.state,
      zipCode: patient.zipCode,
      emergencyContact: patient.emergencyContact,
      medicalHistory: patient.medicalHistory,
      insurance: patient.insurance,
    };

    return NextResponse.json<ApiResponse>({
      success: true,
      data: patientData,
    });

  } catch (error) {
    console.error('Get patient profile error:', error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to fetch patient profile',
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate user
    let user;
    try {
      user = await authenticateUser(request);
    } catch (error) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    // Check if user is a patient
    if (user.role !== 'patient') {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Access denied. Only patients can access this endpoint.',
      }, { status: 403 });
    }

    const body = await request.json();
    const {
      firstName,
      lastName,
      phone,
      dateOfBirth,
      gender,
      address,
      city,
      state,
      zipCode,
      emergencyContact,
      medicalHistory,
      insurance
    } = body;

    // Update patient profile
    const updatedPatient = await Patient.findOneAndUpdate(
      { userId: user._id },
      {
        $set: {
          firstName,
          lastName,
          phone,
          dateOfBirth,
          gender,
          address,
          city,
          state,
          zipCode,
          emergencyContact,
          medicalHistory,
          insurance,
        }
      },
      { new: true, runValidators: true, upsert: true }
    );

    if (!updatedPatient) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Failed to update patient profile',
      }, { status: 500 });
    }

    const patientData = {
      id: updatedPatient._id,
      userId: user._id,
      email: user.email,
      firstName: updatedPatient.firstName,
      lastName: updatedPatient.lastName,
      phone: updatedPatient.phone,
      dateOfBirth: updatedPatient.dateOfBirth,
      gender: updatedPatient.gender,
      address: updatedPatient.address,
      city: updatedPatient.city,
      state: updatedPatient.state,
      zipCode: updatedPatient.zipCode,
      emergencyContact: updatedPatient.emergencyContact,
      medicalHistory: updatedPatient.medicalHistory,
      insurance: updatedPatient.insurance,
    };

    return NextResponse.json<ApiResponse>({
      success: true,
      data: patientData,
      message: 'Patient profile updated successfully',
    });

  } catch (error) {
    console.error('Update patient profile error:', error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to update patient profile',
    }, { status: 500 });
  }
}
